from django.db import models
from django.core.cache import cache
import uuid


class IconSet(models.Model):
    """图标集模型"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=50, unique=True, verbose_name='图标集名称')
    display_name = models.CharField(max_length=100, verbose_name='显示名称')
    description = models.TextField(blank=True, null=True, verbose_name='描述')
    version = models.CharField(max_length=20, verbose_name='版本')

    # 图标集配置
    base_url = models.URLField(blank=True, null=True, verbose_name='基础URL')
    css_url = models.URLField(blank=True, null=True, verbose_name='CSS文件URL')
    js_url = models.URLField(blank=True, null=True, verbose_name='JS文件URL')

    # 图标集类型
    ICON_TYPES = [
        ('font', '字体图标'),
        ('svg', 'SVG图标'),
        ('image', '图片图标'),
    ]
    icon_type = models.CharField(max_length=20, choices=ICON_TYPES, default='font', verbose_name='图标类型')

    # 前缀配置
    prefix = models.CharField(max_length=20, default='icon-', verbose_name='图标前缀')

    # 状态
    is_active = models.BooleanField(default=True, verbose_name='是否激活')
    is_default = models.BooleanField(default=False, verbose_name='是否默认图标集')

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '图标集'
        verbose_name_plural = '图标集'
        db_table = 'icon_set'

    def __str__(self):
        return self.display_name

    def save(self, *args, **kwargs):
        # 确保只有一个默认图标集
        if self.is_default:
            IconSet.objects.filter(is_default=True).update(is_default=False)
        super().save(*args, **kwargs)
        # 清除缓存
        cache.delete(f'iconset_{self.name}')


class Icon(models.Model):
    """图标模型"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    icon_set = models.ForeignKey(IconSet, on_delete=models.CASCADE, verbose_name='图标集')
    name = models.CharField(max_length=100, verbose_name='图标名称')
    display_name = models.CharField(max_length=100, verbose_name='显示名称')
    description = models.TextField(blank=True, null=True, verbose_name='描述')

    # 图标数据
    icon_class = models.CharField(max_length=100, blank=True, null=True, verbose_name='图标类名')
    svg_content = models.TextField(blank=True, null=True, verbose_name='SVG内容')
    image_url = models.URLField(blank=True, null=True, verbose_name='图片URL')

    # 分类和标签
    category = models.CharField(max_length=50, blank=True, null=True, verbose_name='分类')
    tags = models.JSONField(default=list, blank=True, verbose_name='标签')

    # 样式配置
    default_size = models.CharField(max_length=20, default='16px', verbose_name='默认大小')
    default_color = models.CharField(max_length=7, default='#000000', verbose_name='默认颜色')

    # 状态
    is_active = models.BooleanField(default=True, verbose_name='是否激活')

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '图标'
        verbose_name_plural = '图标'
        db_table = 'icon'
        unique_together = ['icon_set', 'name']

    def __str__(self):
        return f'{self.icon_set.name}:{self.name}'

    def get_full_class(self):
        """获取完整的CSS类名"""
        if self.icon_class:
            return f'{self.icon_set.prefix}{self.icon_class}'
        return f'{self.icon_set.prefix}{self.name}'

    def to_dict(self):
        """转换为字典"""
        return {
            'id': str(self.id),
            'name': self.name,
            'display_name': self.display_name,
            'description': self.description,
            'icon_class': self.get_full_class(),
            'svg_content': self.svg_content,
            'image_url': self.image_url,
            'category': self.category,
            'tags': self.tags,
            'default_size': self.default_size,
            'default_color': self.default_color,
            'icon_set': self.icon_set.name,
        }


class IconUsage(models.Model):
    """图标使用记录"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    icon = models.ForeignKey(Icon, on_delete=models.CASCADE, verbose_name='图标')
    usage_count = models.PositiveIntegerField(default=0, verbose_name='使用次数')
    last_used_at = models.DateTimeField(auto_now=True, verbose_name='最后使用时间')

    # 使用上下文
    context = models.CharField(max_length=100, blank=True, null=True, verbose_name='使用上下文')

    class Meta:
        verbose_name = '图标使用记录'
        verbose_name_plural = '图标使用记录'
        db_table = 'icon_usage'
        unique_together = ['icon', 'context']

    def __str__(self):
        return f'{self.icon.name} - {self.usage_count}次'
