from django.contrib import admin
from django.utils.html import format_html
from .models import Theme, UserThemePreference


@admin.register(Theme)
class ThemeAdmin(admin.ModelAdmin):
    """主题管理"""
    list_display = [
        'display_name', 'name', 'theme_type', 'color_preview',
        'is_active', 'is_default', 'created_by', 'created_at'
    ]
    list_filter = ['theme_type', 'is_active', 'is_default', 'created_at']
    search_fields = ['name', 'display_name', 'description']
    ordering = ['-created_at']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'display_name', 'description', 'theme_type')
        }),
        ('主要颜色', {
            'fields': (
                'primary_color', 'secondary_color', 'success_color',
                'warning_color', 'danger_color', 'info_color'
            )
        }),
        ('背景和文字', {
            'fields': ('background_color', 'text_color', 'border_color')
        }),
        ('导航栏', {
            'fields': ('navbar_bg_color', 'navbar_text_color')
        }),
        ('侧边栏', {
            'fields': ('sidebar_bg_color', 'sidebar_text_color')
        }),
        ('自定义样式', {
            'fields': ('custom_css',),
            'classes': ('collapse',)
        }),
        ('状态', {
            'fields': ('is_active', 'is_default')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at', 'created_by'),
            'classes': ('collapse',)
        }),
    )

    def color_preview(self, obj):
        """颜色预览"""
        return format_html(
            '<div style="display: flex; gap: 5px;">'
            '<div style="width: 20px; height: 20px; background-color: {}; border: 1px solid #ccc;" title="主色调"></div>'
            '<div style="width: 20px; height: 20px; background-color: {}; border: 1px solid #ccc;" title="辅助色"></div>'
            '<div style="width: 20px; height: 20px; background-color: {}; border: 1px solid #ccc;" title="背景色"></div>'
            '</div>',
            obj.primary_color,
            obj.secondary_color,
            obj.background_color
        )
    color_preview.short_description = '颜色预览'

    def save_model(self, request, obj, form, change):
        """保存模型时设置创建者"""
        if not change:  # 新建时
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(UserThemePreference)
class UserThemePreferenceAdmin(admin.ModelAdmin):
    """用户主题偏好管理"""
    list_display = ['user', 'theme', 'created_at', 'updated_at']
    list_filter = ['theme', 'created_at']
    search_fields = ['user__username', 'theme__name']
    ordering = ['-updated_at']
    readonly_fields = ['created_at', 'updated_at']
