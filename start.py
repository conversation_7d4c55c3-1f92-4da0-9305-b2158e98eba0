#!/usr/bin/env python
"""
医疗设备管理系统统一启动脚本
"""
import os
import sys
import subprocess
import argparse
import time
import webbrowser
from pathlib import Path

# 项目根目录
PROJECT_ROOT = Path(__file__).parent
os.chdir(PROJECT_ROOT)

# 添加src目录到Python路径
sys.path.insert(0, str(PROJECT_ROOT / 'src'))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'medical_device_mgmt.settings')

def run_command(command, description, check=True):
    """运行命令并显示结果"""
    print(f"\n{'='*50}")
    print(f"执行: {description}")
    print(f"命令: {command}")
    print('='*50)
    
    try:
        if isinstance(command, list):
            result = subprocess.run(command, check=check, capture_output=True, text=True)
        else:
            result = subprocess.run(command, shell=True, check=check, capture_output=True, text=True)
        
        if result.stdout:
            print("输出:")
            print(result.stdout)
        
        if result.stderr and result.returncode != 0:
            print("错误:")
            print(result.stderr)
        
        print(f"✓ {description} 完成")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"✗ {description} 失败")
        print(f"错误: {e}")
        if e.stdout:
            print(f"输出: {e.stdout}")
        if e.stderr:
            print(f"错误输出: {e.stderr}")
        return False

def check_requirements():
    """检查系统要求"""
    print("检查系统要求...")
    
    # 检查Python版本
    python_version = sys.version_info
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 10):
        print("❌ 错误: 需要Python 3.10或更高版本")
        return False
    
    print(f"✓ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 检查必要的包
    try:
        import django
        print(f"✓ Django版本: {django.get_version()}")
    except ImportError:
        print("❌ Django未安装")
        return False
    
    return True

def install_dependencies():
    """安装依赖"""
    if not Path('requirements.txt').exists():
        print("⚠️ requirements.txt 不存在，跳过依赖安装")
        return True
    
    return run_command(
        [sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'],
        "安装Python依赖",
        check=False
    )

def setup_database():
    """设置数据库"""
    print("\n设置数据库...")
    
    # 检查Django配置
    if not run_command([sys.executable, 'manage.py', 'check'], "检查Django配置"):
        return False
    
    # 创建迁移
    if not run_command([sys.executable, 'manage.py', 'makemigrations'], "创建数据库迁移"):
        return False
    
    # 执行迁移
    if not run_command([sys.executable, 'manage.py', 'migrate'], "执行数据库迁移"):
        return False
    
    return True

def create_default_data():
    """创建默认数据"""
    print("\n创建默认数据...")
    
    # 创建默认主题
    run_command([sys.executable, 'manage.py', 'create_default_themes'], "创建默认主题", check=False)
    
    # 创建默认图标
    run_command([sys.executable, 'manage.py', 'create_default_icons'], "创建默认图标", check=False)
    
    return True

def create_test_data():
    """创建测试数据"""
    print("\n创建测试数据...")
    return run_command([sys.executable, 'manage.py', 'create_test_data'], "创建测试数据", check=False)

def collect_static():
    """收集静态文件"""
    return run_command(
        [sys.executable, 'manage.py', 'collectstatic', '--noinput'],
        "收集静态文件",
        check=False
    )

def run_tests():
    """运行测试"""
    return run_command(
        [sys.executable, 'manage.py', 'test', '--settings=deployment.config.test_settings'],
        "运行单元测试",
        check=False
    )

def start_server(host='127.0.0.1', port='8000', open_browser=True):
    """启动开发服务器"""
    print(f"\n启动开发服务器 http://{host}:{port}/")
    
    # 在后台启动服务器
    server_process = subprocess.Popen([
        sys.executable, 'manage.py', 'runserver', f'{host}:{port}'
    ])
    
    # 等待服务器启动
    time.sleep(3)
    
    if open_browser:
        try:
            webbrowser.open(f'http://{host}:{port}/')
            print(f"✓ 浏览器已打开 http://{host}:{port}/")
        except Exception as e:
            print(f"⚠️ 无法自动打开浏览器: {e}")
    
    print(f"""
{'='*60}
🎉 医疗设备管理系统启动成功！
{'='*60}

📱 访问地址:
   首页: http://{host}:{port}/
   管理后台: http://{host}:{port}/admin/
   API根路径: http://{host}:{port}/api/
   API文档: http://{host}:{port}/api-docs/
   健康检查: http://{host}:{port}/health/

👤 默认账户:
   超级用户: superadmin / admin123456
   测试用户: doctor1 / password123

🔧 管理命令:
   创建超级用户: python manage.py createsuperuser
   创建测试数据: python manage.py create_test_data
   运行测试: python manage.py test

按 Ctrl+C 停止服务器
{'='*60}
    """)
    
    try:
        server_process.wait()
    except KeyboardInterrupt:
        print("\n正在停止服务器...")
        server_process.terminate()
        server_process.wait()
        print("✓ 服务器已停止")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='医疗设备管理系统启动脚本')
    parser.add_argument('--host', default='127.0.0.1', help='服务器主机地址')
    parser.add_argument('--port', default='8000', help='服务器端口')
    parser.add_argument('--no-browser', action='store_true', help='不自动打开浏览器')
    parser.add_argument('--no-deps', action='store_true', help='跳过依赖安装')
    parser.add_argument('--no-migrate', action='store_true', help='跳过数据库迁移')
    parser.add_argument('--no-static', action='store_true', help='跳过静态文件收集')
    parser.add_argument('--no-test-data', action='store_true', help='跳过测试数据创建')
    parser.add_argument('--test', action='store_true', help='运行测试')
    parser.add_argument('--setup-only', action='store_true', help='只进行设置，不启动服务器')
    
    args = parser.parse_args()
    
    print("🏥 医疗设备管理系统启动脚本")
    print("="*50)
    
    # 检查系统要求
    if not check_requirements():
        sys.exit(1)
    
    # 安装依赖
    if not args.no_deps:
        if not install_dependencies():
            print("⚠️ 依赖安装失败，但继续执行")
    
    # 设置数据库
    if not args.no_migrate:
        if not setup_database():
            print("❌ 数据库设置失败")
            sys.exit(1)
    
    # 创建默认数据
    create_default_data()
    
    # 创建测试数据
    if not args.no_test_data:
        create_test_data()
    
    # 收集静态文件
    if not args.no_static:
        collect_static()
    
    # 运行测试
    if args.test:
        if not run_tests():
            print("⚠️ 测试失败，但继续执行")
    
    # 启动服务器
    if not args.setup_only:
        start_server(
            host=args.host,
            port=args.port,
            open_browser=not args.no_browser
        )
    else:
        print("\n✓ 设置完成！")
        print(f"运行 'python manage.py runserver {args.host}:{args.port}' 启动服务器")

if __name__ == '__main__':
    main()
