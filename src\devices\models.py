from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator, MaxValueValidator
import uuid
from decimal import Decimal

User = get_user_model()


class DeviceCategory(models.Model):
    """设备分类模型"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100, unique=True, verbose_name='分类名称')
    code = models.CharField(max_length=20, unique=True, verbose_name='分类代码')
    description = models.TextField(blank=True, null=True, verbose_name='分类描述')
    parent = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='children',
        verbose_name='父分类'
    )

    # 分类属性
    icon = models.CharField(max_length=50, blank=True, null=True, verbose_name='图标')
    color = models.CharField(max_length=7, default='#007bff', verbose_name='颜色')
    sort_order = models.PositiveIntegerField(default=0, verbose_name='排序')

    # 状态
    is_active = models.BooleanField(default=True, verbose_name='是否激活')

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name='创建者'
    )

    class Meta:
        verbose_name = '设备分类'
        verbose_name_plural = '设备分类'
        db_table = 'device_category'
        ordering = ['sort_order', 'name']

    def __str__(self):
        return self.name

    def get_full_path(self):
        """获取完整路径"""
        if self.parent:
            return f"{self.parent.get_full_path()} > {self.name}"
        return self.name


class DeviceManufacturer(models.Model):
    """设备制造商模型"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=200, unique=True, verbose_name='制造商名称')
    code = models.CharField(max_length=50, unique=True, verbose_name='制造商代码')
    description = models.TextField(blank=True, null=True, verbose_name='描述')

    # 联系信息
    contact_person = models.CharField(max_length=100, blank=True, null=True, verbose_name='联系人')
    phone = models.CharField(max_length=20, blank=True, null=True, verbose_name='电话')
    email = models.EmailField(blank=True, null=True, verbose_name='邮箱')
    address = models.TextField(blank=True, null=True, verbose_name='地址')
    website = models.URLField(blank=True, null=True, verbose_name='网站')

    # 状态
    is_active = models.BooleanField(default=True, verbose_name='是否激活')

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '设备制造商'
        verbose_name_plural = '设备制造商'
        db_table = 'device_manufacturer'
        ordering = ['name']

    def __str__(self):
        return self.name


class DeviceModel(models.Model):
    """设备型号模型"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=200, verbose_name='型号名称')
    model_number = models.CharField(max_length=100, verbose_name='型号编号')
    manufacturer = models.ForeignKey(
        DeviceManufacturer,
        on_delete=models.CASCADE,
        verbose_name='制造商'
    )
    category = models.ForeignKey(
        DeviceCategory,
        on_delete=models.CASCADE,
        verbose_name='设备分类'
    )

    # 技术规格
    specifications = models.JSONField(default=dict, blank=True, verbose_name='技术规格')
    description = models.TextField(blank=True, null=True, verbose_name='描述')

    # 图片和文档
    image = models.ImageField(upload_to='device_models/', blank=True, null=True, verbose_name='产品图片')
    manual_url = models.URLField(blank=True, null=True, verbose_name='说明书链接')

    # 价格信息
    purchase_price = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name='采购价格'
    )

    # 状态
    is_active = models.BooleanField(default=True, verbose_name='是否激活')

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '设备型号'
        verbose_name_plural = '设备型号'
        db_table = 'device_model'
        unique_together = ['manufacturer', 'model_number']
        ordering = ['manufacturer__name', 'name']

    def __str__(self):
        return f"{self.manufacturer.name} - {self.name}"


class Device(models.Model):
    """设备主模型"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # 基本信息
    asset_number = models.CharField(max_length=50, unique=True, verbose_name='资产编号')
    serial_number = models.CharField(max_length=100, blank=True, null=True, verbose_name='序列号')
    name = models.CharField(max_length=200, verbose_name='设备名称')
    device_model = models.ForeignKey(
        DeviceModel,
        on_delete=models.CASCADE,
        verbose_name='设备型号'
    )

    # 位置信息
    department = models.CharField(max_length=100, blank=True, null=True, verbose_name='所属科室')
    location = models.CharField(max_length=200, blank=True, null=True, verbose_name='具体位置')
    room = models.CharField(max_length=50, blank=True, null=True, verbose_name='房间号')

    # 责任人
    responsible_person = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='responsible_devices',
        verbose_name='责任人'
    )
    operator = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='operated_devices',
        verbose_name='操作员'
    )

    # 采购信息
    purchase_date = models.DateField(null=True, blank=True, verbose_name='采购日期')
    purchase_price = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name='采购价格'
    )
    supplier = models.CharField(max_length=200, blank=True, null=True, verbose_name='供应商')
    warranty_start_date = models.DateField(null=True, blank=True, verbose_name='保修开始日期')
    warranty_end_date = models.DateField(null=True, blank=True, verbose_name='保修结束日期')

    # 设备状态
    STATUS_CHOICES = [
        ('normal', '正常'),
        ('maintenance', '维护中'),
        ('repair', '维修中'),
        ('fault', '故障'),
        ('retired', '报废'),
        ('idle', '闲置'),
    ]
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='normal',
        verbose_name='设备状态'
    )

    # 使用状态
    USAGE_STATUS_CHOICES = [
        ('in_use', '使用中'),
        ('available', '可用'),
        ('unavailable', '不可用'),
        ('reserved', '预约中'),
    ]
    usage_status = models.CharField(
        max_length=20,
        choices=USAGE_STATUS_CHOICES,
        default='available',
        verbose_name='使用状态'
    )

    # 重要程度
    IMPORTANCE_CHOICES = [
        ('critical', '关键'),
        ('important', '重要'),
        ('normal', '一般'),
        ('low', '较低'),
    ]
    importance = models.CharField(
        max_length=20,
        choices=IMPORTANCE_CHOICES,
        default='normal',
        verbose_name='重要程度'
    )

    # 设备参数
    parameters = models.JSONField(default=dict, blank=True, verbose_name='设备参数')
    notes = models.TextField(blank=True, null=True, verbose_name='备注')

    # 最后检查信息
    last_check_date = models.DateTimeField(null=True, blank=True, verbose_name='最后检查时间')
    last_check_result = models.TextField(blank=True, null=True, verbose_name='最后检查结果')
    next_check_date = models.DateField(null=True, blank=True, verbose_name='下次检查日期')

    # 状态
    is_active = models.BooleanField(default=True, verbose_name='是否激活')

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='created_devices',
        verbose_name='创建者'
    )

    class Meta:
        verbose_name = '医疗设备'
        verbose_name_plural = '医疗设备'
        db_table = 'device'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.asset_number} - {self.name}"

    @property
    def is_under_warranty(self):
        """是否在保修期内"""
        if self.warranty_end_date:
            from django.utils import timezone
            return timezone.now().date() <= self.warranty_end_date
        return False

    @property
    def warranty_days_left(self):
        """保修剩余天数"""
        if self.warranty_end_date:
            from django.utils import timezone
            delta = self.warranty_end_date - timezone.now().date()
            return delta.days if delta.days > 0 else 0
        return 0

    def get_status_display_color(self):
        """获取状态显示颜色"""
        status_colors = {
            'normal': '#28a745',
            'maintenance': '#ffc107',
            'repair': '#fd7e14',
            'fault': '#dc3545',
            'retired': '#6c757d',
            'idle': '#17a2b8',
        }
        return status_colors.get(self.status, '#6c757d')


class DeviceStatusHistory(models.Model):
    """设备状态历史记录"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    device = models.ForeignKey(
        Device,
        on_delete=models.CASCADE,
        related_name='status_history',
        verbose_name='设备'
    )

    # 状态变更信息
    old_status = models.CharField(max_length=20, verbose_name='原状态')
    new_status = models.CharField(max_length=20, verbose_name='新状态')
    old_usage_status = models.CharField(max_length=20, blank=True, null=True, verbose_name='原使用状态')
    new_usage_status = models.CharField(max_length=20, blank=True, null=True, verbose_name='新使用状态')

    # 变更原因和描述
    reason = models.CharField(max_length=200, verbose_name='变更原因')
    description = models.TextField(blank=True, null=True, verbose_name='详细描述')

    # 操作人员
    changed_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        verbose_name='操作人员'
    )

    # 时间戳
    changed_at = models.DateTimeField(auto_now_add=True, verbose_name='变更时间')

    class Meta:
        verbose_name = '设备状态历史'
        verbose_name_plural = '设备状态历史'
        db_table = 'device_status_history'
        ordering = ['-changed_at']

    def __str__(self):
        return f"{self.device.asset_number} - {self.old_status} → {self.new_status}"


class DeviceAttachment(models.Model):
    """设备附件模型"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    device = models.ForeignKey(
        Device,
        on_delete=models.CASCADE,
        related_name='attachments',
        verbose_name='设备'
    )

    # 附件信息
    name = models.CharField(max_length=200, verbose_name='附件名称')
    description = models.TextField(blank=True, null=True, verbose_name='描述')
    file = models.FileField(upload_to='device_attachments/', verbose_name='文件')
    file_size = models.PositiveIntegerField(null=True, blank=True, verbose_name='文件大小')

    # 附件类型
    ATTACHMENT_TYPES = [
        ('manual', '使用手册'),
        ('certificate', '合格证书'),
        ('warranty', '保修单'),
        ('photo', '照片'),
        ('report', '检测报告'),
        ('other', '其他'),
    ]
    attachment_type = models.CharField(
        max_length=20,
        choices=ATTACHMENT_TYPES,
        default='other',
        verbose_name='附件类型'
    )

    # 上传信息
    uploaded_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        verbose_name='上传者'
    )
    uploaded_at = models.DateTimeField(auto_now_add=True, verbose_name='上传时间')

    class Meta:
        verbose_name = '设备附件'
        verbose_name_plural = '设备附件'
        db_table = 'device_attachment'
        ordering = ['-uploaded_at']

    def __str__(self):
        return f"{self.device.asset_number} - {self.name}"

    def save(self, *args, **kwargs):
        if self.file:
            self.file_size = self.file.size
        super().save(*args, **kwargs)


class DeviceLocation(models.Model):
    """设备位置模型"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100, unique=True, verbose_name='位置名称')
    code = models.CharField(max_length=20, unique=True, verbose_name='位置代码')
    description = models.TextField(blank=True, null=True, verbose_name='描述')

    # 层级结构
    parent = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='children',
        verbose_name='父位置'
    )

    # 位置类型
    LOCATION_TYPES = [
        ('building', '建筑'),
        ('floor', '楼层'),
        ('department', '科室'),
        ('room', '房间'),
        ('area', '区域'),
    ]
    location_type = models.CharField(
        max_length=20,
        choices=LOCATION_TYPES,
        default='room',
        verbose_name='位置类型'
    )

    # 负责人
    manager = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name='负责人'
    )

    # 状态
    is_active = models.BooleanField(default=True, verbose_name='是否激活')

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '设备位置'
        verbose_name_plural = '设备位置'
        db_table = 'device_location'
        ordering = ['name']

    def __str__(self):
        return self.name

    def get_full_path(self):
        """获取完整路径"""
        if self.parent:
            return f"{self.parent.get_full_path()} > {self.name}"
        return self.name
