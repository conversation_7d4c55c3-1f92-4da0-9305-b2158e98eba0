from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta
import uuid

User = get_user_model()


class MaintenanceType(models.Model):
    """维护类型模型"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100, unique=True, verbose_name='维护类型名称')
    code = models.CharField(max_length=20, unique=True, verbose_name='类型代码')
    description = models.TextField(blank=True, null=True, verbose_name='描述')

    # 默认配置
    default_duration = models.PositiveIntegerField(default=60, verbose_name='默认时长(分钟)')
    default_interval_days = models.PositiveIntegerField(default=30, verbose_name='默认间隔(天)')

    # 状态
    is_active = models.BooleanField(default=True, verbose_name='是否激活')

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '维护类型'
        verbose_name_plural = '维护类型'
        db_table = 'maintenance_type'
        ordering = ['name']

    def __str__(self):
        return self.name


class MaintenancePlan(models.Model):
    """维护计划模型"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    device = models.ForeignKey(
        'devices.Device',
        on_delete=models.CASCADE,
        related_name='maintenance_plans',
        verbose_name='设备'
    )
    maintenance_type = models.ForeignKey(
        MaintenanceType,
        on_delete=models.CASCADE,
        verbose_name='维护类型'
    )

    # 计划信息
    title = models.CharField(max_length=200, verbose_name='计划标题')
    description = models.TextField(blank=True, null=True, verbose_name='计划描述')

    # 时间配置
    start_date = models.DateField(verbose_name='开始日期')
    interval_days = models.PositiveIntegerField(verbose_name='间隔天数')
    estimated_duration = models.PositiveIntegerField(verbose_name='预计时长(分钟)')

    # 下次维护
    next_maintenance_date = models.DateField(verbose_name='下次维护日期')

    # 责任人
    assigned_to = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='assigned_maintenance_plans',
        verbose_name='负责人'
    )

    # 优先级
    PRIORITY_CHOICES = [
        ('low', '低'),
        ('normal', '普通'),
        ('high', '高'),
        ('urgent', '紧急'),
    ]
    priority = models.CharField(
        max_length=20,
        choices=PRIORITY_CHOICES,
        default='normal',
        verbose_name='优先级'
    )

    # 状态
    is_active = models.BooleanField(default=True, verbose_name='是否激活')

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='created_maintenance_plans',
        verbose_name='创建者'
    )

    class Meta:
        verbose_name = '维护计划'
        verbose_name_plural = '维护计划'
        db_table = 'maintenance_plan'
        ordering = ['next_maintenance_date']

    def __str__(self):
        return f"{self.device.asset_number} - {self.title}"

    def update_next_maintenance_date(self):
        """更新下次维护日期"""
        self.next_maintenance_date = self.next_maintenance_date + timedelta(days=self.interval_days)
        self.save(update_fields=['next_maintenance_date'])

    @property
    def is_overdue(self):
        """是否逾期"""
        return timezone.now().date() > self.next_maintenance_date

    @property
    def days_until_maintenance(self):
        """距离维护天数"""
        delta = self.next_maintenance_date - timezone.now().date()
        return delta.days


class MaintenanceRecord(models.Model):
    """维护记录模型"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    device = models.ForeignKey(
        'devices.Device',
        on_delete=models.CASCADE,
        related_name='maintenance_records',
        verbose_name='设备'
    )
    maintenance_plan = models.ForeignKey(
        MaintenancePlan,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='maintenance_records',
        verbose_name='维护计划'
    )
    maintenance_type = models.ForeignKey(
        MaintenanceType,
        on_delete=models.CASCADE,
        verbose_name='维护类型'
    )

    # 维护信息
    title = models.CharField(max_length=200, verbose_name='维护标题')
    description = models.TextField(verbose_name='维护描述')

    # 时间信息
    scheduled_date = models.DateTimeField(verbose_name='计划时间')
    start_time = models.DateTimeField(null=True, blank=True, verbose_name='开始时间')
    end_time = models.DateTimeField(null=True, blank=True, verbose_name='结束时间')
    actual_duration = models.PositiveIntegerField(null=True, blank=True, verbose_name='实际时长(分钟)')

    # 执行人员
    performed_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name='performed_maintenances',
        verbose_name='执行人'
    )

    # 维护状态
    STATUS_CHOICES = [
        ('scheduled', '已计划'),
        ('in_progress', '进行中'),
        ('completed', '已完成'),
        ('cancelled', '已取消'),
        ('failed', '失败'),
    ]
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='scheduled',
        verbose_name='状态'
    )

    # 维护结果
    result = models.TextField(blank=True, null=True, verbose_name='维护结果')
    issues_found = models.TextField(blank=True, null=True, verbose_name='发现问题')
    parts_replaced = models.JSONField(default=list, blank=True, verbose_name='更换部件')
    cost = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name='维护成本'
    )

    # 评级
    RATING_CHOICES = [
        (1, '很差'),
        (2, '差'),
        (3, '一般'),
        (4, '好'),
        (5, '很好'),
    ]
    rating = models.PositiveSmallIntegerField(
        choices=RATING_CHOICES,
        null=True,
        blank=True,
        verbose_name='维护评级'
    )

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='created_maintenance_records',
        verbose_name='创建者'
    )

    class Meta:
        verbose_name = '维护记录'
        verbose_name_plural = '维护记录'
        db_table = 'maintenance_record'
        ordering = ['-scheduled_date']

    def __str__(self):
        return f"{self.device.asset_number} - {self.title}"

    def save(self, *args, **kwargs):
        # 自动计算实际时长
        if self.start_time and self.end_time:
            duration = self.end_time - self.start_time
            self.actual_duration = int(duration.total_seconds() / 60)
        super().save(*args, **kwargs)

    @property
    def duration_display(self):
        """时长显示"""
        if self.actual_duration:
            hours = self.actual_duration // 60
            minutes = self.actual_duration % 60
            if hours > 0:
                return f"{hours}小时{minutes}分钟"
            return f"{minutes}分钟"
        return "未记录"


class FaultReport(models.Model):
    """故障报告模型"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    device = models.ForeignKey(
        'devices.Device',
        on_delete=models.CASCADE,
        related_name='fault_reports',
        verbose_name='设备'
    )

    # 故障信息
    title = models.CharField(max_length=200, verbose_name='故障标题')
    description = models.TextField(verbose_name='故障描述')
    fault_code = models.CharField(max_length=50, blank=True, null=True, verbose_name='故障代码')

    # 严重程度
    SEVERITY_CHOICES = [
        ('low', '轻微'),
        ('medium', '中等'),
        ('high', '严重'),
        ('critical', '致命'),
    ]
    severity = models.CharField(
        max_length=20,
        choices=SEVERITY_CHOICES,
        default='medium',
        verbose_name='严重程度'
    )

    # 故障类型
    FAULT_TYPE_CHOICES = [
        ('mechanical', '机械故障'),
        ('electrical', '电气故障'),
        ('software', '软件故障'),
        ('calibration', '校准问题'),
        ('wear', '磨损'),
        ('other', '其他'),
    ]
    fault_type = models.CharField(
        max_length=20,
        choices=FAULT_TYPE_CHOICES,
        default='other',
        verbose_name='故障类型'
    )

    # 时间信息
    occurred_at = models.DateTimeField(verbose_name='发生时间')
    reported_at = models.DateTimeField(auto_now_add=True, verbose_name='报告时间')

    # 报告人
    reported_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name='reported_faults',
        verbose_name='报告人'
    )

    # 处理状态
    STATUS_CHOICES = [
        ('open', '待处理'),
        ('assigned', '已分配'),
        ('in_progress', '处理中'),
        ('resolved', '已解决'),
        ('closed', '已关闭'),
    ]
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='open',
        verbose_name='处理状态'
    )

    # 分配给
    assigned_to = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='assigned_faults',
        verbose_name='分配给'
    )

    # 解决信息
    resolution = models.TextField(blank=True, null=True, verbose_name='解决方案')
    resolved_at = models.DateTimeField(null=True, blank=True, verbose_name='解决时间')
    resolved_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='resolved_faults',
        verbose_name='解决人'
    )

    # 关联维护记录
    maintenance_record = models.ForeignKey(
        MaintenanceRecord,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='related_faults',
        verbose_name='关联维护记录'
    )

    class Meta:
        verbose_name = '故障报告'
        verbose_name_plural = '故障报告'
        db_table = 'fault_report'
        ordering = ['-occurred_at']

    def __str__(self):
        return f"{self.device.asset_number} - {self.title}"

    @property
    def resolution_time(self):
        """解决时长"""
        if self.resolved_at and self.reported_at:
            delta = self.resolved_at - self.reported_at
            return delta
        return None
