"""
三层令牌认证系统
"""
import jwt
from datetime import datetime, timedelta
from django.conf import settings
from django.contrib.auth import get_user_model
from django.utils import timezone
from rest_framework import authentication, exceptions
from .models import AccessToken, RefreshToken, DeviceToken

User = get_user_model()


class TokenManager:
    """令牌管理器"""
    
    @staticmethod
    def generate_access_token(user, device_token=None):
        """生成访问令牌"""
        payload = {
            'user_id': str(user.id),
            'username': user.username,
            'exp': timezone.now() + timedelta(
                minutes=settings.TOKEN_SETTINGS.get('ACCESS_TOKEN_LIFETIME', 15)
            ),
            'iat': timezone.now(),
            'type': 'access'
        }
        
        if device_token:
            payload['device_id'] = device_token.device_id
        
        token = jwt.encode(
            payload,
            settings.TOKEN_SETTINGS.get('SIGNING_KEY'),
            algorithm=settings.TOKEN_SETTINGS.get('ALGORITHM', 'HS256')
        )
        
        # 保存到数据库
        access_token = AccessToken.objects.create(
            user=user,
            token=token
        )
        
        return token
    
    @staticmethod
    def generate_refresh_token(user):
        """生成刷新令牌"""
        payload = {
            'user_id': str(user.id),
            'exp': timezone.now() + timedelta(
                days=settings.TOKEN_SETTINGS.get('REFRESH_TOKEN_LIFETIME', 7)
            ),
            'iat': timezone.now(),
            'type': 'refresh'
        }
        
        token = jwt.encode(
            payload,
            settings.TOKEN_SETTINGS.get('SIGNING_KEY'),
            algorithm=settings.TOKEN_SETTINGS.get('ALGORITHM', 'HS256')
        )
        
        # 保存到数据库
        refresh_token = RefreshToken.objects.create(
            user=user,
            token=token
        )
        
        return token
    
    @staticmethod
    def generate_device_token(user, device_id, device_name, device_type, ip_address=None, user_agent=None):
        """生成设备令牌"""
        payload = {
            'user_id': str(user.id),
            'device_id': device_id,
            'exp': timezone.now() + timedelta(
                days=settings.TOKEN_SETTINGS.get('DEVICE_TOKEN_LIFETIME', 30)
            ),
            'iat': timezone.now(),
            'type': 'device'
        }
        
        token = jwt.encode(
            payload,
            settings.TOKEN_SETTINGS.get('SIGNING_KEY'),
            algorithm=settings.TOKEN_SETTINGS.get('ALGORITHM', 'HS256')
        )
        
        # 保存到数据库，如果设备已存在则更新
        device_token, created = DeviceToken.objects.update_or_create(
            user=user,
            device_id=device_id,
            defaults={
                'device_name': device_name,
                'device_type': device_type,
                'token': token,
                'ip_address': ip_address,
                'user_agent': user_agent,
                'is_active': True,
                'expires_at': timezone.now() + timedelta(
                    days=settings.TOKEN_SETTINGS.get('DEVICE_TOKEN_LIFETIME', 30)
                )
            }
        )
        
        return token, device_token
    
    @staticmethod
    def verify_token(token, token_type='access'):
        """验证令牌"""
        try:
            payload = jwt.decode(
                token,
                settings.TOKEN_SETTINGS.get('SIGNING_KEY'),
                algorithms=[settings.TOKEN_SETTINGS.get('ALGORITHM', 'HS256')]
            )
            
            if payload.get('type') != token_type:
                raise exceptions.AuthenticationFailed('令牌类型不匹配')
            
            user_id = payload.get('user_id')
            if not user_id:
                raise exceptions.AuthenticationFailed('令牌无效')
            
            try:
                user = User.objects.get(id=user_id, is_active=True)
            except User.DoesNotExist:
                raise exceptions.AuthenticationFailed('用户不存在或已被禁用')
            
            return user, payload
            
        except jwt.ExpiredSignatureError:
            raise exceptions.AuthenticationFailed('令牌已过期')
        except jwt.InvalidTokenError:
            raise exceptions.AuthenticationFailed('令牌无效')


class TripleTokenAuthentication(authentication.BaseAuthentication):
    """三层令牌认证"""
    
    def authenticate(self, request):
        """认证方法"""
        # 从请求头获取令牌
        auth_header = request.META.get('HTTP_AUTHORIZATION')
        if not auth_header or not auth_header.startswith('Bearer '):
            return None
        
        token = auth_header.split(' ')[1]
        
        try:
            # 首先尝试验证访问令牌
            user, payload = TokenManager.verify_token(token, 'access')
            
            # 检查数据库中的令牌是否有效
            try:
                access_token = AccessToken.objects.get(
                    token=token,
                    user=user,
                    is_active=True
                )
                if access_token.is_expired:
                    access_token.is_active = False
                    access_token.save()
                    raise exceptions.AuthenticationFailed('访问令牌已过期')
                
            except AccessToken.DoesNotExist:
                raise exceptions.AuthenticationFailed('访问令牌无效')
            
            # 如果有设备ID，验证设备令牌
            device_id = payload.get('device_id')
            if device_id:
                try:
                    device_token = DeviceToken.objects.get(
                        user=user,
                        device_id=device_id,
                        is_active=True
                    )
                    if device_token.is_expired:
                        device_token.is_active = False
                        device_token.save()
                        raise exceptions.AuthenticationFailed('设备令牌已过期')
                    
                    # 更新设备最后使用时间
                    device_token.update_last_used()
                    
                except DeviceToken.DoesNotExist:
                    raise exceptions.AuthenticationFailed('设备令牌无效')
            
            return (user, token)
            
        except exceptions.AuthenticationFailed:
            # 如果访问令牌验证失败，尝试刷新令牌
            try:
                user, payload = TokenManager.verify_token(token, 'refresh')
                
                # 检查数据库中的刷新令牌是否有效
                try:
                    refresh_token = RefreshToken.objects.get(
                        token=token,
                        user=user,
                        is_active=True
                    )
                    if refresh_token.is_expired:
                        refresh_token.is_active = False
                        refresh_token.save()
                        raise exceptions.AuthenticationFailed('刷新令牌已过期')
                    
                    # 标记请求需要刷新访问令牌
                    request.needs_token_refresh = True
                    return (user, token)
                    
                except RefreshToken.DoesNotExist:
                    raise exceptions.AuthenticationFailed('刷新令牌无效')
                    
            except exceptions.AuthenticationFailed:
                return None
    
    def authenticate_header(self, request):
        """返回认证头"""
        return 'Bearer'


class DeviceTokenAuthentication(authentication.BaseAuthentication):
    """设备令牌认证（用于长期设备访问）"""
    
    def authenticate(self, request):
        """认证方法"""
        # 从请求头获取设备令牌
        device_token = request.META.get('HTTP_X_DEVICE_TOKEN')
        if not device_token:
            return None
        
        try:
            user, payload = TokenManager.verify_token(device_token, 'device')
            
            # 检查数据库中的设备令牌
            device_id = payload.get('device_id')
            if not device_id:
                raise exceptions.AuthenticationFailed('设备令牌无效')
            
            try:
                device_token_obj = DeviceToken.objects.get(
                    token=device_token,
                    user=user,
                    device_id=device_id,
                    is_active=True
                )
                if device_token_obj.is_expired:
                    device_token_obj.is_active = False
                    device_token_obj.save()
                    raise exceptions.AuthenticationFailed('设备令牌已过期')
                
                # 更新设备最后使用时间
                device_token_obj.update_last_used()
                
                return (user, device_token)
                
            except DeviceToken.DoesNotExist:
                raise exceptions.AuthenticationFailed('设备令牌无效')
                
        except exceptions.AuthenticationFailed:
            return None
    
    def authenticate_header(self, request):
        """返回认证头"""
        return 'X-Device-Token'
