"""
主题模块URL配置
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

app_name = 'themes'

router = DefaultRouter()
router.register(r'themes', views.ThemeViewSet)

urlpatterns = [
    # REST API 路由
    path('', include(router.urls)),

    # 用户主题偏好
    path('preference/', views.ThemePreferenceView.as_view(), name='theme_preference'),

    # 主题CSS
    path('css/', views.ThemeCSSView.as_view(), name='theme_css'),

    # 默认主题
    path('default/', views.DefaultThemeView.as_view(), name='default_theme'),
]
