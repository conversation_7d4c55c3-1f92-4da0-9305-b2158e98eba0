<template>
  <el-container class="layout-container">
    <!-- 侧边栏 -->
    <el-aside :width="sidebarWidth" class="sidebar">
      <Sidebar />
    </el-aside>
    
    <!-- 主内容区 -->
    <el-container class="main-container">
      <!-- 顶部导航栏 -->
      <el-header class="header">
        <Navbar />
      </el-header>
      
      <!-- 标签页导航 -->
      <div class="tabs-container" v-if="showTabs">
        <TabsView />
      </div>
      
      <!-- 主内容 -->
      <el-main class="main-content">
        <router-view v-slot="{ Component, route }">
          <transition name="fade-transform" mode="out-in">
            <keep-alive :include="cachedViews">
              <component :is="Component" :key="route.path" />
            </keep-alive>
          </transition>
        </router-view>
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup>
import { computed } from 'vue'
// import { useAppStore } from '@/stores/app'

// 临时使用本地状态，稍后创建 app store
const sidebarCollapsed = ref(false)
const showTabs = ref(true)
const cachedViews = ref([])
import Sidebar from './components/Sidebar.vue'
import Navbar from './components/Navbar.vue'
import TabsView from './components/TabsView.vue'

// const appStore = useAppStore()

// 计算属性
const sidebarWidth = computed(() => {
  return sidebarCollapsed.value ? '64px' : '200px'
})
</script>

<style lang="scss" scoped>
.layout-container {
  height: 100vh;
  
  .sidebar {
    transition: width 0.3s ease;
    border-right: 1px solid var(--el-border-color-light);
    background-color: var(--el-bg-color);
  }
  
  .main-container {
    .header {
      height: 60px;
      padding: 0;
      border-bottom: 1px solid var(--el-border-color-light);
      background-color: var(--el-bg-color);
    }
    
    .tabs-container {
      height: 40px;
      border-bottom: 1px solid var(--el-border-color-light);
      background-color: var(--el-bg-color);
    }
    
    .main-content {
      padding: 20px;
      background-color: var(--el-bg-color-page);
      overflow-y: auto;
    }
  }
}

// 页面切换动画
.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all 0.3s;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}
</style>
