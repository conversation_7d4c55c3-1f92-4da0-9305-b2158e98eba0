<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>医疗设备管理系统 - 现代化界面演示</title>
    
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .demo-header {
            background: linear-gradient(135deg, #409EFF 0%, #36CFC9 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }
        
        .demo-title {
            font-size: 32px;
            font-weight: 600;
            margin-bottom: 16px;
        }
        
        .demo-subtitle {
            font-size: 16px;
            opacity: 0.9;
        }
        
        .demo-content {
            padding: 40px;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .feature-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 30px;
            text-align: center;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .feature-icon {
            font-size: 48px;
            margin-bottom: 20px;
            color: #409EFF;
        }
        
        .feature-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 12px;
            color: #303133;
        }
        
        .feature-desc {
            font-size: 14px;
            color: #606266;
            line-height: 1.6;
        }
        
        .stats-preview {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            padding: 30px;
            color: white;
            margin-bottom: 40px;
        }
        
        .stats-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .stat-item {
            text-align: center;
            padding: 20px;
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
        }
        
        .stat-value {
            font-size: 32px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .stat-label {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .tech-stack {
            background: #f0f9ff;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 40px;
        }
        
        .tech-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #303133;
            text-align: center;
        }
        
        .tech-list {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            justify-content: center;
        }
        
        .tech-tag {
            background: #409EFF;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .demo-actions {
            text-align: center;
            padding: 20px 0;
        }
        
        .demo-btn {
            display: inline-block;
            background: #409EFF;
            color: white;
            padding: 12px 24px;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 500;
            margin: 0 10px;
            transition: background-color 0.3s;
        }
        
        .demo-btn:hover {
            background: #337ecc;
            color: white;
        }
        
        .demo-btn.secondary {
            background: #67C23A;
        }
        
        .demo-btn.secondary:hover {
            background: #529b2e;
        }
        
        @media (max-width: 768px) {
            .demo-header {
                padding: 20px;
            }
            
            .demo-title {
                font-size: 24px;
            }
            
            .demo-content {
                padding: 20px;
            }
            
            .feature-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <!-- 头部 -->
        <div class="demo-header">
            <h1 class="demo-title">🏥 医疗设备管理系统</h1>
            <p class="demo-subtitle">现代化前端界面 - 基于 Vue.js 3 + Element Plus</p>
        </div>
        
        <!-- 内容区域 -->
        <div class="demo-content">
            <!-- 功能特性 -->
            <div class="feature-grid">
                <div class="feature-card">
                    <div class="feature-icon">🎨</div>
                    <h3 class="feature-title">现代化UI设计</h3>
                    <p class="feature-desc">基于Element Plus的美观界面，支持主题切换和响应式设计</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">⚡</div>
                    <h3 class="feature-title">高性能架构</h3>
                    <p class="feature-desc">Vue.js 3 + Vite构建，支持代码分割和懒加载</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">🔒</div>
                    <h3 class="feature-title">安全认证</h3>
                    <p class="feature-desc">JWT令牌认证，完整的权限控制和路由守卫</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">📊</div>
                    <h3 class="feature-title">数据可视化</h3>
                    <p class="feature-desc">ECharts图表集成，实时数据展示和分析</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">📱</div>
                    <h3 class="feature-title">响应式设计</h3>
                    <p class="feature-desc">完美适配桌面端、平板和移动设备</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">🔧</div>
                    <h3 class="feature-title">模块化开发</h3>
                    <p class="feature-desc">组件化架构，易于维护和扩展</p>
                </div>
            </div>
            
            <!-- 统计数据预览 -->
            <div class="stats-preview">
                <h3 class="stats-title">系统数据概览</h3>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value">156</div>
                        <div class="stat-label">设备总数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">142</div>
                        <div class="stat-label">正常设备</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">8</div>
                        <div class="stat-label">维护中</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">6</div>
                        <div class="stat-label">故障设备</div>
                    </div>
                </div>
            </div>
            
            <!-- 技术栈 -->
            <div class="tech-stack">
                <h3 class="tech-title">技术栈</h3>
                <div class="tech-list">
                    <span class="tech-tag">Vue.js 3</span>
                    <span class="tech-tag">Element Plus</span>
                    <span class="tech-tag">Pinia</span>
                    <span class="tech-tag">Vue Router</span>
                    <span class="tech-tag">Axios</span>
                    <span class="tech-tag">ECharts</span>
                    <span class="tech-tag">Vite</span>
                    <span class="tech-tag">Django REST</span>
                </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="demo-actions">
                <a href="/modern/" class="demo-btn">查看完整界面</a>
                <a href="/api/swagger/" class="demo-btn secondary">API文档</a>
                <a href="/" class="demo-btn" style="background: #E6A23C;">返回原版</a>
            </div>
        </div>
    </div>
    
    <script>
        // 简单的交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 为功能卡片添加点击效果
            const cards = document.querySelectorAll('.feature-card');
            cards.forEach(card => {
                card.addEventListener('click', function() {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });
            
            // 统计数字动画
            const statValues = document.querySelectorAll('.stat-value');
            statValues.forEach(stat => {
                const finalValue = parseInt(stat.textContent);
                let currentValue = 0;
                const increment = finalValue / 50;
                
                const timer = setInterval(() => {
                    currentValue += increment;
                    if (currentValue >= finalValue) {
                        stat.textContent = finalValue;
                        clearInterval(timer);
                    } else {
                        stat.textContent = Math.floor(currentValue);
                    }
                }, 30);
            });
        });
    </script>
</body>
</html>
