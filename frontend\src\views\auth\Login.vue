<template>
  <div class="login-container">
    <div class="login-wrapper">
      <!-- 左侧背景区域 -->
      <div class="login-bg">
        <div class="bg-content">
          <h1 class="system-title">医疗设备管理系统</h1>
          <p class="system-desc">现代化的医疗设备管理解决方案</p>
          <div class="feature-list">
            <div class="feature-item">
              <el-icon><Monitor /></el-icon>
              <span>设备全生命周期管理</span>
            </div>
            <div class="feature-item">
              <el-icon><Tools /></el-icon>
              <span>智能维护计划</span>
            </div>
            <div class="feature-item">
              <el-icon><DataAnalysis /></el-icon>
              <span>数据分析与报表</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 右侧登录表单 -->
      <div class="login-form-wrapper">
        <div class="login-form-container">
          <!-- Logo和标题 -->
          <div class="login-header">
            <el-icon class="logo-icon"><Monitor /></el-icon>
            <h2 class="login-title">用户登录</h2>
            <p class="login-subtitle">请输入您的账号和密码</p>
          </div>
          
          <!-- 登录表单 -->
          <el-form
            ref="loginFormRef"
            :model="loginForm"
            :rules="loginRules"
            class="login-form"
            @keyup.enter="handleLogin"
          >
            <el-form-item prop="username">
              <el-input
                v-model="loginForm.username"
                placeholder="请输入用户名"
                size="large"
                prefix-icon="User"
                clearable
              />
            </el-form-item>
            
            <el-form-item prop="password">
              <el-input
                v-model="loginForm.password"
                type="password"
                placeholder="请输入密码"
                size="large"
                prefix-icon="Lock"
                show-password
                clearable
              />
            </el-form-item>
            
            <el-form-item>
              <div class="login-options">
                <el-checkbox v-model="loginForm.remember">记住密码</el-checkbox>
                <el-link type="primary" :underline="false">忘记密码？</el-link>
              </div>
            </el-form-item>
            
            <el-form-item>
              <el-button
                type="primary"
                size="large"
                :loading="loading"
                @click="handleLogin"
                class="login-btn"
              >
                {{ loading ? '登录中...' : '登录' }}
              </el-button>
            </el-form-item>
          </el-form>
          
          <!-- 其他登录方式 -->
          <div class="other-login">
            <el-divider>其他登录方式</el-divider>
            <div class="social-login">
              <el-button circle>
                <el-icon><Message /></el-icon>
              </el-button>
              <el-button circle>
                <el-icon><ChatDotRound /></el-icon>
              </el-button>
            </div>
          </div>
          
          <!-- 版权信息 -->
          <div class="copyright">
            <p>&copy; 2024 医疗设备管理系统. All rights reserved.</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'

const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const loginFormRef = ref()
const loading = ref(false)

const loginForm = reactive({
  username: '',
  password: '',
  remember: false
})

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ]
}

// 登录处理
const handleLogin = async () => {
  if (!loginFormRef.value) return
  
  try {
    const valid = await loginFormRef.value.validate()
    if (!valid) return
    
    loading.value = true
    
    const success = await authStore.login({
      username: loginForm.username,
      password: loginForm.password
    })
    
    if (success) {
      ElMessage.success('登录成功')
      
      // 保存记住密码设置
      if (loginForm.remember) {
        localStorage.setItem('rememberedUsername', loginForm.username)
      } else {
        localStorage.removeItem('rememberedUsername')
      }
      
      // 跳转到首页
      router.push('/')
    }
  } catch (error) {
    console.error('Login error:', error)
  } finally {
    loading.value = false
  }
}

// 初始化
const init = () => {
  // 如果已经登录，直接跳转
  if (authStore.isAuthenticated) {
    router.push('/')
    return
  }
  
  // 恢复记住的用户名
  const rememberedUsername = localStorage.getItem('rememberedUsername')
  if (rememberedUsername) {
    loginForm.username = rememberedUsername
    loginForm.remember = true
  }
}

// 组件挂载时初始化
init()
</script>

<style lang="scss" scoped>
.login-container {
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  
  .login-wrapper {
    width: 100%;
    max-width: 1000px;
    height: 600px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    display: flex;
    
    @media (max-width: 768px) {
      flex-direction: column;
      height: auto;
      max-width: 400px;
    }
    
    .login-bg {
      flex: 1;
      background: linear-gradient(135deg, #409EFF 0%, #36CFC9 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      position: relative;
      
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
      }
      
      .bg-content {
        position: relative;
        z-index: 1;
        text-align: center;
        padding: 40px;
        
        .system-title {
          font-size: 32px;
          font-weight: 600;
          margin-bottom: 16px;
        }
        
        .system-desc {
          font-size: 16px;
          opacity: 0.9;
          margin-bottom: 40px;
        }
        
        .feature-list {
          .feature-item {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 16px;
            font-size: 14px;
            
            .el-icon {
              font-size: 20px;
              margin-right: 8px;
            }
          }
        }
      }
      
      @media (max-width: 768px) {
        display: none;
      }
    }
    
    .login-form-wrapper {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 40px;
      
      @media (max-width: 768px) {
        padding: 20px;
      }
      
      .login-form-container {
        width: 100%;
        max-width: 360px;
        
        .login-header {
          text-align: center;
          margin-bottom: 40px;
          
          .logo-icon {
            font-size: 48px;
            color: var(--el-color-primary);
            margin-bottom: 16px;
          }
          
          .login-title {
            font-size: 24px;
            font-weight: 600;
            color: var(--el-text-color-primary);
            margin-bottom: 8px;
          }
          
          .login-subtitle {
            font-size: 14px;
            color: var(--el-text-color-secondary);
          }
        }
        
        .login-form {
          .login-options {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
          }
          
          .login-btn {
            width: 100%;
            height: 48px;
            font-size: 16px;
            font-weight: 500;
          }
        }
        
        .other-login {
          margin-top: 32px;
          
          .social-login {
            display: flex;
            justify-content: center;
            gap: 16px;
            margin-top: 16px;
            
            .el-button {
              width: 40px;
              height: 40px;
            }
          }
        }
        
        .copyright {
          margin-top: 32px;
          text-align: center;
          
          p {
            font-size: 12px;
            color: var(--el-text-color-secondary);
          }
        }
      }
    }
  }
}
</style>
