<template>
  <div class="dashboard-container">
    <!-- 统计卡片 -->
    <div class="stats-grid">
      <el-card class="stat-card" v-for="stat in stats" :key="stat.key">
        <div class="flex-between">
          <div>
            <div class="stat-value" :style="{ color: stat.color }">
              {{ stat.value }}
            </div>
            <div class="stat-label">{{ stat.label }}</div>
          </div>
          <el-icon class="stat-icon" :style="{ color: stat.color }">
            <component :is="stat.icon" />
          </el-icon>
        </div>
      </el-card>
    </div>
    
    <!-- 图表区域 -->
    <div class="charts-grid">
      <!-- 设备状态分布 -->
      <el-card class="chart-card">
        <template #header>
          <div class="flex-between">
            <span>设备状态分布</span>
            <el-button type="text" @click="refreshDeviceChart">
              <el-icon><Refresh /></el-icon>
            </el-button>
          </div>
        </template>
        <div class="chart-container">
          <v-chart :option="deviceStatusOption" class="chart" />
        </div>
      </el-card>
      
      <!-- 维护趋势 -->
      <el-card class="chart-card">
        <template #header>
          <div class="flex-between">
            <span>维护趋势</span>
            <el-button type="text" @click="refreshMaintenanceChart">
              <el-icon><Refresh /></el-icon>
            </el-button>
          </div>
        </template>
        <div class="chart-container">
          <v-chart :option="maintenanceTrendOption" class="chart" />
        </div>
      </el-card>
    </div>
    
    <!-- 快速操作和最近活动 -->
    <div class="bottom-grid">
      <!-- 快速操作 -->
      <el-card class="quick-actions-card">
        <template #header>
          <span>快速操作</span>
        </template>
        <div class="quick-actions">
          <el-button
            v-for="action in quickActions"
            :key="action.key"
            :type="action.type"
            :icon="action.icon"
            @click="handleQuickAction(action.key)"
            class="action-btn"
          >
            {{ action.label }}
          </el-button>
        </div>
      </el-card>
      
      <!-- 最近活动 -->
      <el-card class="recent-activities-card">
        <template #header>
          <span>最近活动</span>
        </template>
        <div class="activities-list">
          <div
            v-for="activity in recentActivities"
            :key="activity.id"
            class="activity-item"
          >
            <el-icon class="activity-icon" :style="{ color: activity.color }">
              <component :is="activity.icon" />
            </el-icon>
            <div class="activity-content">
              <div class="activity-title">{{ activity.title }}</div>
              <div class="activity-time">{{ activity.time }}</div>
            </div>
          </div>
          
          <div v-if="recentActivities.length === 0" class="empty-container">
            <el-icon class="empty-icon"><DocumentRemove /></el-icon>
            <div class="empty-text">暂无最近活动</div>
          </div>
        </div>
      </el-card>
    </div>
    
    <!-- 系统告警 -->
    <el-card class="alerts-card" v-if="alerts.length > 0">
      <template #header>
        <div class="flex-between">
          <span>系统告警</span>
          <el-badge :value="alerts.length" type="danger" />
        </div>
      </template>
      <div class="alerts-list">
        <el-alert
          v-for="alert in alerts"
          :key="alert.id"
          :title="alert.title"
          :description="alert.description"
          :type="alert.type"
          :closable="false"
          show-icon
          class="alert-item"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { PieChart, LineChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'
import VChart from 'vue-echarts'

// 注册 ECharts 组件
use([
  CanvasRenderer,
  PieChart,
  LineChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

// 响应式数据
const loading = ref(false)

// 统计数据
const stats = ref([
  {
    key: 'total_devices',
    label: '设备总数',
    value: 0,
    color: '#409EFF',
    icon: 'Monitor'
  },
  {
    key: 'normal_devices',
    label: '正常设备',
    value: 0,
    color: '#67C23A',
    icon: 'CircleCheck'
  },
  {
    key: 'maintenance_devices',
    label: '维护中',
    value: 0,
    color: '#E6A23C',
    icon: 'Tools'
  },
  {
    key: 'fault_devices',
    label: '故障设备',
    value: 0,
    color: '#F56C6C',
    icon: 'Warning'
  }
])

// 设备状态分布图表配置
const deviceStatusOption = ref({
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  legend: {
    orient: 'vertical',
    left: 'left'
  },
  series: [
    {
      name: '设备状态',
      type: 'pie',
      radius: '50%',
      data: [
        { value: 0, name: '正常' },
        { value: 0, name: '维护中' },
        { value: 0, name: '故障' },
        { value: 0, name: '闲置' }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
})

// 维护趋势图表配置
const maintenanceTrendOption = ref({
  tooltip: {
    trigger: 'axis'
  },
  legend: {
    data: ['维护次数', '故障次数']
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ['1月', '2月', '3月', '4月', '5月', '6月']
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: '维护次数',
      type: 'line',
      data: [12, 15, 18, 22, 19, 25]
    },
    {
      name: '故障次数',
      type: 'line',
      data: [3, 5, 2, 8, 4, 6]
    }
  ]
})

// 快速操作
const quickActions = ref([
  { key: 'add_device', label: '添加设备', type: 'primary', icon: 'Plus' },
  { key: 'report_fault', label: '故障报告', type: 'warning', icon: 'Warning' },
  { key: 'maintenance_plan', label: '维护计划', type: 'success', icon: 'Calendar' },
  { key: 'inventory_check', label: '库存盘点', type: 'info', icon: 'Box' }
])

// 最近活动
const recentActivities = ref([])

// 系统告警
const alerts = ref([])

// 方法
const loadDashboardData = async () => {
  loading.value = true
  try {
    // 这里调用API获取仪表板数据
    // const response = await dashboardApi.getStats()
    
    // 模拟数据
    stats.value[0].value = 156
    stats.value[1].value = 142
    stats.value[2].value = 8
    stats.value[3].value = 6
    
    // 更新图表数据
    deviceStatusOption.value.series[0].data = [
      { value: 142, name: '正常' },
      { value: 8, name: '维护中' },
      { value: 6, name: '故障' },
      { value: 0, name: '闲置' }
    ]
    
    // 模拟最近活动
    recentActivities.value = [
      {
        id: 1,
        title: '设备 MRI-001 完成维护',
        time: '2小时前',
        icon: 'CircleCheck',
        color: '#67C23A'
      },
      {
        id: 2,
        title: '新增设备 CT-005',
        time: '4小时前',
        icon: 'Plus',
        color: '#409EFF'
      },
      {
        id: 3,
        title: '设备 X-RAY-003 报告故障',
        time: '6小时前',
        icon: 'Warning',
        color: '#F56C6C'
      }
    ]
    
    // 模拟告警
    alerts.value = [
      {
        id: 1,
        title: '设备维护提醒',
        description: '3台设备即将到达维护周期',
        type: 'warning'
      }
    ]
  } catch (error) {
    console.error('Failed to load dashboard data:', error)
  } finally {
    loading.value = false
  }
}

const refreshDeviceChart = () => {
  // 刷新设备状态图表
  loadDashboardData()
}

const refreshMaintenanceChart = () => {
  // 刷新维护趋势图表
  loadDashboardData()
}

const handleQuickAction = (actionKey) => {
  switch (actionKey) {
    case 'add_device':
      // 跳转到添加设备页面
      break
    case 'report_fault':
      // 打开故障报告对话框
      break
    case 'maintenance_plan':
      // 跳转到维护计划页面
      break
    case 'inventory_check':
      // 跳转到库存盘点页面
      break
  }
}

// 生命周期
onMounted(() => {
  loadDashboardData()
})
</script>

<style lang="scss" scoped>
.dashboard-container {
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
    
    .stat-card {
      .stat-value {
        font-size: 32px;
        font-weight: 600;
        margin-bottom: 4px;
      }
      
      .stat-label {
        font-size: 14px;
        color: var(--el-text-color-secondary);
      }
      
      .stat-icon {
        font-size: 40px;
        opacity: 0.8;
      }
    }
  }
  
  .charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
    
    .chart-card {
      .chart-container {
        height: 300px;
        
        .chart {
          height: 100%;
          width: 100%;
        }
      }
    }
  }
  
  .bottom-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
    
    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
    
    .quick-actions-card {
      .quick-actions {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 12px;
        
        .action-btn {
          height: 48px;
        }
      }
    }
    
    .recent-activities-card {
      .activities-list {
        .activity-item {
          display: flex;
          align-items: center;
          padding: 12px 0;
          border-bottom: 1px solid var(--el-border-color-lighter);
          
          &:last-child {
            border-bottom: none;
          }
          
          .activity-icon {
            font-size: 20px;
            margin-right: 12px;
          }
          
          .activity-content {
            flex: 1;
            
            .activity-title {
              font-size: 14px;
              color: var(--el-text-color-primary);
              margin-bottom: 4px;
            }
            
            .activity-time {
              font-size: 12px;
              color: var(--el-text-color-secondary);
            }
          }
        }
      }
    }
  }
  
  .alerts-card {
    .alerts-list {
      .alert-item {
        margin-bottom: 12px;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>
