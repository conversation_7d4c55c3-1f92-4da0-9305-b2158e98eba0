"""
设备模块序列化器
"""
from rest_framework import serializers
from django.utils import timezone
from .models import (
    DeviceCategory, DeviceManufacturer, DeviceModel, Device,
    DeviceStatusHistory, DeviceAttachment, DeviceLocation
)


class DeviceCategorySerializer(serializers.ModelSerializer):
    """设备分类序列化器"""
    full_path = serializers.SerializerMethodField()
    children_count = serializers.SerializerMethodField()
    device_count = serializers.SerializerMethodField()
    
    class Meta:
        model = DeviceCategory
        fields = [
            'id', 'name', 'code', 'description', 'parent', 'icon', 'color',
            'sort_order', 'is_active', 'full_path', 'children_count', 'device_count',
            'created_at', 'updated_at', 'created_by'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'created_by']
    
    def get_full_path(self, obj):
        """获取完整路径"""
        return obj.get_full_path()
    
    def get_children_count(self, obj):
        """获取子分类数量"""
        return obj.children.filter(is_active=True).count()
    
    def get_device_count(self, obj):
        """获取设备数量"""
        return Device.objects.filter(device_model__category=obj, is_active=True).count()


class DeviceManufacturerSerializer(serializers.ModelSerializer):
    """设备制造商序列化器"""
    device_model_count = serializers.SerializerMethodField()
    
    class Meta:
        model = DeviceManufacturer
        fields = [
            'id', 'name', 'code', 'description', 'contact_person', 'phone',
            'email', 'address', 'website', 'is_active', 'device_model_count',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_device_model_count(self, obj):
        """获取设备型号数量"""
        return obj.devicemodel_set.filter(is_active=True).count()


class DeviceModelSerializer(serializers.ModelSerializer):
    """设备型号序列化器"""
    manufacturer_name = serializers.CharField(source='manufacturer.name', read_only=True)
    category_name = serializers.CharField(source='category.name', read_only=True)
    device_count = serializers.SerializerMethodField()
    
    class Meta:
        model = DeviceModel
        fields = [
            'id', 'name', 'model_number', 'manufacturer', 'manufacturer_name',
            'category', 'category_name', 'specifications', 'description',
            'image', 'manual_url', 'purchase_price', 'is_active', 'device_count',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_device_count(self, obj):
        """获取设备数量"""
        return obj.device_set.filter(is_active=True).count()


class DeviceLocationSerializer(serializers.ModelSerializer):
    """设备位置序列化器"""
    full_path = serializers.SerializerMethodField()
    manager_name = serializers.CharField(source='manager.username', read_only=True)
    device_count = serializers.SerializerMethodField()
    
    class Meta:
        model = DeviceLocation
        fields = [
            'id', 'name', 'code', 'description', 'parent', 'location_type',
            'manager', 'manager_name', 'is_active', 'full_path', 'device_count',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_full_path(self, obj):
        """获取完整路径"""
        return obj.get_full_path()
    
    def get_device_count(self, obj):
        """获取设备数量"""
        return Device.objects.filter(location=obj.name, is_active=True).count()


class DeviceStatusHistorySerializer(serializers.ModelSerializer):
    """设备状态历史序列化器"""
    device_name = serializers.CharField(source='device.name', read_only=True)
    device_asset_number = serializers.CharField(source='device.asset_number', read_only=True)
    changed_by_name = serializers.CharField(source='changed_by.username', read_only=True)
    
    class Meta:
        model = DeviceStatusHistory
        fields = [
            'id', 'device', 'device_name', 'device_asset_number',
            'old_status', 'new_status', 'old_usage_status', 'new_usage_status',
            'reason', 'description', 'changed_by', 'changed_by_name', 'changed_at'
        ]
        read_only_fields = ['id', 'changed_at']


class DeviceAttachmentSerializer(serializers.ModelSerializer):
    """设备附件序列化器"""
    device_name = serializers.CharField(source='device.name', read_only=True)
    uploaded_by_name = serializers.CharField(source='uploaded_by.username', read_only=True)
    file_size_mb = serializers.SerializerMethodField()
    
    class Meta:
        model = DeviceAttachment
        fields = [
            'id', 'device', 'device_name', 'name', 'description', 'file',
            'file_size', 'file_size_mb', 'attachment_type',
            'uploaded_by', 'uploaded_by_name', 'uploaded_at'
        ]
        read_only_fields = ['id', 'file_size', 'uploaded_by', 'uploaded_at']
    
    def get_file_size_mb(self, obj):
        """获取文件大小（MB）"""
        if obj.file_size:
            return round(obj.file_size / (1024 * 1024), 2)
        return 0
    
    def create(self, validated_data):
        """创建附件"""
        validated_data['uploaded_by'] = self.context['request'].user
        return super().create(validated_data)


class DeviceSerializer(serializers.ModelSerializer):
    """设备序列化器"""
    device_model_name = serializers.CharField(source='device_model.name', read_only=True)
    manufacturer_name = serializers.CharField(source='device_model.manufacturer.name', read_only=True)
    category_name = serializers.CharField(source='device_model.category.name', read_only=True)
    responsible_person_name = serializers.CharField(source='responsible_person.username', read_only=True)
    operator_name = serializers.CharField(source='operator.username', read_only=True)
    created_by_name = serializers.CharField(source='created_by.username', read_only=True)
    
    # 计算字段
    is_under_warranty = serializers.ReadOnlyField()
    warranty_days_left = serializers.ReadOnlyField()
    status_color = serializers.SerializerMethodField()
    
    # 关联数据
    attachments = DeviceAttachmentSerializer(many=True, read_only=True)
    recent_status_history = serializers.SerializerMethodField()
    
    class Meta:
        model = Device
        fields = [
            'id', 'asset_number', 'serial_number', 'name', 'device_model',
            'device_model_name', 'manufacturer_name', 'category_name',
            'department', 'location', 'room', 'responsible_person',
            'responsible_person_name', 'operator', 'operator_name',
            'purchase_date', 'purchase_price', 'supplier',
            'warranty_start_date', 'warranty_end_date', 'is_under_warranty',
            'warranty_days_left', 'status', 'usage_status', 'importance',
            'status_color', 'parameters', 'notes', 'last_check_date',
            'last_check_result', 'next_check_date', 'is_active',
            'attachments', 'recent_status_history',
            'created_at', 'updated_at', 'created_by', 'created_by_name'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'created_by']
    
    def get_status_color(self, obj):
        """获取状态颜色"""
        return obj.get_status_display_color()
    
    def get_recent_status_history(self, obj):
        """获取最近的状态历史"""
        recent_history = obj.status_history.all()[:5]
        return DeviceStatusHistorySerializer(recent_history, many=True).data
    
    def create(self, validated_data):
        """创建设备"""
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)
    
    def update(self, instance, validated_data):
        """更新设备"""
        # 检查状态是否发生变化
        old_status = instance.status
        old_usage_status = instance.usage_status
        new_status = validated_data.get('status', old_status)
        new_usage_status = validated_data.get('usage_status', old_usage_status)
        
        # 更新设备
        device = super().update(instance, validated_data)
        
        # 如果状态发生变化，记录历史
        if old_status != new_status or old_usage_status != new_usage_status:
            DeviceStatusHistory.objects.create(
                device=device,
                old_status=old_status,
                new_status=new_status,
                old_usage_status=old_usage_status,
                new_usage_status=new_usage_status,
                reason='状态更新',
                description=f'设备状态从 {old_status} 更新为 {new_status}',
                changed_by=self.context['request'].user
            )
        
        return device


class DeviceListSerializer(serializers.ModelSerializer):
    """设备列表序列化器"""
    device_model_name = serializers.CharField(source='device_model.name', read_only=True)
    manufacturer_name = serializers.CharField(source='device_model.manufacturer.name', read_only=True)
    category_name = serializers.CharField(source='device_model.category.name', read_only=True)
    responsible_person_name = serializers.CharField(source='responsible_person.username', read_only=True)
    status_color = serializers.SerializerMethodField()
    is_under_warranty = serializers.ReadOnlyField()
    
    class Meta:
        model = Device
        fields = [
            'id', 'asset_number', 'name', 'device_model_name', 'manufacturer_name',
            'category_name', 'department', 'location', 'responsible_person_name',
            'status', 'usage_status', 'importance', 'status_color',
            'is_under_warranty', 'last_check_date', 'next_check_date'
        ]
    
    def get_status_color(self, obj):
        """获取状态颜色"""
        return obj.get_status_display_color()


class DeviceStatsSerializer(serializers.Serializer):
    """设备统计序列化器"""
    total_devices = serializers.IntegerField()
    status_distribution = serializers.DictField()
    category_distribution = serializers.DictField()
    warranty_status = serializers.DictField()
    importance_distribution = serializers.DictField()
    recent_additions = DeviceListSerializer(many=True)
