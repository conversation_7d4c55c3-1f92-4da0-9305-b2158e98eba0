#!/usr/bin/env python
"""
部署脚本
"""
import os
import sys
import subprocess
import django
from pathlib import Path

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent.parent.parent
sys.path.insert(0, str(PROJECT_ROOT / 'src'))

from django.core.management import execute_from_command_line

def run_command(command, description):
    """运行命令并显示结果"""
    print(f"\n{'='*50}")
    print(f"执行: {description}")
    print(f"命令: {command}")
    print('='*50)
    
    try:
        if isinstance(command, list):
            result = subprocess.run(command, check=True, capture_output=True, text=True)
        else:
            result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        
        if result.stdout:
            print("输出:")
            print(result.stdout)
        
        print(f"✓ {description} 完成")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"✗ {description} 失败")
        print(f"错误: {e}")
        if e.stdout:
            print(f"输出: {e.stdout}")
        if e.stderr:
            print(f"错误输出: {e.stderr}")
        return False

def setup_environment():
    """设置环境"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'medical_device_management.settings')
    django.setup()

def main():
    """主部署流程"""
    print("医疗设备管理系统部署脚本")
    print("="*50)
    
    # 切换到项目根目录
    os.chdir(PROJECT_ROOT)
    
    # 设置环境
    setup_environment()
    
    # 检查Python版本
    python_version = sys.version_info
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 10):
        print("错误: 需要Python 3.10或更高版本")
        return False
    
    print(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 安装依赖
    if not run_command([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], 
                      "安装Python依赖"):
        return False
    
    # 检查Django配置
    if not run_command([sys.executable, 'manage.py', 'check'], "检查Django配置"):
        return False
    
    # 创建迁移
    if not run_command([sys.executable, 'manage.py', 'makemigrations'], "创建数据库迁移"):
        return False
    
    # 执行迁移
    if not run_command([sys.executable, 'manage.py', 'migrate'], "执行数据库迁移"):
        return False
    
    # 收集静态文件
    if not run_command([sys.executable, 'manage.py', 'collectstatic', '--noinput'], 
                      "收集静态文件"):
        print("警告: 静态文件收集失败，但继续部署")
    
    # 创建默认数据
    print("\n创建默认数据...")
    try:
        execute_from_command_line(['manage.py', 'create_default_themes'])
        print("✓ 默认主题创建完成")
    except Exception as e:
        print(f"警告: 默认主题创建失败: {e}")
    
    try:
        execute_from_command_line(['manage.py', 'create_default_icons'])
        print("✓ 默认图标创建完成")
    except Exception as e:
        print(f"警告: 默认图标创建失败: {e}")
    
    # 运行测试
    print("\n运行测试...")
    if not run_command([sys.executable, 'manage.py', 'test', '--settings=deployment.config.test_settings'],
                      "运行单元测试"):
        print("警告: 测试失败，但继续部署")
    
    # 创建超级用户提示
    print("\n" + "="*50)
    print("部署完成！")
    print("="*50)
    print("\n下一步操作:")
    print("1. 创建超级用户: python manage.py createsuperuser")
    print("2. 启动开发服务器: python manage.py runserver")
    print("3. 访问管理后台: http://127.0.0.1:8000/admin/")
    print("4. 访问API文档: http://127.0.0.1:8000/api-docs/")
    print("5. 访问API根路径: http://127.0.0.1:8000/api/")
    
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
