"""
设备模块URL配置
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

app_name = 'devices'

router = DefaultRouter()
router.register(r'categories', views.DeviceCategoryViewSet)
router.register(r'manufacturers', views.DeviceManufacturerViewSet)
router.register(r'models', views.DeviceModelViewSet)
router.register(r'locations', views.DeviceLocationViewSet)
router.register(r'devices', views.DeviceViewSet)

urlpatterns = [
    # REST API 路由
    path('', include(router.urls)),
]
