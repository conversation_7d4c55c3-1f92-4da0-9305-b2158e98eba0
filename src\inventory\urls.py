"""
库存模块URL配置
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

app_name = 'inventory'

router = DefaultRouter()
router.register(r'suppliers', views.SupplierViewSet)
router.register(r'categories', views.ItemCategoryViewSet)
router.register(r'items', views.ItemViewSet)
router.register(r'movements', views.StockMovementViewSet)
router.register(r'purchase-orders', views.PurchaseOrderViewSet)

urlpatterns = [
    # REST API 路由
    path('', include(router.urls)),
]
