"""
报表模块视图
"""
from rest_framework import viewsets, permissions, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from django.db.models import Count, Sum, Q
from django.utils import timezone
from datetime import timedel<PERSON>, datetime

from .models import ReportTemplate, ReportInstance, Dashboard
from .serializers import (
    ReportTemplateSerializer, ReportTemplateListSerializer,
    ReportInstanceSerializer, ReportInstanceListSerializer,
    DashboardSerializer, DashboardListSerializer,
    ReportGenerateSerializer, DashboardStatsSerializer, ChartDataSerializer
)


class ReportTemplateViewSet(viewsets.ModelViewSet):
    """报表模板视图集"""
    queryset = ReportTemplate.objects.filter(is_active=True)
    serializer_class = ReportTemplateSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'report_type', 'created_at']
    ordering = ['-created_at']

    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'list':
            return ReportTemplateListSerializer
        return ReportTemplateSerializer

    def get_queryset(self):
        """获取查询集"""
        queryset = super().get_queryset()

        # 报表类型过滤
        report_type = self.request.query_params.get('report_type')
        if report_type:
            queryset = queryset.filter(report_type=report_type)

        # 公开状态过滤
        is_public = self.request.query_params.get('is_public')
        if is_public is not None:
            queryset = queryset.filter(is_public=is_public.lower() == 'true')

        # 如果不是管理员，只能看到公开的或自己创建的模板
        if not self.request.user.is_staff:
            queryset = queryset.filter(
                Q(is_public=True) | Q(created_by=self.request.user)
            )

        return queryset.select_related('created_by')

    def perform_create(self, serializer):
        """创建时设置创建者"""
        serializer.save(created_by=self.request.user)

    @action(detail=True, methods=['post'])
    def generate(self, request, pk=None):
        """生成报表"""
        template = self.get_object()
        serializer = ReportGenerateSerializer(data=request.data)

        if serializer.is_valid():
            # 创建报表实例
            instance_data = {
                'template': template,
                'title': serializer.validated_data.get('title', template.name),
                'description': serializer.validated_data.get('description', ''),
                'parameters': serializer.validated_data.get('parameters', {}),
                'generated_by': request.user
            }

            instance = ReportInstance.objects.create(**instance_data)

            # 这里应该启动异步任务来生成报表
            # 暂时直接标记为完成
            instance.mark_completed()

            return Response({
                'message': '报表生成任务已启动',
                'instance': ReportInstanceSerializer(instance).data
            })

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ReportInstanceViewSet(viewsets.ModelViewSet):
    """报表实例视图集"""
    queryset = ReportInstance.objects.all()
    serializer_class = ReportInstanceSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['title', 'template__name']
    ordering_fields = ['generated_at', 'status', 'total_records']
    ordering = ['-generated_at']

    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'list':
            return ReportInstanceListSerializer
        return ReportInstanceSerializer

    def get_queryset(self):
        """获取查询集"""
        queryset = super().get_queryset()

        # 模板过滤
        template = self.request.query_params.get('template')
        if template:
            queryset = queryset.filter(template__id=template)

        # 状态过滤
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # 如果不是管理员，只能看到自己生成的报表
        if not self.request.user.is_staff:
            queryset = queryset.filter(generated_by=self.request.user)

        return queryset.select_related('template', 'generated_by')

    def perform_create(self, serializer):
        """创建时设置生成者"""
        serializer.save(generated_by=self.request.user)

    @action(detail=True, methods=['get'])
    def download(self, request, pk=None):
        """下载报表文件"""
        instance = self.get_object()

        if instance.status != 'completed':
            return Response(
                {'error': '报表尚未生成完成'},
                status=status.HTTP_400_BAD_REQUEST
            )

        if not instance.file_path:
            return Response(
                {'error': '报表文件不存在'},
                status=status.HTTP_404_NOT_FOUND
            )

        # 这里应该返回文件下载响应
        return Response({
            'download_url': f'/media/reports/{instance.file_path}',
            'file_size': instance.file_size
        })


class DashboardViewSet(viewsets.ModelViewSet):
    """仪表板视图集"""
    queryset = Dashboard.objects.filter(is_active=True)
    serializer_class = DashboardSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'created_at']
    ordering = ['-created_at']

    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'list':
            return DashboardListSerializer
        return DashboardSerializer

    def get_queryset(self):
        """获取查询集"""
        queryset = super().get_queryset()

        # 如果不是管理员，只能看到公开的或有权限访问的仪表板
        if not self.request.user.is_staff:
            queryset = queryset.filter(
                Q(is_public=True) |
                Q(created_by=self.request.user) |
                Q(allowed_users=self.request.user)
            ).distinct()

        return queryset.select_related('created_by').prefetch_related('allowed_users')

    def perform_create(self, serializer):
        """创建时设置创建者"""
        serializer.save(created_by=self.request.user)


class DashboardStatsView(APIView):
    """仪表板统计视图"""
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        """获取仪表板统计数据"""
        from devices.models import Device
        from maintenance.models import MaintenanceRecord, FaultReport
        from inventory.models import Item

        # 设备统计
        device_stats = {
            'total': Device.objects.filter(is_active=True).count(),
            'normal': Device.objects.filter(status='normal', is_active=True).count(),
            'maintenance': Device.objects.filter(status='maintenance', is_active=True).count(),
            'fault': Device.objects.filter(status='fault', is_active=True).count(),
        }

        # 维护统计
        today = timezone.now().date()
        maintenance_stats = {
            'total_this_month': MaintenanceRecord.objects.filter(
                scheduled_date__month=today.month,
                scheduled_date__year=today.year
            ).count(),
            'completed_this_month': MaintenanceRecord.objects.filter(
                scheduled_date__month=today.month,
                scheduled_date__year=today.year,
                status='completed'
            ).count(),
        }

        # 库存统计
        inventory_stats = {
            'total_items': Item.objects.filter(is_active=True).count(),
            'low_stock_items': len([item for item in Item.objects.filter(is_active=True) if item.is_low_stock]),
        }

        # 故障统计
        fault_stats = {
            'open_faults': FaultReport.objects.filter(status='open').count(),
            'critical_faults': FaultReport.objects.filter(
                severity='critical',
                status__in=['open', 'assigned', 'in_progress']
            ).count(),
        }

        # 最近活动
        recent_activities = []

        # 告警信息
        alerts = []

        # 检查需要维护的设备
        from maintenance.models import MaintenancePlan
        overdue_plans = MaintenancePlan.objects.filter(
            next_maintenance_date__lt=today,
            is_active=True
        ).count()

        if overdue_plans > 0:
            alerts.append({
                'type': 'warning',
                'message': f'有 {overdue_plans} 个维护计划已逾期',
                'action': '/maintenance/plans?overdue=true'
            })

        # 检查低库存物品
        low_stock_count = inventory_stats['low_stock_items']
        if low_stock_count > 0:
            alerts.append({
                'type': 'warning',
                'message': f'有 {low_stock_count} 个物品库存不足',
                'action': '/inventory/items?stock_status=low_stock'
            })

        # 检查关键故障
        if fault_stats['critical_faults'] > 0:
            alerts.append({
                'type': 'danger',
                'message': f'有 {fault_stats["critical_faults"]} 个关键故障待处理',
                'action': '/maintenance/faults?severity=critical'
            })

        serializer = DashboardStatsSerializer({
            'device_stats': device_stats,
            'maintenance_stats': maintenance_stats,
            'inventory_stats': inventory_stats,
            'fault_stats': fault_stats,
            'recent_activities': recent_activities,
            'alerts': alerts,
        })

        return Response(serializer.data)
