from django.db import models
from django.contrib.auth.models import AbstractUser
from django.utils import timezone
from datetime import timedelta
import uuid


class User(AbstractUser):
    """扩展用户模型"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    phone = models.CharField(max_length=20, blank=True, null=True, verbose_name='手机号')
    avatar = models.ImageField(upload_to='avatars/', blank=True, null=True, verbose_name='头像')
    department = models.CharField(max_length=100, blank=True, null=True, verbose_name='部门')
    position = models.CharField(max_length=100, blank=True, null=True, verbose_name='职位')
    is_active = models.BooleanField(default=True, verbose_name='是否激活')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '用户'
        verbose_name_plural = '用户'
        db_table = 'auth_user'


class Role(models.Model):
    """角色模型"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=50, unique=True, verbose_name='角色名称')
    description = models.TextField(blank=True, null=True, verbose_name='角色描述')
    permissions = models.JSONField(default=list, verbose_name='权限列表')
    is_active = models.BooleanField(default=True, verbose_name='是否激活')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '角色'
        verbose_name_plural = '角色'
        db_table = 'auth_role'

    def __str__(self):
        return self.name


class UserRole(models.Model):
    """用户角色关联模型"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户')
    role = models.ForeignKey(Role, on_delete=models.CASCADE, verbose_name='角色')
    assigned_at = models.DateTimeField(auto_now_add=True, verbose_name='分配时间')
    assigned_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name='assigned_roles',
        verbose_name='分配者'
    )

    class Meta:
        verbose_name = '用户角色'
        verbose_name_plural = '用户角色'
        db_table = 'auth_user_role'
        unique_together = ['user', 'role']


class AccessToken(models.Model):
    """访问令牌模型"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户')
    token = models.TextField(verbose_name='令牌')
    expires_at = models.DateTimeField(verbose_name='过期时间')
    is_active = models.BooleanField(default=True, verbose_name='是否激活')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')

    class Meta:
        verbose_name = '访问令牌'
        verbose_name_plural = '访问令牌'
        db_table = 'auth_access_token'

    @property
    def is_expired(self):
        return timezone.now() > self.expires_at

    def save(self, *args, **kwargs):
        if not self.expires_at:
            from django.conf import settings
            lifetime = settings.TOKEN_SETTINGS.get('ACCESS_TOKEN_LIFETIME', 15)
            self.expires_at = timezone.now() + timedelta(minutes=lifetime)
        super().save(*args, **kwargs)


class RefreshToken(models.Model):
    """刷新令牌模型"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户')
    token = models.TextField(verbose_name='令牌')
    expires_at = models.DateTimeField(verbose_name='过期时间')
    is_active = models.BooleanField(default=True, verbose_name='是否激活')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')

    class Meta:
        verbose_name = '刷新令牌'
        verbose_name_plural = '刷新令牌'
        db_table = 'auth_refresh_token'

    @property
    def is_expired(self):
        return timezone.now() > self.expires_at

    def save(self, *args, **kwargs):
        if not self.expires_at:
            from django.conf import settings
            lifetime = settings.TOKEN_SETTINGS.get('REFRESH_TOKEN_LIFETIME', 7)
            self.expires_at = timezone.now() + timedelta(days=lifetime)
        super().save(*args, **kwargs)


class DeviceToken(models.Model):
    """设备令牌模型"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户')
    device_id = models.CharField(max_length=255, verbose_name='设备ID')
    device_name = models.CharField(max_length=255, verbose_name='设备名称')
    device_type = models.CharField(max_length=50, verbose_name='设备类型')  # web, mobile, tablet
    token = models.TextField(verbose_name='令牌')
    expires_at = models.DateTimeField(verbose_name='过期时间')
    last_used_at = models.DateTimeField(null=True, blank=True, verbose_name='最后使用时间')
    ip_address = models.GenericIPAddressField(null=True, blank=True, verbose_name='IP地址')
    user_agent = models.TextField(blank=True, null=True, verbose_name='用户代理')
    is_active = models.BooleanField(default=True, verbose_name='是否激活')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')

    class Meta:
        verbose_name = '设备令牌'
        verbose_name_plural = '设备令牌'
        db_table = 'auth_device_token'
        unique_together = ['user', 'device_id']

    @property
    def is_expired(self):
        return timezone.now() > self.expires_at

    def save(self, *args, **kwargs):
        if not self.expires_at:
            from django.conf import settings
            lifetime = settings.TOKEN_SETTINGS.get('DEVICE_TOKEN_LIFETIME', 30)
            self.expires_at = timezone.now() + timedelta(days=lifetime)
        super().save(*args, **kwargs)

    def update_last_used(self):
        """更新最后使用时间"""
        self.last_used_at = timezone.now()
        self.save(update_fields=['last_used_at'])


class LoginLog(models.Model):
    """登录日志模型"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户')
    login_time = models.DateTimeField(auto_now_add=True, verbose_name='登录时间')
    logout_time = models.DateTimeField(null=True, blank=True, verbose_name='登出时间')
    ip_address = models.GenericIPAddressField(verbose_name='IP地址')
    user_agent = models.TextField(verbose_name='用户代理')
    device_type = models.CharField(max_length=50, verbose_name='设备类型')
    login_status = models.CharField(
        max_length=20,
        choices=[
            ('success', '成功'),
            ('failed', '失败'),
            ('logout', '登出'),
        ],
        default='success',
        verbose_name='登录状态'
    )

    class Meta:
        verbose_name = '登录日志'
        verbose_name_plural = '登录日志'
        db_table = 'auth_login_log'
