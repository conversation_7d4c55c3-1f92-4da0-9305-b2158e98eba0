from django.db import models
from django.contrib.auth import get_user_model
import uuid

User = get_user_model()


class Theme(models.Model):
    """主题模型"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=50, unique=True, verbose_name='主题名称')
    display_name = models.CharField(max_length=100, verbose_name='显示名称')
    description = models.TextField(blank=True, null=True, verbose_name='主题描述')

    # 主题配置
    primary_color = models.CharField(max_length=7, default='#007bff', verbose_name='主色调')
    secondary_color = models.CharField(max_length=7, default='#6c757d', verbose_name='辅助色')
    success_color = models.CharField(max_length=7, default='#28a745', verbose_name='成功色')
    warning_color = models.CharField(max_length=7, default='#ffc107', verbose_name='警告色')
    danger_color = models.CharField(max_length=7, default='#dc3545', verbose_name='危险色')
    info_color = models.CharField(max_length=7, default='#17a2b8', verbose_name='信息色')

    # 背景和文字颜色
    background_color = models.CharField(max_length=7, default='#ffffff', verbose_name='背景色')
    text_color = models.CharField(max_length=7, default='#212529', verbose_name='文字色')
    border_color = models.CharField(max_length=7, default='#dee2e6', verbose_name='边框色')

    # 导航栏配置
    navbar_bg_color = models.CharField(max_length=7, default='#343a40', verbose_name='导航栏背景色')
    navbar_text_color = models.CharField(max_length=7, default='#ffffff', verbose_name='导航栏文字色')

    # 侧边栏配置
    sidebar_bg_color = models.CharField(max_length=7, default='#f8f9fa', verbose_name='侧边栏背景色')
    sidebar_text_color = models.CharField(max_length=7, default='#495057', verbose_name='侧边栏文字色')

    # 自定义CSS
    custom_css = models.TextField(blank=True, null=True, verbose_name='自定义CSS')

    # 主题类型
    THEME_TYPES = [
        ('light', '浅色主题'),
        ('dark', '深色主题'),
        ('medical', '医疗主题'),
        ('custom', '自定义主题'),
    ]
    theme_type = models.CharField(max_length=20, choices=THEME_TYPES, default='light', verbose_name='主题类型')

    # 状态
    is_active = models.BooleanField(default=True, verbose_name='是否激活')
    is_default = models.BooleanField(default=False, verbose_name='是否默认主题')

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name='创建者'
    )

    class Meta:
        verbose_name = '主题'
        verbose_name_plural = '主题'
        db_table = 'theme'

    def __str__(self):
        return self.display_name

    def save(self, *args, **kwargs):
        # 确保只有一个默认主题
        if self.is_default:
            Theme.objects.filter(is_default=True).update(is_default=False)
        super().save(*args, **kwargs)

    def to_css_variables(self):
        """转换为CSS变量"""
        return {
            '--primary-color': self.primary_color,
            '--secondary-color': self.secondary_color,
            '--success-color': self.success_color,
            '--warning-color': self.warning_color,
            '--danger-color': self.danger_color,
            '--info-color': self.info_color,
            '--background-color': self.background_color,
            '--text-color': self.text_color,
            '--border-color': self.border_color,
            '--navbar-bg-color': self.navbar_bg_color,
            '--navbar-text-color': self.navbar_text_color,
            '--sidebar-bg-color': self.sidebar_bg_color,
            '--sidebar-text-color': self.sidebar_text_color,
        }


class UserThemePreference(models.Model):
    """用户主题偏好"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.OneToOneField(User, on_delete=models.CASCADE, verbose_name='用户')
    theme = models.ForeignKey(Theme, on_delete=models.CASCADE, verbose_name='主题')

    # 个人定制设置
    custom_settings = models.JSONField(default=dict, blank=True, verbose_name='个人定制设置')

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '用户主题偏好'
        verbose_name_plural = '用户主题偏好'
        db_table = 'user_theme_preference'

    def __str__(self):
        return f'{self.user.username} - {self.theme.display_name}'
