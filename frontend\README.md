# 医疗设备管理系统 - 现代化前端界面

## 🎯 项目概述

这是一个基于 Vue.js 3 + Element Plus 的现代化前端界面，为医疗设备管理系统提供优秀的用户体验。

## 🚀 技术栈

- **前端框架**: Vue.js 3 (Composition API)
- **UI组件库**: Element Plus
- **状态管理**: Pinia
- **路由管理**: Vue Router 4
- **HTTP客户端**: Axios
- **图表库**: ECharts + Vue-ECharts
- **构建工具**: Vite
- **开发语言**: JavaScript/TypeScript

## 📦 安装和运行

### 前置要求

- Node.js >= 16.0.0
- npm >= 8.0.0 或 yarn >= 1.22.0

### 安装步骤

1. **安装 Node.js**
   ```bash
   # 下载并安装 Node.js
   # 访问 https://nodejs.org/ 下载最新 LTS 版本
   ```

2. **安装依赖**
   ```bash
   cd frontend
   npm install
   # 或使用 yarn
   yarn install
   ```

3. **启动开发服务器**
   ```bash
   npm run dev
   # 或使用 yarn
   yarn dev
   ```

4. **构建生产版本**
   ```bash
   npm run build
   # 或使用 yarn
   yarn build
   ```

## 🏗️ 项目结构

```
frontend/
├── public/                 # 静态资源
├── src/
│   ├── api/               # API 接口
│   │   ├── request.js     # HTTP 请求封装
│   │   ├── auth.js        # 认证相关 API
│   │   ├── devices.js     # 设备管理 API
│   │   └── theme.js       # 主题相关 API
│   ├── components/        # 公共组件
│   ├── layout/           # 布局组件
│   │   ├── index.vue     # 主布局
│   │   └── components/   # 布局子组件
│   ├── router/           # 路由配置
│   ├── stores/           # 状态管理
│   │   ├── auth.js       # 认证状态
│   │   └── theme.js      # 主题状态
│   ├── styles/           # 样式文件
│   ├── views/            # 页面组件
│   │   ├── auth/         # 认证页面
│   │   ├── dashboard/    # 仪表板
│   │   ├── devices/      # 设备管理
│   │   ├── maintenance/  # 维护管理
│   │   ├── inventory/    # 库存管理
│   │   └── reports/      # 报表分析
│   ├── App.vue           # 根组件
│   └── main.js           # 入口文件
├── index.html            # HTML 模板
├── package.json          # 项目配置
├── vite.config.js        # Vite 配置
└── README.md             # 项目说明
```

## 🎨 功能特性

### 已实现功能

- ✅ **现代化UI设计**: 基于 Element Plus 的美观界面
- ✅ **响应式布局**: 支持桌面端和移动端
- ✅ **主题切换**: 支持浅色/深色/医疗主题
- ✅ **路由管理**: 单页应用路由配置
- ✅ **状态管理**: Pinia 状态管理
- ✅ **API集成**: 完整的后端API接口封装
- ✅ **认证系统**: JWT令牌认证
- ✅ **权限控制**: 路由级别权限控制

### 开发中功能

- 🚧 **设备管理**: 设备CRUD操作界面
- 🚧 **维护管理**: 维护计划和记录管理
- 🚧 **库存管理**: 库存跟踪和管理
- 🚧 **报表分析**: 数据可视化和报表
- 🚧 **实时通知**: WebSocket实时消息推送

## 🔧 开发指南

### 环境配置

1. **开发环境变量**
   ```bash
   # 创建 .env.development 文件
   VITE_API_BASE_URL=http://127.0.0.1:8000/api
   VITE_APP_TITLE=医疗设备管理系统
   ```

2. **生产环境变量**
   ```bash
   # 创建 .env.production 文件
   VITE_API_BASE_URL=/api
   VITE_APP_TITLE=医疗设备管理系统
   ```

### 代码规范

- 使用 Vue 3 Composition API
- 遵循 Element Plus 设计规范
- 使用 ESLint 进行代码检查
- 组件命名采用 PascalCase
- 文件命名采用 kebab-case

### API 集成

```javascript
// 示例：调用设备列表API
import { devicesApi } from '@/api/devices'

const loadDevices = async () => {
  try {
    const response = await devicesApi.devices.list({
      page: 1,
      page_size: 20
    })
    console.log(response.data)
  } catch (error) {
    console.error('Failed to load devices:', error)
  }
}
```

## 🚀 部署指南

### 开发环境部署

1. **启动后端服务**
   ```bash
   cd /path/to/backend
   python manage.py runserver 8000
   ```

2. **启动前端开发服务器**
   ```bash
   cd frontend
   npm run dev
   ```

3. **访问应用**
   - 前端: http://localhost:3000
   - 后端API: http://localhost:8000/api
   - API文档: http://localhost:8000/api/swagger/

### 生产环境部署

1. **构建前端**
   ```bash
   cd frontend
   npm run build
   ```

2. **部署到Django静态文件目录**
   ```bash
   # 构建文件会自动输出到 ../src/static/frontend/
   # Django会自动服务这些静态文件
   ```

3. **配置Nginx (可选)**
   ```nginx
   server {
       listen 80;
       server_name your-domain.com;
       
       location / {
           try_files $uri $uri/ /index.html;
           root /path/to/static/frontend;
       }
       
       location /api/ {
           proxy_pass http://127.0.0.1:8000;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
       }
   }
   ```

## 🎯 下一步开发计划

### 短期目标 (1-2周)

1. **完善设备管理模块**
   - 设备列表页面
   - 设备详情页面
   - 设备添加/编辑表单
   - 设备状态管理

2. **实现维护管理功能**
   - 维护计划管理
   - 维护记录查看
   - 故障报告功能

### 中期目标 (1个月)

1. **库存管理模块**
   - 库存列表和搜索
   - 库存变动记录
   - 供应商管理

2. **报表分析功能**
   - 数据可视化图表
   - 报表生成和导出
   - 统计分析

### 长期目标 (2-3个月)

1. **高级功能**
   - 实时通知系统
   - 移动端适配优化
   - 离线功能支持
   - 多语言支持

2. **性能优化**
   - 代码分割和懒加载
   - 缓存策略优化
   - 打包体积优化

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

- 项目维护者: 医疗设备管理系统开发团队
- 邮箱: <EMAIL>
- 项目地址: https://github.com/your-org/medical-device-management

## 🙏 致谢

感谢以下开源项目的支持：

- [Vue.js](https://vuejs.org/)
- [Element Plus](https://element-plus.org/)
- [Vite](https://vitejs.dev/)
- [Pinia](https://pinia.vuejs.org/)
- [ECharts](https://echarts.apache.org/)
