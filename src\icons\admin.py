from django.contrib import admin
from django.utils.html import format_html
from .models import IconSet, Icon, IconUsage


@admin.register(IconSet)
class IconSetAdmin(admin.ModelAdmin):
    """图标集管理"""
    list_display = [
        'display_name', 'name', 'version', 'icon_type',
        'icon_count', 'is_active', 'is_default', 'created_at'
    ]
    list_filter = ['icon_type', 'is_active', 'is_default', 'created_at']
    search_fields = ['name', 'display_name', 'description']
    ordering = ['-created_at']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'display_name', 'description', 'version')
        }),
        ('配置', {
            'fields': ('icon_type', 'prefix', 'base_url', 'css_url', 'js_url')
        }),
        ('状态', {
            'fields': ('is_active', 'is_default')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def icon_count(self, obj):
        """图标数量"""
        return Icon.objects.filter(icon_set=obj, is_active=True).count()
    icon_count.short_description = '图标数量'


@admin.register(Icon)
class IconAdmin(admin.ModelAdmin):
    """图标管理"""
    list_display = [
        'display_name', 'name', 'icon_set', 'category',
        'icon_preview', 'usage_count', 'is_active', 'created_at'
    ]
    list_filter = ['icon_set', 'category', 'is_active', 'created_at']
    search_fields = ['name', 'display_name', 'description', 'tags']
    ordering = ['-created_at']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('icon_set', 'name', 'display_name', 'description')
        }),
        ('图标数据', {
            'fields': ('icon_class', 'svg_content', 'image_url')
        }),
        ('分类和标签', {
            'fields': ('category', 'tags')
        }),
        ('样式配置', {
            'fields': ('default_size', 'default_color')
        }),
        ('状态', {
            'fields': ('is_active',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def icon_preview(self, obj):
        """图标预览"""
        if obj.icon_set.icon_type == 'font':
            return format_html(
                '<i class="{}" style="font-size: 20px; color: {};"></i>',
                obj.get_full_class(),
                obj.default_color
            )
        elif obj.icon_set.icon_type == 'svg' and obj.svg_content:
            return format_html(
                '<div style="width: 20px; height: 20px;">{}</div>',
                obj.svg_content
            )
        elif obj.icon_set.icon_type == 'image' and obj.image_url:
            return format_html(
                '<img src="{}" style="width: 20px; height: 20px;" alt="{}">',
                obj.image_url,
                obj.display_name
            )
        return '-'
    icon_preview.short_description = '预览'

    def usage_count(self, obj):
        """使用次数"""
        usage = IconUsage.objects.filter(icon=obj).first()
        return usage.usage_count if usage else 0
    usage_count.short_description = '使用次数'


@admin.register(IconUsage)
class IconUsageAdmin(admin.ModelAdmin):
    """图标使用记录管理"""
    list_display = ['icon', 'usage_count', 'context', 'last_used_at']
    list_filter = ['context', 'last_used_at']
    search_fields = ['icon__name', 'icon__display_name', 'context']
    ordering = ['-last_used_at']
    readonly_fields = ['last_used_at']
