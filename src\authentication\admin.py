from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from .models import User, Role, UserRole, AccessToken, RefreshToken, DeviceToken, LoginLog


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    """用户管理"""
    list_display = ['username', 'email', 'first_name', 'last_name', 'department', 'position', 'is_active', 'created_at']
    list_filter = ['is_active', 'is_staff', 'is_superuser', 'department', 'created_at']
    search_fields = ['username', 'email', 'first_name', 'last_name', 'phone']
    ordering = ['-created_at']

    fieldsets = BaseUserAdmin.fieldsets + (
        ('扩展信息', {
            'fields': ('phone', 'avatar', 'department', 'position')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    readonly_fields = ['created_at', 'updated_at']


@admin.register(Role)
class RoleAdmin(admin.ModelAdmin):
    """角色管理"""
    list_display = ['name', 'description', 'is_active', 'user_count', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'description']
    ordering = ['-created_at']
    readonly_fields = ['created_at', 'updated_at']

    def user_count(self, obj):
        """用户数量"""
        return UserRole.objects.filter(role=obj).count()
    user_count.short_description = '用户数量'


@admin.register(UserRole)
class UserRoleAdmin(admin.ModelAdmin):
    """用户角色管理"""
    list_display = ['user', 'role', 'assigned_at', 'assigned_by']
    list_filter = ['role', 'assigned_at']
    search_fields = ['user__username', 'role__name']
    ordering = ['-assigned_at']
    readonly_fields = ['assigned_at']


@admin.register(AccessToken)
class AccessTokenAdmin(admin.ModelAdmin):
    """访问令牌管理"""
    list_display = ['user', 'is_active', 'expires_at', 'created_at']
    list_filter = ['is_active', 'expires_at', 'created_at']
    search_fields = ['user__username']
    ordering = ['-created_at']
    readonly_fields = ['token', 'created_at']


@admin.register(RefreshToken)
class RefreshTokenAdmin(admin.ModelAdmin):
    """刷新令牌管理"""
    list_display = ['user', 'is_active', 'expires_at', 'created_at']
    list_filter = ['is_active', 'expires_at', 'created_at']
    search_fields = ['user__username']
    ordering = ['-created_at']
    readonly_fields = ['token', 'created_at']


@admin.register(DeviceToken)
class DeviceTokenAdmin(admin.ModelAdmin):
    """设备令牌管理"""
    list_display = ['user', 'device_name', 'device_type', 'is_active', 'last_used_at', 'expires_at']
    list_filter = ['device_type', 'is_active', 'expires_at', 'created_at']
    search_fields = ['user__username', 'device_name', 'device_id']
    ordering = ['-created_at']
    readonly_fields = ['token', 'created_at']


@admin.register(LoginLog)
class LoginLogAdmin(admin.ModelAdmin):
    """登录日志管理"""
    list_display = ['user', 'login_time', 'logout_time', 'ip_address', 'device_type', 'login_status']
    list_filter = ['login_status', 'device_type', 'login_time']
    search_fields = ['user__username', 'ip_address']
    ordering = ['-login_time']
    readonly_fields = ['login_time', 'logout_time']
