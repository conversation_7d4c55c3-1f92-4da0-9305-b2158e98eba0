from django.contrib import admin
from django.utils.html import format_html
from .models import MaintenanceType, MaintenancePlan, MaintenanceRecord, FaultReport


@admin.register(MaintenanceType)
class MaintenanceTypeAdmin(admin.ModelAdmin):
    """维护类型管理"""
    list_display = [
        'name', 'code', 'default_duration', 'default_interval_days',
        'plan_count', 'is_active', 'created_at'
    ]
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'code', 'description']
    ordering = ['name']
    readonly_fields = ['created_at', 'updated_at']

    def plan_count(self, obj):
        """计划数量"""
        return obj.maintenanceplan_set.filter(is_active=True).count()
    plan_count.short_description = '计划数量'


@admin.register(MaintenancePlan)
class MaintenancePlanAdmin(admin.ModelAdmin):
    """维护计划管理"""
    list_display = [
        'title', 'device', 'maintenance_type', 'next_maintenance_date',
        'priority_badge', 'assigned_to', 'status_badge', 'created_at'
    ]
    list_filter = [
        'maintenance_type', 'priority', 'is_active',
        'next_maintenance_date', 'created_at'
    ]
    search_fields = ['title', 'device__name', 'device__asset_number']
    ordering = ['next_maintenance_date']
    readonly_fields = ['created_at', 'updated_at', 'is_overdue', 'days_until_maintenance']

    fieldsets = (
        ('基本信息', {
            'fields': ('device', 'maintenance_type', 'title', 'description')
        }),
        ('时间配置', {
            'fields': (
                'start_date', 'interval_days', 'estimated_duration',
                'next_maintenance_date'
            )
        }),
        ('分配信息', {
            'fields': ('assigned_to', 'priority')
        }),
        ('状态信息', {
            'fields': ('is_active', 'is_overdue', 'days_until_maintenance'),
            'classes': ('collapse',)
        }),
        ('系统信息', {
            'fields': ('created_at', 'updated_at', 'created_by'),
            'classes': ('collapse',)
        }),
    )

    def priority_badge(self, obj):
        """优先级徽章"""
        colors = {
            'low': '#28a745',
            'normal': '#17a2b8',
            'high': '#ffc107',
            'urgent': '#dc3545',
        }
        color = colors.get(obj.priority, '#6c757d')
        return format_html(
            '<span style="background-color: {}; color: white; padding: 2px 8px; '
            'border-radius: 12px; font-size: 12px;">{}</span>',
            color,
            obj.get_priority_display()
        )
    priority_badge.short_description = '优先级'

    def status_badge(self, obj):
        """状态徽章"""
        if obj.is_overdue:
            color = '#dc3545'
            text = '逾期'
        elif obj.days_until_maintenance <= 3:
            color = '#ffc107'
            text = '即将到期'
        else:
            color = '#28a745'
            text = '正常'

        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            text
        )
    status_badge.short_description = '状态'

    def save_model(self, request, obj, form, change):
        """保存模型时设置创建者"""
        if not change:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(MaintenanceRecord)
class MaintenanceRecordAdmin(admin.ModelAdmin):
    """维护记录管理"""
    list_display = [
        'title', 'device', 'maintenance_type', 'scheduled_date',
        'status_badge', 'performed_by', 'duration_display', 'rating_stars'
    ]
    list_filter = [
        'status', 'maintenance_type', 'rating',
        'scheduled_date', 'created_at'
    ]
    search_fields = ['title', 'device__name', 'device__asset_number']
    ordering = ['-scheduled_date']
    readonly_fields = ['actual_duration', 'duration_display', 'created_at', 'updated_at']

    fieldsets = (
        ('基本信息', {
            'fields': (
                'device', 'maintenance_plan', 'maintenance_type',
                'title', 'description'
            )
        }),
        ('时间信息', {
            'fields': (
                'scheduled_date', 'start_time', 'end_time',
                'actual_duration', 'duration_display'
            )
        }),
        ('执行信息', {
            'fields': ('performed_by', 'status')
        }),
        ('结果信息', {
            'fields': ('result', 'issues_found', 'parts_replaced', 'cost', 'rating')
        }),
        ('系统信息', {
            'fields': ('created_at', 'updated_at', 'created_by'),
            'classes': ('collapse',)
        }),
    )

    def status_badge(self, obj):
        """状态徽章"""
        colors = {
            'scheduled': '#17a2b8',
            'in_progress': '#ffc107',
            'completed': '#28a745',
            'cancelled': '#6c757d',
            'failed': '#dc3545',
        }
        color = colors.get(obj.status, '#6c757d')
        return format_html(
            '<span style="background-color: {}; color: white; padding: 2px 8px; '
            'border-radius: 12px; font-size: 12px;">{}</span>',
            color,
            obj.get_status_display()
        )
    status_badge.short_description = '状态'

    def rating_stars(self, obj):
        """评级星星"""
        if obj.rating:
            stars = '★' * obj.rating + '☆' * (5 - obj.rating)
            return format_html('<span style="color: #ffc107;">{}</span>', stars)
        return '-'
    rating_stars.short_description = '评级'

    def save_model(self, request, obj, form, change):
        """保存模型时设置创建者"""
        if not change:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(FaultReport)
class FaultReportAdmin(admin.ModelAdmin):
    """故障报告管理"""
    list_display = [
        'title', 'device', 'severity_badge', 'fault_type',
        'status_badge', 'occurred_at', 'reported_by', 'assigned_to'
    ]
    list_filter = [
        'severity', 'fault_type', 'status',
        'occurred_at', 'reported_at'
    ]
    search_fields = ['title', 'description', 'device__name', 'device__asset_number']
    ordering = ['-occurred_at']
    readonly_fields = ['reported_at', 'resolution_time_display']

    fieldsets = (
        ('基本信息', {
            'fields': ('device', 'title', 'description', 'fault_code')
        }),
        ('故障分类', {
            'fields': ('severity', 'fault_type')
        }),
        ('时间信息', {
            'fields': ('occurred_at', 'reported_at', 'resolution_time_display')
        }),
        ('人员信息', {
            'fields': ('reported_by', 'assigned_to', 'resolved_by')
        }),
        ('处理信息', {
            'fields': ('status', 'resolution', 'resolved_at', 'maintenance_record')
        }),
    )

    def severity_badge(self, obj):
        """严重程度徽章"""
        colors = {
            'low': '#28a745',
            'medium': '#ffc107',
            'high': '#fd7e14',
            'critical': '#dc3545',
        }
        color = colors.get(obj.severity, '#6c757d')
        return format_html(
            '<span style="background-color: {}; color: white; padding: 2px 8px; '
            'border-radius: 12px; font-size: 12px;">{}</span>',
            color,
            obj.get_severity_display()
        )
    severity_badge.short_description = '严重程度'

    def status_badge(self, obj):
        """状态徽章"""
        colors = {
            'open': '#dc3545',
            'assigned': '#ffc107',
            'in_progress': '#17a2b8',
            'resolved': '#28a745',
            'closed': '#6c757d',
        }
        color = colors.get(obj.status, '#6c757d')
        return format_html(
            '<span style="background-color: {}; color: white; padding: 2px 8px; '
            'border-radius: 12px; font-size: 12px;">{}</span>',
            color,
            obj.get_status_display()
        )
    status_badge.short_description = '状态'

    def resolution_time_display(self, obj):
        """解决时长显示"""
        resolution_time = obj.resolution_time
        if resolution_time:
            days = resolution_time.days
            hours = resolution_time.seconds // 3600
            if days > 0:
                return f"{days}天{hours}小时"
            return f"{hours}小时"
        return "未解决"
    resolution_time_display.short_description = '解决时长'
