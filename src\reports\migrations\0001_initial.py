# Generated by Django 5.2.4 on 2025-08-05 04:51

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Dashboard',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200, verbose_name='仪表板名称')),
                ('description', models.TextField(blank=True, null=True, verbose_name='描述')),
                ('layout', models.JSONField(default=dict, verbose_name='布局配置')),
                ('widgets', models.JSONField(default=list, verbose_name='组件配置')),
                ('auto_refresh', models.BooleanField(default=False, verbose_name='自动刷新')),
                ('refresh_interval', models.PositiveIntegerField(default=300, verbose_name='刷新间隔(秒)')),
                ('is_public', models.BooleanField(default=False, verbose_name='是否公开')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否激活')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('allowed_users', models.ManyToManyField(blank=True, related_name='allowed_dashboards', to=settings.AUTH_USER_MODEL, verbose_name='允许访问的用户')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='创建者')),
            ],
            options={
                'verbose_name': '仪表板',
                'verbose_name_plural': '仪表板',
                'db_table': 'dashboard',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ReportTemplate',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200, verbose_name='模板名称')),
                ('description', models.TextField(blank=True, null=True, verbose_name='模板描述')),
                ('report_type', models.CharField(choices=[('device_usage', '设备使用报表'), ('maintenance_summary', '维护汇总报表'), ('inventory_status', '库存状态报表'), ('cost_analysis', '成本分析报表'), ('fault_analysis', '故障分析报表'), ('custom', '自定义报表')], max_length=50, verbose_name='报表类型')),
                ('config', models.JSONField(default=dict, verbose_name='报表配置')),
                ('filters', models.JSONField(default=dict, verbose_name='过滤条件')),
                ('fields', models.JSONField(default=list, verbose_name='字段配置')),
                ('ordering', models.JSONField(default=list, verbose_name='排序配置')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否激活')),
                ('is_public', models.BooleanField(default=False, verbose_name='是否公开')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='创建者')),
            ],
            options={
                'verbose_name': '报表模板',
                'verbose_name_plural': '报表模板',
                'db_table': 'report_template',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ReportInstance',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=200, verbose_name='报表标题')),
                ('description', models.TextField(blank=True, null=True, verbose_name='报表描述')),
                ('parameters', models.JSONField(default=dict, verbose_name='生成参数')),
                ('data', models.JSONField(default=dict, verbose_name='报表数据')),
                ('total_records', models.PositiveIntegerField(default=0, verbose_name='总记录数')),
                ('status', models.CharField(choices=[('generating', '生成中'), ('completed', '已完成'), ('failed', '生成失败')], default='generating', max_length=20, verbose_name='状态')),
                ('error_message', models.TextField(blank=True, null=True, verbose_name='错误信息')),
                ('file_path', models.CharField(blank=True, max_length=500, null=True, verbose_name='文件路径')),
                ('file_size', models.PositiveIntegerField(blank=True, null=True, verbose_name='文件大小')),
                ('generated_at', models.DateTimeField(auto_now_add=True, verbose_name='生成时间')),
                ('completed_at', models.DateTimeField(blank=True, null=True, verbose_name='完成时间')),
                ('generated_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='生成者')),
                ('template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='instances', to='reports.reporttemplate', verbose_name='报表模板')),
            ],
            options={
                'verbose_name': '报表实例',
                'verbose_name_plural': '报表实例',
                'db_table': 'report_instance',
                'ordering': ['-generated_at'],
            },
        ),
    ]
