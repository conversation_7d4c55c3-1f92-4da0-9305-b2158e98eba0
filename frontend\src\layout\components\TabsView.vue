<template>
  <div class="tabs-container">
    <el-tabs
      v-model="activeTab"
      type="card"
      closable
      @tab-click="handleTabClick"
      @tab-remove="handleTabRemove"
      class="tabs-view"
    >
      <el-tab-pane
        v-for="tab in tabs"
        :key="tab.path"
        :label="tab.title"
        :name="tab.path"
      />
    </el-tabs>
    
    <!-- 右键菜单 -->
    <el-dropdown
      ref="contextMenuRef"
      trigger="contextmenu"
      @command="handleContextMenu"
      class="context-menu"
    >
      <span></span>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item command="refresh">刷新</el-dropdown-item>
          <el-dropdown-item command="close">关闭</el-dropdown-item>
          <el-dropdown-item command="closeOthers">关闭其他</el-dropdown-item>
          <el-dropdown-item command="closeAll">关闭所有</el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

// 响应式数据
const tabs = ref([
  { path: '/dashboard', title: '仪表板' }
])
const activeTab = ref('/dashboard')
const contextMenuRef = ref()

// 监听路由变化，添加新标签
watch(route, (newRoute) => {
  if (newRoute.meta?.title) {
    addTab({
      path: newRoute.path,
      title: newRoute.meta.title
    })
    activeTab.value = newRoute.path
  }
}, { immediate: true })

// 方法
const addTab = (tab) => {
  const existingTab = tabs.value.find(t => t.path === tab.path)
  if (!existingTab) {
    tabs.value.push(tab)
  }
}

const handleTabClick = (tab) => {
  router.push(tab.props.name)
}

const handleTabRemove = (targetPath) => {
  const targetIndex = tabs.value.findIndex(tab => tab.path === targetPath)
  
  if (targetIndex === -1) return
  
  // 如果关闭的是当前激活的标签
  if (targetPath === activeTab.value) {
    // 切换到相邻的标签
    const nextTab = tabs.value[targetIndex + 1] || tabs.value[targetIndex - 1]
    if (nextTab) {
      router.push(nextTab.path)
    }
  }
  
  // 移除标签
  tabs.value.splice(targetIndex, 1)
}

const handleContextMenu = (command) => {
  switch (command) {
    case 'refresh':
      // 刷新当前页面
      router.go(0)
      break
    case 'close':
      handleTabRemove(activeTab.value)
      break
    case 'closeOthers':
      tabs.value = tabs.value.filter(tab => tab.path === activeTab.value)
      break
    case 'closeAll':
      tabs.value = [{ path: '/dashboard', title: '仪表板' }]
      router.push('/dashboard')
      break
  }
}
</script>

<style lang="scss" scoped>
.tabs-container {
  height: 40px;
  padding: 0 20px;
  background-color: var(--el-bg-color);
  
  .tabs-view {
    height: 100%;
    
    :deep(.el-tabs__header) {
      margin: 0;
      border-bottom: none;
      
      .el-tabs__nav {
        border: none;
        
        .el-tabs__item {
          height: 32px;
          line-height: 32px;
          border: 1px solid var(--el-border-color-light);
          border-radius: 4px 4px 0 0;
          margin-right: 4px;
          padding: 0 12px;
          
          &.is-active {
            background-color: var(--el-color-primary-light-9);
            border-color: var(--el-color-primary);
            color: var(--el-color-primary);
          }
          
          &:hover {
            background-color: var(--el-color-primary-light-9);
          }
        }
      }
    }
    
    :deep(.el-tabs__content) {
      display: none;
    }
  }
  
  .context-menu {
    position: absolute;
    top: -9999px;
    left: -9999px;
  }
}
</style>
