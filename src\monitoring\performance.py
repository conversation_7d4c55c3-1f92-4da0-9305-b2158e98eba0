"""
性能监控工具
"""
import time
import os
from functools import wraps
from django.conf import settings
from django.core.cache import cache
from django.db import connection
import logging

try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False

performance_logger = logging.getLogger('performance')


def monitor_performance(func_name=None):
    """性能监控装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            
            # 记录开始时的数据库查询数量
            initial_queries = len(connection.queries)
            
            try:
                result = func(*args, **kwargs)
                success = True
                error = None
            except Exception as e:
                success = False
                error = str(e)
                raise
            finally:
                end_time = time.time()
                duration = end_time - start_time
                
                # 计算数据库查询数量
                query_count = len(connection.queries) - initial_queries
                
                # 记录性能数据
                performance_data = {
                    'function': func_name or func.__name__,
                    'duration': duration,
                    'query_count': query_count,
                    'success': success,
                    'error': error,
                }
                
                performance_logger.info(f"Function performance: {func.__name__}", extra=performance_data)
                
                # 如果执行时间过长，记录警告
                threshold = getattr(settings, 'SLOW_FUNCTION_THRESHOLD', 1.0)
                if duration > threshold:
                    performance_logger.warning(f"Slow function: {func.__name__} took {duration:.3f}s", extra=performance_data)
            
            return result
        return wrapper
    return decorator


class SystemMonitor:
    """系统监控类"""
    
    @staticmethod
    def get_system_stats():
        """获取系统统计信息"""
        if not PSUTIL_AVAILABLE:
            return {
                'error': 'psutil not available',
                'timestamp': time.time(),
            }

        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)

            # 内存使用情况
            memory = psutil.virtual_memory()

            # 磁盘使用情况
            disk = psutil.disk_usage('/')

            # 进程信息
            process = psutil.Process(os.getpid())
            process_memory = process.memory_info()

            return {
                'cpu': {
                    'percent': cpu_percent,
                    'count': psutil.cpu_count(),
                },
                'memory': {
                    'total': memory.total,
                    'available': memory.available,
                    'percent': memory.percent,
                    'used': memory.used,
                },
                'disk': {
                    'total': disk.total,
                    'used': disk.used,
                    'free': disk.free,
                    'percent': (disk.used / disk.total) * 100,
                },
                'process': {
                    'memory_rss': process_memory.rss,
                    'memory_vms': process_memory.vms,
                    'cpu_percent': process.cpu_percent(),
                    'num_threads': process.num_threads(),
                },
                'timestamp': time.time(),
            }
        except Exception as e:
            performance_logger.error(f"Error getting system stats: {e}")
            return None
    
    @staticmethod
    def get_database_stats():
        """获取数据库统计信息"""
        try:
            from django.db import connections
            
            stats = {}
            for alias in connections:
                conn = connections[alias]
                stats[alias] = {
                    'queries_count': len(connection.queries),
                    'vendor': conn.vendor,
                }
            
            return stats
        except Exception as e:
            performance_logger.error(f"Error getting database stats: {e}")
            return None
    
    @staticmethod
    def get_cache_stats():
        """获取缓存统计信息"""
        try:
            # 这里可以根据使用的缓存后端获取统计信息
            # 对于Redis，可以使用 cache._cache.get_client().info()
            return {
                'backend': cache.__class__.__name__,
                'location': getattr(cache, '_servers', 'Unknown'),
            }
        except Exception as e:
            performance_logger.error(f"Error getting cache stats: {e}")
            return None


class PerformanceCollector:
    """性能数据收集器"""
    
    def __init__(self):
        self.metrics = []
    
    def collect_metrics(self):
        """收集性能指标"""
        system_stats = SystemMonitor.get_system_stats()
        database_stats = SystemMonitor.get_database_stats()
        cache_stats = SystemMonitor.get_cache_stats()
        
        metrics = {
            'timestamp': time.time(),
            'system': system_stats,
            'database': database_stats,
            'cache': cache_stats,
        }
        
        self.metrics.append(metrics)
        
        # 只保留最近的1000条记录
        if len(self.metrics) > 1000:
            self.metrics = self.metrics[-1000:]
        
        # 记录到日志
        performance_logger.info("System metrics collected", extra=metrics)
        
        return metrics
    
    def get_recent_metrics(self, count=10):
        """获取最近的性能指标"""
        return self.metrics[-count:] if self.metrics else []
    
    def get_average_metrics(self, minutes=5):
        """获取指定时间内的平均指标"""
        cutoff_time = time.time() - (minutes * 60)
        recent_metrics = [m for m in self.metrics if m['timestamp'] > cutoff_time]
        
        if not recent_metrics:
            return None
        
        # 计算平均值
        avg_cpu = sum(m['system']['cpu']['percent'] for m in recent_metrics if m['system']) / len(recent_metrics)
        avg_memory = sum(m['system']['memory']['percent'] for m in recent_metrics if m['system']) / len(recent_metrics)
        
        return {
            'period_minutes': minutes,
            'sample_count': len(recent_metrics),
            'avg_cpu_percent': avg_cpu,
            'avg_memory_percent': avg_memory,
        }


# 全局性能收集器实例
performance_collector = PerformanceCollector()


def start_performance_monitoring():
    """启动性能监控"""
    import threading
    import time
    
    def monitor_loop():
        while True:
            try:
                performance_collector.collect_metrics()
                time.sleep(60)  # 每分钟收集一次
            except Exception as e:
                performance_logger.error(f"Error in performance monitoring loop: {e}")
                time.sleep(60)
    
    # 在后台线程中运行监控
    monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
    monitor_thread.start()
    
    performance_logger.info("Performance monitoring started")


class DatabaseQueryAnalyzer:
    """数据库查询分析器"""
    
    @staticmethod
    def analyze_slow_queries(threshold=0.1):
        """分析慢查询"""
        slow_queries = []
        
        for query in connection.queries:
            duration = float(query['time'])
            if duration > threshold:
                slow_queries.append({
                    'sql': query['sql'],
                    'duration': duration,
                })
        
        if slow_queries:
            performance_logger.warning(f"Found {len(slow_queries)} slow queries", extra={
                'slow_queries': slow_queries,
                'threshold': threshold,
            })
        
        return slow_queries
    
    @staticmethod
    def get_query_stats():
        """获取查询统计"""
        total_queries = len(connection.queries)
        total_time = sum(float(q['time']) for q in connection.queries)
        
        return {
            'total_queries': total_queries,
            'total_time': total_time,
            'average_time': total_time / total_queries if total_queries > 0 else 0,
        }
