<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>医疗设备管理系统 - 现代化界面</title>
    
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- Element Plus JS -->
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <!-- Element Plus Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>
    
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f7fa;
        }
        
        .modern-layout {
            display: flex;
            height: 100vh;
        }
        
        .sidebar {
            width: 200px;
            background: #ffffff;
            border-right: 1px solid #e4e7ed;
            box-shadow: 2px 0 6px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
        }
        
        .logo-area {
            height: 60px;
            display: flex;
            align-items: center;
            padding: 0 20px;
            border-bottom: 1px solid #e4e7ed;
            font-size: 18px;
            font-weight: 600;
            color: #409EFF;
        }
        
        .nav-menu {
            flex: 1;
            padding: 20px 0;
        }
        
        .nav-item {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: #606266;
            text-decoration: none;
            transition: all 0.3s;
            border-left: 3px solid transparent;
            cursor: pointer;
        }
        
        .nav-item:hover,
        .nav-item.active {
            background-color: #f0f9ff;
            color: #409EFF;
            border-left-color: #409EFF;
        }
        
        .nav-item i {
            margin-right: 8px;
            width: 16px;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            height: 60px;
            background: #ffffff;
            border-bottom: 1px solid #e4e7ed;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .content-area {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
            padding: 24px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .stat-card.success {
            background: linear-gradient(135deg, #67C23A 0%, #85ce61 100%);
        }
        
        .stat-card.warning {
            background: linear-gradient(135deg, #E6A23C 0%, #ebb563 100%);
        }
        
        .stat-card.danger {
            background: linear-gradient(135deg, #F56C6C 0%, #f78989 100%);
        }
        
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            transform: translate(30px, -30px);
        }
        
        .stat-value {
            font-size: 32px;
            font-weight: 600;
            margin-bottom: 8px;
            position: relative;
            z-index: 1;
        }
        
        .stat-label {
            font-size: 14px;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }
        
        .chart-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .chart-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .chart-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #303133;
        }
        
        .quick-actions {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .action-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 16px;
            margin-top: 20px;
        }
        
        .action-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            text-decoration: none;
            color: #495057;
            transition: all 0.3s;
            cursor: pointer;
        }
        
        .action-btn:hover {
            background: #409EFF;
            color: white;
            border-color: #409EFF;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
        }
        
        .action-icon {
            font-size: 32px;
            margin-bottom: 8px;
        }
        
        .action-text {
            font-size: 14px;
            font-weight: 500;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #409EFF;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }
        
        @media (max-width: 768px) {
            .modern-layout {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                height: auto;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .chart-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="modern-layout">
            <!-- 侧边栏 -->
            <div class="sidebar">
                <div class="logo-area">
                    <i class="el-icon-monitor" style="margin-right: 8px;"></i>
                    医疗设备管理
                </div>
                <div class="nav-menu">
                    <div class="nav-item active" onclick="switchView('dashboard')">
                        <i class="el-icon-data-board"></i>
                        仪表板
                    </div>
                    <div class="nav-item" onclick="switchView('devices')">
                        <i class="el-icon-monitor"></i>
                        设备管理
                    </div>
                    <div class="nav-item" onclick="switchView('maintenance')">
                        <i class="el-icon-tools"></i>
                        维护管理
                    </div>
                    <div class="nav-item" onclick="switchView('inventory')">
                        <i class="el-icon-box"></i>
                        库存管理
                    </div>
                    <div class="nav-item" onclick="switchView('reports')">
                        <i class="el-icon-data-analysis"></i>
                        报表分析
                    </div>
                    <div class="nav-item" onclick="switchView('settings')">
                        <i class="el-icon-setting"></i>
                        系统设置
                    </div>
                </div>
            </div>
            
            <!-- 主内容区 -->
            <div class="main-content">
                <!-- 顶部导航栏 -->
                <div class="header">
                    <div>
                        <h3 style="margin: 0; color: #303133;" id="page-title">仪表板</h3>
                    </div>
                    <div class="user-info">
                        <span style="color: #606266;">欢迎回来</span>
                        <div class="user-avatar">管</div>
                        <span style="color: #303133; font-weight: 500;">管理员</span>
                    </div>
                </div>
                
                <!-- 内容区域 -->
                <div class="content-area">
                    <!-- 仪表板视图 -->
                    <div id="dashboard-view">
                        <!-- 统计卡片 -->
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-value">156</div>
                                <div class="stat-label">设备总数</div>
                            </div>
                            <div class="stat-card success">
                                <div class="stat-value">142</div>
                                <div class="stat-label">正常设备</div>
                            </div>
                            <div class="stat-card warning">
                                <div class="stat-value">8</div>
                                <div class="stat-label">维护中</div>
                            </div>
                            <div class="stat-card danger">
                                <div class="stat-value">6</div>
                                <div class="stat-label">故障设备</div>
                            </div>
                        </div>
                        
                        <!-- 图表区域 -->
                        <div class="chart-grid">
                            <div class="chart-card">
                                <div class="chart-title">设备状态分布</div>
                                <div style="height: 300px; background: #f8f9fa; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #909399;">
                                    图表区域 (需要集成 ECharts)
                                </div>
                            </div>
                            <div class="chart-card">
                                <div class="chart-title">维护趋势</div>
                                <div style="height: 300px; background: #f8f9fa; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #909399;">
                                    图表区域 (需要集成 ECharts)
                                </div>
                            </div>
                        </div>
                        
                        <!-- 快速操作 -->
                        <div class="quick-actions">
                            <div class="chart-title">快速操作</div>
                            <div class="action-grid">
                                <div class="action-btn" onclick="showMessage('添加设备')">
                                    <div class="action-icon">➕</div>
                                    <div class="action-text">添加设备</div>
                                </div>
                                <div class="action-btn" onclick="showMessage('故障报告')">
                                    <div class="action-icon">⚠️</div>
                                    <div class="action-text">故障报告</div>
                                </div>
                                <div class="action-btn" onclick="showMessage('维护计划')">
                                    <div class="action-icon">📅</div>
                                    <div class="action-text">维护计划</div>
                                </div>
                                <div class="action-btn" onclick="showMessage('库存盘点')">
                                    <div class="action-icon">📦</div>
                                    <div class="action-text">库存盘点</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 其他视图 -->
                    <div id="other-views" style="display: none;">
                        <div style="text-align: center; padding: 60px; color: #909399;">
                            <div style="font-size: 48px; margin-bottom: 20px;">🚧</div>
                            <h3 id="other-view-title">功能开发中...</h3>
                            <p>完整的Vue.js前端正在开发中，敬请期待！</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    <script>
        // 当前视图状态
        let currentView = 'dashboard';

        // 页面标题映射
        const titles = {
            dashboard: '仪表板',
            devices: '设备管理',
            maintenance: '维护管理',
            inventory: '库存管理',
            reports: '报表分析',
            settings: '系统设置'
        };

        // 切换视图函数
        function switchView(view) {
            currentView = view;

            // 更新页面标题
            document.getElementById('page-title').textContent = titles[view] || '仪表板';

            // 更新导航菜单激活状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.closest('.nav-item').classList.add('active');

            // 显示/隐藏视图
            if (view === 'dashboard') {
                document.getElementById('dashboard-view').style.display = 'block';
                document.getElementById('other-views').style.display = 'none';
            } else {
                document.getElementById('dashboard-view').style.display = 'none';
                document.getElementById('other-views').style.display = 'block';
                document.getElementById('other-view-title').textContent = titles[view] + '功能开发中...';
            }
        }

        // 显示消息函数
        function showMessage(action) {
            // 使用Element Plus的消息组件
            if (typeof ElementPlus !== 'undefined' && ElementPlus.ElMessage) {
                ElementPlus.ElMessage.success(`${action}功能即将开放！`);
            } else {
                // 降级到原生alert
                alert(`${action}功能即将开放！`);
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('医疗设备管理系统 - 现代化界面已加载');
        });
    </script>
</body>
</html>
