<template>
  <div class="device-list-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">设备管理</h2>
        <p class="page-desc">管理所有医疗设备的基本信息和状态</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showAddDialog">
          <el-icon><Plus /></el-icon>
          添加设备
        </el-button>
      </div>
    </div>
    
    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="设备名称">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入设备名称"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        
        <el-form-item label="设备分类">
          <el-select
            v-model="searchForm.category"
            placeholder="请选择分类"
            clearable
          >
            <el-option
              v-for="category in categories"
              :key="category.id"
              :label="category.name"
              :value="category.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="设备状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
          >
            <el-option label="正常" value="normal" />
            <el-option label="维护中" value="maintenance" />
            <el-option label="故障" value="fault" />
            <el-option label="闲置" value="idle" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="位置">
          <el-select
            v-model="searchForm.location"
            placeholder="请选择位置"
            clearable
          >
            <el-option
              v-for="location in locations"
              :key="location.id"
              :label="location.name"
              :value="location.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 设备列表 -->
    <el-card class="table-card">
      <template #header>
        <div class="flex-between">
          <span>设备列表 ({{ pagination.total }})</span>
          <div class="table-actions">
            <el-button type="text" @click="handleExport">
              <el-icon><Download /></el-icon>
              导出
            </el-button>
            <el-button type="text" @click="handleRefresh">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>
      
      <el-table
        v-loading="loading"
        :data="deviceList"
        stripe
        @selection-change="handleSelectionChange"
        class="device-table"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column prop="asset_number" label="资产编号" width="120" />
        
        <el-table-column prop="name" label="设备名称" min-width="150">
          <template #default="{ row }">
            <el-link type="primary" @click="showDeviceDetail(row)">
              {{ row.name }}
            </el-link>
          </template>
        </el-table-column>
        
        <el-table-column prop="category_name" label="分类" width="120" />
        
        <el-table-column prop="manufacturer_name" label="制造商" width="120" />
        
        <el-table-column prop="model_name" label="型号" width="120" />
        
        <el-table-column prop="location_name" label="位置" width="120" />
        
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="purchase_date" label="采购日期" width="120" />
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="showEditDialog(row)">
              编辑
            </el-button>
            <el-button type="text" size="small" @click="showMaintenanceDialog(row)">
              维护
            </el-button>
            <el-dropdown @command="(cmd) => handleMoreAction(cmd, row)">
              <el-button type="text" size="small">
                更多<el-icon><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="detail">查看详情</el-dropdown-item>
                  <el-dropdown-item command="history">状态历史</el-dropdown-item>
                  <el-dropdown-item command="qrcode">二维码</el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </el-card>
    
    <!-- 添加/编辑设备对话框 -->
    <DeviceDialog
      v-model="dialogVisible"
      :device="currentDevice"
      :categories="categories"
      :manufacturers="manufacturers"
      :locations="locations"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { devicesApi } from '@/api/devices'
import DeviceDialog from './components/DeviceDialog.vue'

// 响应式数据
const loading = ref(false)
const dialogVisible = ref(false)
const currentDevice = ref(null)
const selectedDevices = ref([])

const searchForm = reactive({
  name: '',
  category: '',
  status: '',
  location: ''
})

const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

const deviceList = ref([])
const categories = ref([])
const manufacturers = ref([])
const locations = ref([])

// 方法
const loadDevices = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      page_size: pagination.size,
      search: searchForm.name,
      category: searchForm.category,
      status: searchForm.status,
      location: searchForm.location
    }
    
    const response = await devicesApi.devices.list(params)
    deviceList.value = response.data.results
    pagination.total = response.data.count
  } catch (error) {
    ElMessage.error('加载设备列表失败')
  } finally {
    loading.value = false
  }
}

const loadCategories = async () => {
  try {
    const response = await devicesApi.categories.list()
    categories.value = response.data.results
  } catch (error) {
    console.error('Failed to load categories:', error)
  }
}

const loadManufacturers = async () => {
  try {
    const response = await devicesApi.manufacturers.list()
    manufacturers.value = response.data.results
  } catch (error) {
    console.error('Failed to load manufacturers:', error)
  }
}

const loadLocations = async () => {
  try {
    const response = await devicesApi.locations.list()
    locations.value = response.data.results
  } catch (error) {
    console.error('Failed to load locations:', error)
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadDevices()
}

const handleReset = () => {
  Object.assign(searchForm, {
    name: '',
    category: '',
    status: '',
    location: ''
  })
  handleSearch()
}

const handleRefresh = () => {
  loadDevices()
}

const handlePageChange = (page) => {
  pagination.page = page
  loadDevices()
}

const handleSizeChange = (size) => {
  pagination.size = size
  pagination.page = 1
  loadDevices()
}

const handleSelectionChange = (selection) => {
  selectedDevices.value = selection
}

const showAddDialog = () => {
  currentDevice.value = null
  dialogVisible.value = true
}

const showEditDialog = (device) => {
  currentDevice.value = { ...device }
  dialogVisible.value = true
}

const showDeviceDetail = (device) => {
  // 跳转到设备详情页面
  console.log('Show device detail:', device)
}

const showMaintenanceDialog = (device) => {
  // 显示维护对话框
  console.log('Show maintenance dialog:', device)
}

const handleMoreAction = async (command, device) => {
  switch (command) {
    case 'detail':
      showDeviceDetail(device)
      break
    case 'history':
      // 显示状态历史
      break
    case 'qrcode':
      // 显示二维码
      break
    case 'delete':
      try {
        await ElMessageBox.confirm('确定要删除这个设备吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        await devicesApi.devices.delete(device.id)
        ElMessage.success('删除成功')
        loadDevices()
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('删除失败')
        }
      }
      break
  }
}

const handleDialogSuccess = () => {
  dialogVisible.value = false
  loadDevices()
}

const handleExport = () => {
  // 导出功能
  ElMessage.info('导出功能开发中...')
}

const getStatusType = (status) => {
  const statusMap = {
    normal: 'success',
    maintenance: 'warning',
    fault: 'danger',
    idle: 'info'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    normal: '正常',
    maintenance: '维护中',
    fault: '故障',
    idle: '闲置'
  }
  return statusMap[status] || '未知'
}

// 生命周期
onMounted(() => {
  loadDevices()
  loadCategories()
  loadManufacturers()
  loadLocations()
})
</script>

<style lang="scss" scoped>
.device-list-container {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    
    .header-left {
      .page-title {
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 8px;
        color: var(--el-text-color-primary);
      }
      
      .page-desc {
        font-size: 14px;
        color: var(--el-text-color-secondary);
        margin: 0;
      }
    }
  }
  
  .search-card {
    margin-bottom: 20px;
    
    .search-form {
      .el-form-item {
        margin-bottom: 0;
      }
    }
  }
  
  .table-card {
    .table-actions {
      display: flex;
      gap: 8px;
    }
    
    .device-table {
      margin-bottom: 20px;
    }
    
    .pagination-container {
      display: flex;
      justify-content: center;
    }
  }
}

@media (max-width: 768px) {
  .device-list-container {
    .page-header {
      flex-direction: column;
      gap: 16px;
    }
    
    .search-form {
      .el-form-item {
        width: 100%;
        margin-right: 0;
      }
    }
  }
}
</style>
