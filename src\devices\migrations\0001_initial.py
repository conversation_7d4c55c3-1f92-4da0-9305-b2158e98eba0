# Generated by Django 5.2.4 on 2025-08-05 04:31

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='DeviceManufacturer',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200, unique=True, verbose_name='制造商名称')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='制造商代码')),
                ('description', models.TextField(blank=True, null=True, verbose_name='描述')),
                ('contact_person', models.CharField(blank=True, max_length=100, null=True, verbose_name='联系人')),
                ('phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='电话')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='邮箱')),
                ('address', models.TextField(blank=True, null=True, verbose_name='地址')),
                ('website', models.URLField(blank=True, null=True, verbose_name='网站')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否激活')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '设备制造商',
                'verbose_name_plural': '设备制造商',
                'db_table': 'device_manufacturer',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Device',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('asset_number', models.CharField(max_length=50, unique=True, verbose_name='资产编号')),
                ('serial_number', models.CharField(blank=True, max_length=100, null=True, verbose_name='序列号')),
                ('name', models.CharField(max_length=200, verbose_name='设备名称')),
                ('department', models.CharField(blank=True, max_length=100, null=True, verbose_name='所属科室')),
                ('location', models.CharField(blank=True, max_length=200, null=True, verbose_name='具体位置')),
                ('room', models.CharField(blank=True, max_length=50, null=True, verbose_name='房间号')),
                ('purchase_date', models.DateField(blank=True, null=True, verbose_name='采购日期')),
                ('purchase_price', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True, verbose_name='采购价格')),
                ('supplier', models.CharField(blank=True, max_length=200, null=True, verbose_name='供应商')),
                ('warranty_start_date', models.DateField(blank=True, null=True, verbose_name='保修开始日期')),
                ('warranty_end_date', models.DateField(blank=True, null=True, verbose_name='保修结束日期')),
                ('status', models.CharField(choices=[('normal', '正常'), ('maintenance', '维护中'), ('repair', '维修中'), ('fault', '故障'), ('retired', '报废'), ('idle', '闲置')], default='normal', max_length=20, verbose_name='设备状态')),
                ('usage_status', models.CharField(choices=[('in_use', '使用中'), ('available', '可用'), ('unavailable', '不可用'), ('reserved', '预约中')], default='available', max_length=20, verbose_name='使用状态')),
                ('importance', models.CharField(choices=[('critical', '关键'), ('important', '重要'), ('normal', '一般'), ('low', '较低')], default='normal', max_length=20, verbose_name='重要程度')),
                ('parameters', models.JSONField(blank=True, default=dict, verbose_name='设备参数')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='备注')),
                ('last_check_date', models.DateTimeField(blank=True, null=True, verbose_name='最后检查时间')),
                ('last_check_result', models.TextField(blank=True, null=True, verbose_name='最后检查结果')),
                ('next_check_date', models.DateField(blank=True, null=True, verbose_name='下次检查日期')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否激活')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_devices', to=settings.AUTH_USER_MODEL, verbose_name='创建者')),
                ('operator', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='operated_devices', to=settings.AUTH_USER_MODEL, verbose_name='操作员')),
                ('responsible_person', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='responsible_devices', to=settings.AUTH_USER_MODEL, verbose_name='责任人')),
            ],
            options={
                'verbose_name': '医疗设备',
                'verbose_name_plural': '医疗设备',
                'db_table': 'device',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DeviceAttachment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200, verbose_name='附件名称')),
                ('description', models.TextField(blank=True, null=True, verbose_name='描述')),
                ('file', models.FileField(upload_to='device_attachments/', verbose_name='文件')),
                ('file_size', models.PositiveIntegerField(blank=True, null=True, verbose_name='文件大小')),
                ('attachment_type', models.CharField(choices=[('manual', '使用手册'), ('certificate', '合格证书'), ('warranty', '保修单'), ('photo', '照片'), ('report', '检测报告'), ('other', '其他')], default='other', max_length=20, verbose_name='附件类型')),
                ('uploaded_at', models.DateTimeField(auto_now_add=True, verbose_name='上传时间')),
                ('device', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attachments', to='devices.device', verbose_name='设备')),
                ('uploaded_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='上传者')),
            ],
            options={
                'verbose_name': '设备附件',
                'verbose_name_plural': '设备附件',
                'db_table': 'device_attachment',
                'ordering': ['-uploaded_at'],
            },
        ),
        migrations.CreateModel(
            name='DeviceCategory',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='分类名称')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='分类代码')),
                ('description', models.TextField(blank=True, null=True, verbose_name='分类描述')),
                ('icon', models.CharField(blank=True, max_length=50, null=True, verbose_name='图标')),
                ('color', models.CharField(default='#007bff', max_length=7, verbose_name='颜色')),
                ('sort_order', models.PositiveIntegerField(default=0, verbose_name='排序')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否激活')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='创建者')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='devices.devicecategory', verbose_name='父分类')),
            ],
            options={
                'verbose_name': '设备分类',
                'verbose_name_plural': '设备分类',
                'db_table': 'device_category',
                'ordering': ['sort_order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='DeviceLocation',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='位置名称')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='位置代码')),
                ('description', models.TextField(blank=True, null=True, verbose_name='描述')),
                ('location_type', models.CharField(choices=[('building', '建筑'), ('floor', '楼层'), ('department', '科室'), ('room', '房间'), ('area', '区域')], default='room', max_length=20, verbose_name='位置类型')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否激活')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('manager', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='负责人')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='devices.devicelocation', verbose_name='父位置')),
            ],
            options={
                'verbose_name': '设备位置',
                'verbose_name_plural': '设备位置',
                'db_table': 'device_location',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='DeviceModel',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200, verbose_name='型号名称')),
                ('model_number', models.CharField(max_length=100, verbose_name='型号编号')),
                ('specifications', models.JSONField(blank=True, default=dict, verbose_name='技术规格')),
                ('description', models.TextField(blank=True, null=True, verbose_name='描述')),
                ('image', models.ImageField(blank=True, null=True, upload_to='device_models/', verbose_name='产品图片')),
                ('manual_url', models.URLField(blank=True, null=True, verbose_name='说明书链接')),
                ('purchase_price', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True, verbose_name='采购价格')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否激活')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='devices.devicecategory', verbose_name='设备分类')),
                ('manufacturer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='devices.devicemanufacturer', verbose_name='制造商')),
            ],
            options={
                'verbose_name': '设备型号',
                'verbose_name_plural': '设备型号',
                'db_table': 'device_model',
                'ordering': ['manufacturer__name', 'name'],
                'unique_together': {('manufacturer', 'model_number')},
            },
        ),
        migrations.AddField(
            model_name='device',
            name='device_model',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='devices.devicemodel', verbose_name='设备型号'),
        ),
        migrations.CreateModel(
            name='DeviceStatusHistory',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('old_status', models.CharField(max_length=20, verbose_name='原状态')),
                ('new_status', models.CharField(max_length=20, verbose_name='新状态')),
                ('old_usage_status', models.CharField(blank=True, max_length=20, null=True, verbose_name='原使用状态')),
                ('new_usage_status', models.CharField(blank=True, max_length=20, null=True, verbose_name='新使用状态')),
                ('reason', models.CharField(max_length=200, verbose_name='变更原因')),
                ('description', models.TextField(blank=True, null=True, verbose_name='详细描述')),
                ('changed_at', models.DateTimeField(auto_now_add=True, verbose_name='变更时间')),
                ('changed_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='操作人员')),
                ('device', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='status_history', to='devices.device', verbose_name='设备')),
            ],
            options={
                'verbose_name': '设备状态历史',
                'verbose_name_plural': '设备状态历史',
                'db_table': 'device_status_history',
                'ordering': ['-changed_at'],
            },
        ),
    ]
