"""
库存模块视图
"""
from rest_framework import viewsets, permissions, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db.models import Sum, Count, Q
from django.utils import timezone
from decimal import Decimal

from .models import Supplier, ItemCategory, Item, StockMovement, PurchaseOrder, PurchaseOrderItem
from .serializers import (
    SupplierSerializer, ItemCategorySerializer, ItemSerializer, ItemListSerializer,
    StockMovementSerializer, PurchaseOrderSerializer, PurchaseOrderListSerializer,
    PurchaseOrderItemSerializer, InventoryStatsSerializer
)


class SupplierViewSet(viewsets.ModelViewSet):
    """供应商视图集"""
    queryset = Supplier.objects.filter(is_active=True)
    serializer_class = SupplierSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'code', 'contact_person']
    ordering_fields = ['name', 'rating', 'created_at']
    ordering = ['name']

    def get_permissions(self):
        """根据动作设置权限"""
        if self.action in ['list', 'retrieve']:
            permission_classes = [permissions.IsAuthenticated]
        else:
            permission_classes = [permissions.IsAdminUser]
        return [permission() for permission in permission_classes]


class ItemCategoryViewSet(viewsets.ModelViewSet):
    """物品分类视图集"""
    queryset = ItemCategory.objects.filter(is_active=True)
    serializer_class = ItemCategorySerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'code', 'description']
    ordering_fields = ['name', 'sort_order', 'created_at']
    ordering = ['sort_order', 'name']

    def get_permissions(self):
        """根据动作设置权限"""
        if self.action in ['list', 'retrieve', 'tree']:
            permission_classes = [permissions.IsAuthenticated]
        else:
            permission_classes = [permissions.IsAdminUser]
        return [permission() for permission in permission_classes]

    @action(detail=False, methods=['get'])
    def tree(self, request):
        """获取分类树"""
        def build_tree(parent=None):
            categories = ItemCategory.objects.filter(
                parent=parent,
                is_active=True
            ).order_by('sort_order', 'name')

            tree = []
            for category in categories:
                node = ItemCategorySerializer(category).data
                node['children'] = build_tree(category)
                tree.append(node)
            return tree

        tree = build_tree()
        return Response(tree)


class ItemViewSet(viewsets.ModelViewSet):
    """物品视图集"""
    queryset = Item.objects.filter(is_active=True)
    serializer_class = ItemSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'code', 'brand', 'model']
    ordering_fields = ['name', 'code', 'current_stock', 'created_at']
    ordering = ['name']

    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'list':
            return ItemListSerializer
        return ItemSerializer

    def get_queryset(self):
        """获取查询集"""
        queryset = super().get_queryset()

        # 分类过滤
        category = self.request.query_params.get('category')
        if category:
            queryset = queryset.filter(category__id=category)

        # 物品类型过滤
        item_type = self.request.query_params.get('item_type')
        if item_type:
            queryset = queryset.filter(item_type=item_type)

        # 库存状态过滤
        stock_status = self.request.query_params.get('stock_status')
        if stock_status == 'low_stock':
            # 这里需要在数据库层面实现，暂时用Python过滤
            low_stock_items = []
            for item in queryset:
                if item.is_low_stock:
                    low_stock_items.append(item.id)
            queryset = queryset.filter(id__in=low_stock_items)

        return queryset.select_related('category', 'created_by')

    def perform_create(self, serializer):
        """创建时设置创建者"""
        serializer.save(created_by=self.request.user)

    @action(detail=False, methods=['get'])
    def low_stock(self, request):
        """获取低库存物品"""
        items = []
        for item in self.get_queryset():
            if item.is_low_stock:
                items.append(item)

        serializer = ItemListSerializer(items, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def adjust_stock(self, request, pk=None):
        """调整库存"""
        item = self.get_object()
        quantity = request.data.get('quantity', 0)
        notes = request.data.get('notes', '')

        if quantity == 0:
            return Response(
                {'error': '调整数量不能为0'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 创建库存变动记录
        movement = StockMovement.objects.create(
            item=item,
            movement_type='adjustment',
            quantity=quantity,
            notes=notes,
            created_by=request.user
        )

        return Response({
            'message': '库存调整成功',
            'current_stock': item.current_stock,
            'movement': StockMovementSerializer(movement).data
        })


class StockMovementViewSet(viewsets.ModelViewSet):
    """库存变动视图集"""
    queryset = StockMovement.objects.all()
    serializer_class = StockMovementSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['item__name', 'item__code', 'reference_number']
    ordering_fields = ['created_at', 'movement_type', 'quantity']
    ordering = ['-created_at']

    def get_queryset(self):
        """获取查询集"""
        queryset = super().get_queryset()

        # 物品过滤
        item = self.request.query_params.get('item')
        if item:
            queryset = queryset.filter(item__id=item)

        # 变动类型过滤
        movement_type = self.request.query_params.get('movement_type')
        if movement_type:
            queryset = queryset.filter(movement_type=movement_type)

        # 供应商过滤
        supplier = self.request.query_params.get('supplier')
        if supplier:
            queryset = queryset.filter(supplier__id=supplier)

        # 日期范围过滤
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        if start_date:
            queryset = queryset.filter(created_at__gte=start_date)
        if end_date:
            queryset = queryset.filter(created_at__lte=end_date)

        return queryset.select_related('item', 'supplier', 'created_by')

    def perform_create(self, serializer):
        """创建时设置创建者"""
        serializer.save(created_by=self.request.user)


class PurchaseOrderViewSet(viewsets.ModelViewSet):
    """采购订单视图集"""
    queryset = PurchaseOrder.objects.all()
    serializer_class = PurchaseOrderSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['order_number', 'supplier__name']
    ordering_fields = ['order_date', 'status', 'total_amount', 'created_at']
    ordering = ['-created_at']

    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'list':
            return PurchaseOrderListSerializer
        return PurchaseOrderSerializer

    def get_queryset(self):
        """获取查询集"""
        queryset = super().get_queryset()

        # 供应商过滤
        supplier = self.request.query_params.get('supplier')
        if supplier:
            queryset = queryset.filter(supplier__id=supplier)

        # 状态过滤
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        return queryset.select_related('supplier', 'created_by', 'approved_by')

    def perform_create(self, serializer):
        """创建时设置创建者"""
        serializer.save(created_by=self.request.user)

    @action(detail=True, methods=['post'])
    def add_item(self, request, pk=None):
        """添加采购明细"""
        order = self.get_object()

        if order.status not in ['draft', 'submitted']:
            return Response(
                {'error': '只能在草稿或已提交状态下添加明细'},
                status=status.HTTP_400_BAD_REQUEST
            )

        serializer = PurchaseOrderItemSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save(purchase_order=order)

            # 更新订单总金额
            total = order.items.aggregate(
                total=Sum('total_price')
            )['total'] or Decimal('0.00')
            order.total_amount = total
            order.save()

            return Response({
                'message': '明细添加成功',
                'item': serializer.data,
                'order_total': order.total_amount
            })

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'])
    def submit(self, request, pk=None):
        """提交订单"""
        order = self.get_object()

        if order.status != 'draft':
            return Response(
                {'error': '只能提交草稿状态的订单'},
                status=status.HTTP_400_BAD_REQUEST
            )

        if not order.items.exists():
            return Response(
                {'error': '订单必须包含至少一个明细'},
                status=status.HTTP_400_BAD_REQUEST
            )

        order.status = 'submitted'
        order.save()

        return Response({
            'message': '订单提交成功',
            'order': PurchaseOrderSerializer(order).data
        })

    @action(detail=True, methods=['post'])
    def receive(self, request, pk=None):
        """收货"""
        order = self.get_object()
        received_items = request.data.get('items', [])

        if order.status not in ['confirmed', 'partial_received']:
            return Response(
                {'error': '只能对已确认的订单进行收货'},
                status=status.HTTP_400_BAD_REQUEST
            )

        for item_data in received_items:
            item_id = item_data.get('item_id')
            received_qty = item_data.get('received_quantity', 0)

            try:
                order_item = order.items.get(item__id=item_id)
                if received_qty > order_item.remaining_quantity:
                    return Response(
                        {'error': f'收货数量不能超过剩余数量'},
                        status=status.HTTP_400_BAD_REQUEST
                    )

                # 更新收货数量
                order_item.received_quantity += received_qty
                order_item.save()

                # 创建入库记录
                StockMovement.objects.create(
                    item=order_item.item,
                    movement_type='in',
                    quantity=received_qty,
                    unit_price=order_item.unit_price,
                    reference_number=order.order_number,
                    supplier=order.supplier,
                    notes=f'采购订单收货: {order.order_number}',
                    created_by=request.user
                )

            except PurchaseOrderItem.DoesNotExist:
                continue

        # 检查是否完全收货
        all_received = all(item.is_fully_received for item in order.items.all())
        if all_received:
            order.status = 'received'
        else:
            order.status = 'partial_received'

        order.save()

        return Response({
            'message': '收货成功',
            'order': PurchaseOrderSerializer(order).data
        })
