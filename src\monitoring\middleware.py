"""
监控中间件
"""
import time
import logging
from django.utils.deprecation import MiddlewareMixin
from django.conf import settings
from django.http import JsonResponse
import json

# 配置日志记录器
logger = logging.getLogger('monitoring')
performance_logger = logging.getLogger('performance')
api_logger = logging.getLogger('api')


class RequestLoggingMiddleware(MiddlewareMixin):
    """请求日志中间件"""
    
    def process_request(self, request):
        """处理请求开始"""
        request.start_time = time.time()
        
        # 记录请求信息
        logger.info(f"Request started: {request.method} {request.path}")
        
        # 记录API请求详细信息
        if request.path.startswith('/api/'):
            api_logger.info(f"API Request: {request.method} {request.path}", extra={
                'method': request.method,
                'path': request.path,
                'user': str(request.user) if hasattr(request, 'user') and request.user.is_authenticated else 'Anonymous',
                'ip': self.get_client_ip(request),
                'user_agent': request.META.get('HTTP_USER_AGENT', ''),
            })
    
    def process_response(self, request, response):
        """处理响应"""
        if hasattr(request, 'start_time'):
            duration = time.time() - request.start_time
            
            # 记录响应信息
            logger.info(f"Request completed: {request.method} {request.path} - {response.status_code} ({duration:.3f}s)")
            
            # 记录性能信息
            if duration > getattr(settings, 'SLOW_REQUEST_THRESHOLD', 1.0):
                performance_logger.warning(f"Slow request: {request.method} {request.path} took {duration:.3f}s", extra={
                    'method': request.method,
                    'path': request.path,
                    'duration': duration,
                    'status_code': response.status_code,
                })
            
            # 记录API响应
            if request.path.startswith('/api/'):
                api_logger.info(f"API Response: {response.status_code} ({duration:.3f}s)", extra={
                    'method': request.method,
                    'path': request.path,
                    'status_code': response.status_code,
                    'duration': duration,
                })
        
        return response
    
    def process_exception(self, request, exception):
        """处理异常"""
        logger.error(f"Request exception: {request.method} {request.path} - {str(exception)}", extra={
            'method': request.method,
            'path': request.path,
            'exception': str(exception),
            'exception_type': type(exception).__name__,
        }, exc_info=True)
    
    def get_client_ip(self, request):
        """获取客户端IP"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class PerformanceMonitoringMiddleware(MiddlewareMixin):
    """性能监控中间件"""
    
    def process_request(self, request):
        """处理请求开始"""
        request.performance_start = time.time()
    
    def process_response(self, request, response):
        """处理响应"""
        if hasattr(request, 'performance_start'):
            duration = time.time() - request.performance_start
            
            # 添加性能头信息
            response['X-Response-Time'] = f"{duration:.3f}s"
            
            # 记录性能指标
            self.record_performance_metrics(request, response, duration)
        
        return response
    
    def record_performance_metrics(self, request, response, duration):
        """记录性能指标"""
        metrics = {
            'timestamp': time.time(),
            'method': request.method,
            'path': request.path,
            'status_code': response.status_code,
            'duration': duration,
            'content_length': len(response.content) if hasattr(response, 'content') else 0,
        }
        
        # 记录到性能日志
        performance_logger.info("Performance metrics", extra=metrics)
        
        # 如果启用了详细监控，记录更多信息
        if getattr(settings, 'DETAILED_PERFORMANCE_MONITORING', False):
            try:
                import psutil
                import os

                process = psutil.Process(os.getpid())
                metrics.update({
                    'memory_usage': process.memory_info().rss / 1024 / 1024,  # MB
                    'cpu_percent': process.cpu_percent(),
                })

                performance_logger.info("Detailed performance metrics", extra=metrics)
            except ImportError:
                # psutil不可用，跳过详细监控
                performance_logger.info("Performance metrics (psutil not available)", extra=metrics)


class SecurityLoggingMiddleware(MiddlewareMixin):
    """安全日志中间件"""
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.security_logger = logging.getLogger('security')
    
    def __call__(self, request):
        # 检查可疑请求
        self.check_suspicious_requests(request)
        
        response = self.get_response(request)
        
        # 记录安全相关事件
        self.log_security_events(request, response)
        
        return response
    
    def check_suspicious_requests(self, request):
        """检查可疑请求"""
        # 检查SQL注入尝试
        suspicious_patterns = [
            'union select', 'drop table', 'insert into', 'delete from',
            '<script>', 'javascript:', 'onload=', 'onerror=',
        ]
        
        query_string = request.GET.urlencode().lower()
        for pattern in suspicious_patterns:
            if pattern in query_string:
                self.security_logger.warning(f"Suspicious request detected: {pattern}", extra={
                    'pattern': pattern,
                    'path': request.path,
                    'query_string': request.GET.urlencode(),
                    'ip': self.get_client_ip(request),
                    'user_agent': request.META.get('HTTP_USER_AGENT', ''),
                })
                break
    
    def log_security_events(self, request, response):
        """记录安全事件"""
        # 记录认证失败
        if response.status_code == 401:
            self.security_logger.warning("Authentication failed", extra={
                'path': request.path,
                'method': request.method,
                'ip': self.get_client_ip(request),
                'user_agent': request.META.get('HTTP_USER_AGENT', ''),
            })
        
        # 记录权限拒绝
        if response.status_code == 403:
            self.security_logger.warning("Access denied", extra={
                'path': request.path,
                'method': request.method,
                'user': str(request.user) if hasattr(request, 'user') else 'Anonymous',
                'ip': self.get_client_ip(request),
            })
        
        # 记录管理员访问
        if request.path.startswith('/admin/') and hasattr(request, 'user') and request.user.is_authenticated:
            self.security_logger.info("Admin access", extra={
                'path': request.path,
                'user': str(request.user),
                'ip': self.get_client_ip(request),
            })
    
    def get_client_ip(self, request):
        """获取客户端IP"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class HealthCheckMiddleware(MiddlewareMixin):
    """健康检查中间件"""
    
    def process_request(self, request):
        """处理健康检查请求"""
        if request.path == '/health/':
            return JsonResponse({
                'status': 'healthy',
                'timestamp': time.time(),
                'version': getattr(settings, 'VERSION', '1.0.0'),
            })
        return None
