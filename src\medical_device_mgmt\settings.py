"""
Django开发环境配置
"""
import os
from pathlib import Path

# 导入基础配置
import sys
sys.path.append(str(Path(__file__).resolve().parent.parent.parent / 'deployment'))
from deployment.config.base_settings import *

# 开发环境特定配置
DEBUG = True
ALLOWED_HOSTS = ['localhost', '127.0.0.1', '0.0.0.0']

# 开发环境数据库配置
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'data' / 'db.sqlite3',
    }
}


# 开发环境特定配置
# 开发环境可以启用Django Debug Toolbar
if DEBUG:
    try:
        import debug_toolbar
        INSTALLED_APPS += ['debug_toolbar']
        MIDDLEWARE += ['debug_toolbar.middleware.DebugToolbarMiddleware']
        INTERNAL_IPS = ['127.0.0.1']
    except ImportError:
        pass

# 开发环境日志配置
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {asctime} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'simple'
        },
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': BASE_DIR / 'logs' / 'django.log',
            'formatter': 'verbose',
        },
    },
    'root': {
        'handlers': ['console'],
        'level': 'INFO',
    },
    'loggers': {
        'django': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
            'propagate': False,
        },
        'core': {
            'handlers': ['console'],
            'level': 'DEBUG',
            'propagate': False,
        },
    },
}

# 开发环境特定的配置覆盖
# 覆盖基础配置中的某些设置

# 开发环境的认证类
REST_FRAMEWORK.update({
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'authentication.authentication.TripleTokenAuthentication',
        'rest_framework.authentication.SessionAuthentication',  # 开发环境额外支持Session认证
    ],
})

# 开发环境的令牌配置
TOKEN_SETTINGS = {
    'ACCESS_TOKEN_LIFETIME': 15,  # 分钟
    'REFRESH_TOKEN_LIFETIME': 7,  # 天
    'DEVICE_TOKEN_LIFETIME': 30,  # 天
    'ALGORITHM': 'HS256',
    'SIGNING_KEY': SECRET_KEY,
}

# 开发环境的主题配置
THEME_SETTINGS = {
    'DEFAULT_THEME': 'light',
    'AVAILABLE_THEMES': ['light', 'dark', 'medical'],
    'THEME_COOKIE_NAME': 'user_theme',
    'THEME_COOKIE_AGE': 365 * 24 * 60 * 60,  # 1年
}

# 开发环境的图标库配置
ICON_SETTINGS = {
    'DEFAULT_ICON_SET': 'feather',
    'AVAILABLE_ICON_SETS': ['feather', 'material', 'fontawesome'],
    'ICON_CACHE_TIMEOUT': 3600,  # 1小时
}
