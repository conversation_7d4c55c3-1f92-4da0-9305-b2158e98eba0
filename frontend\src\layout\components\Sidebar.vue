<template>
  <div class="sidebar-container">
    <!-- Logo区域 -->
    <div class="logo-container">
      <router-link to="/" class="logo-link">
        <el-icon class="logo-icon">
          <Monitor />
        </el-icon>
        <span v-show="!collapsed" class="logo-text">医疗设备管理</span>
      </router-link>
    </div>
    
    <!-- 菜单区域 -->
    <el-scrollbar class="menu-scrollbar">
      <el-menu
        :default-active="activeMenu"
        :collapse="collapsed"
        :unique-opened="true"
        router
        class="sidebar-menu"
      >
        <template v-for="route in menuRoutes" :key="route.path">
          <SidebarItem :route="route" />
        </template>
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import SidebarItem from './SidebarItem.vue'

const route = useRoute()

// 临时使用本地状态
const collapsed = ref(false)

// 当前激活的菜单
const activeMenu = computed(() => {
  const { path } = route
  return path
})

// 菜单路由配置
const menuRoutes = [
  {
    path: '/dashboard',
    name: 'Dashboard',
    meta: { title: '仪表板', icon: 'DataBoard' }
  },
  {
    path: '/devices',
    name: 'Devices',
    meta: { title: '设备管理', icon: 'Monitor' },
    children: [
      {
        path: '/devices',
        name: 'DeviceList',
        meta: { title: '设备列表' }
      },
      {
        path: '/devices/categories',
        name: 'DeviceCategories',
        meta: { title: '设备分类' }
      },
      {
        path: '/devices/manufacturers',
        name: 'DeviceManufacturers',
        meta: { title: '制造商管理' }
      },
      {
        path: '/devices/locations',
        name: 'DeviceLocations',
        meta: { title: '位置管理' }
      }
    ]
  },
  {
    path: '/maintenance',
    name: 'Maintenance',
    meta: { title: '维护管理', icon: 'Tools' },
    children: [
      {
        path: '/maintenance',
        name: 'MaintenanceList',
        meta: { title: '维护记录' }
      },
      {
        path: '/maintenance/plans',
        name: 'MaintenancePlans',
        meta: { title: '维护计划' }
      },
      {
        path: '/maintenance/faults',
        name: 'FaultReports',
        meta: { title: '故障报告' }
      }
    ]
  },
  {
    path: '/inventory',
    name: 'Inventory',
    meta: { title: '库存管理', icon: 'Box' },
    children: [
      {
        path: '/inventory',
        name: 'InventoryList',
        meta: { title: '库存列表' }
      },
      {
        path: '/inventory/suppliers',
        name: 'Suppliers',
        meta: { title: '供应商管理' }
      },
      {
        path: '/inventory/movements',
        name: 'StockMovements',
        meta: { title: '库存变动' }
      }
    ]
  },
  {
    path: '/reports',
    name: 'Reports',
    meta: { title: '报表分析', icon: 'DataAnalysis' }
  },
  {
    path: '/settings',
    name: 'Settings',
    meta: { title: '系统设置', icon: 'Setting' }
  }
]
</script>

<style lang="scss" scoped>
.sidebar-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .logo-container {
    height: 60px;
    display: flex;
    align-items: center;
    padding: 0 16px;
    border-bottom: 1px solid var(--el-border-color-light);
    
    .logo-link {
      display: flex;
      align-items: center;
      text-decoration: none;
      color: var(--el-text-color-primary);
      font-weight: 600;
      
      .logo-icon {
        font-size: 24px;
        color: var(--el-color-primary);
        margin-right: 8px;
      }
      
      .logo-text {
        font-size: 16px;
        white-space: nowrap;
      }
    }
  }
  
  .menu-scrollbar {
    flex: 1;
    
    .sidebar-menu {
      border: none;
      background-color: transparent;
      
      :deep(.el-menu-item),
      :deep(.el-sub-menu__title) {
        height: 48px;
        line-height: 48px;
        
        &:hover {
          background-color: var(--el-color-primary-light-9);
        }
        
        &.is-active {
          background-color: var(--el-color-primary-light-8);
          color: var(--el-color-primary);
          
          &::before {
            content: '';
            position: absolute;
            right: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background-color: var(--el-color-primary);
          }
        }
      }
      
      :deep(.el-sub-menu .el-menu-item) {
        padding-left: 48px !important;
        
        &.is-active {
          background-color: var(--el-color-primary-light-9);
        }
      }
    }
  }
}
</style>
