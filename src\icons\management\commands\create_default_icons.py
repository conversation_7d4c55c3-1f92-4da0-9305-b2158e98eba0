"""
创建默认图标集的管理命令
"""
from django.core.management.base import BaseCommand
from icons.models import IconSet, Icon


class Command(BaseCommand):
    help = '创建默认图标集和图标'
    
    def handle(self, *args, **options):
        """执行命令"""
        self.stdout.write('开始创建默认图标集...')
        
        # Feather Icons 图标集
        feather_set, created = IconSet.objects.get_or_create(
            name='feather',
            defaults={
                'display_name': 'Feather Icons',
                'description': '轻量级的开源图标集，简洁美观',
                'version': '4.29.0',
                'icon_type': 'font',
                'prefix': 'feather-',
                'css_url': 'https://cdn.jsdelivr.net/npm/feather-icons@4.29.0/dist/feather.min.css',
                'is_active': True,
                'is_default': True,
            }
        )
        if created:
            self.stdout.write(self.style.SUCCESS('✓ Feather Icons 图标集创建成功'))
        else:
            self.stdout.write('Feather Icons 图标集已存在')
        
        # 创建一些常用的 Feather 图标
        feather_icons = [
            {'name': 'home', 'display_name': '首页', 'category': '导航', 'icon_class': 'home'},
            {'name': 'user', 'display_name': '用户', 'category': '用户', 'icon_class': 'user'},
            {'name': 'users', 'display_name': '用户组', 'category': '用户', 'icon_class': 'users'},
            {'name': 'settings', 'display_name': '设置', 'category': '操作', 'icon_class': 'settings'},
            {'name': 'search', 'display_name': '搜索', 'category': '操作', 'icon_class': 'search'},
            {'name': 'plus', 'display_name': '添加', 'category': '操作', 'icon_class': 'plus'},
            {'name': 'edit', 'display_name': '编辑', 'category': '操作', 'icon_class': 'edit'},
            {'name': 'trash', 'display_name': '删除', 'category': '操作', 'icon_class': 'trash'},
            {'name': 'save', 'display_name': '保存', 'category': '操作', 'icon_class': 'save'},
            {'name': 'download', 'display_name': '下载', 'category': '操作', 'icon_class': 'download'},
            {'name': 'upload', 'display_name': '上传', 'category': '操作', 'icon_class': 'upload'},
            {'name': 'file', 'display_name': '文件', 'category': '文件', 'icon_class': 'file'},
            {'name': 'folder', 'display_name': '文件夹', 'category': '文件', 'icon_class': 'folder'},
            {'name': 'image', 'display_name': '图片', 'category': '文件', 'icon_class': 'image'},
            {'name': 'calendar', 'display_name': '日历', 'category': '时间', 'icon_class': 'calendar'},
            {'name': 'clock', 'display_name': '时钟', 'category': '时间', 'icon_class': 'clock'},
            {'name': 'mail', 'display_name': '邮件', 'category': '通信', 'icon_class': 'mail'},
            {'name': 'phone', 'display_name': '电话', 'category': '通信', 'icon_class': 'phone'},
            {'name': 'message-circle', 'display_name': '消息', 'category': '通信', 'icon_class': 'message-circle'},
            {'name': 'bell', 'display_name': '通知', 'category': '通信', 'icon_class': 'bell'},
            {'name': 'heart', 'display_name': '收藏', 'category': '状态', 'icon_class': 'heart'},
            {'name': 'star', 'display_name': '星标', 'category': '状态', 'icon_class': 'star'},
            {'name': 'check', 'display_name': '成功', 'category': '状态', 'icon_class': 'check'},
            {'name': 'x', 'display_name': '错误', 'category': '状态', 'icon_class': 'x'},
            {'name': 'alert-circle', 'display_name': '警告', 'category': '状态', 'icon_class': 'alert-circle'},
            {'name': 'info', 'display_name': '信息', 'category': '状态', 'icon_class': 'info'},
            {'name': 'eye', 'display_name': '查看', 'category': '操作', 'icon_class': 'eye'},
            {'name': 'eye-off', 'display_name': '隐藏', 'category': '操作', 'icon_class': 'eye-off'},
            {'name': 'lock', 'display_name': '锁定', 'category': '安全', 'icon_class': 'lock'},
            {'name': 'unlock', 'display_name': '解锁', 'category': '安全', 'icon_class': 'unlock'},
            {'name': 'shield', 'display_name': '安全', 'category': '安全', 'icon_class': 'shield'},
            {'name': 'activity', 'display_name': '活动', 'category': '医疗', 'icon_class': 'activity'},
            {'name': 'thermometer', 'display_name': '温度计', 'category': '医疗', 'icon_class': 'thermometer'},
            {'name': 'zap', 'display_name': '电击', 'category': '医疗', 'icon_class': 'zap'},
            {'name': 'cpu', 'display_name': '设备', 'category': '设备', 'icon_class': 'cpu'},
            {'name': 'monitor', 'display_name': '监视器', 'category': '设备', 'icon_class': 'monitor'},
            {'name': 'smartphone', 'display_name': '手机', 'category': '设备', 'icon_class': 'smartphone'},
            {'name': 'tablet', 'display_name': '平板', 'category': '设备', 'icon_class': 'tablet'},
            {'name': 'wifi', 'display_name': 'WiFi', 'category': '网络', 'icon_class': 'wifi'},
            {'name': 'bluetooth', 'display_name': '蓝牙', 'category': '网络', 'icon_class': 'bluetooth'},
        ]
        
        created_count = 0
        for icon_data in feather_icons:
            icon, created = Icon.objects.get_or_create(
                icon_set=feather_set,
                name=icon_data['name'],
                defaults={
                    'display_name': icon_data['display_name'],
                    'description': f"{icon_data['display_name']}图标",
                    'icon_class': icon_data['icon_class'],
                    'category': icon_data['category'],
                    'tags': [icon_data['category'], icon_data['display_name']],
                    'default_size': '16px',
                    'default_color': '#495057',
                    'is_active': True,
                }
            )
            if created:
                created_count += 1
        
        if created_count > 0:
            self.stdout.write(self.style.SUCCESS(f'✓ 创建了 {created_count} 个 Feather 图标'))
        else:
            self.stdout.write('Feather 图标已存在')
        
        # Material Icons 图标集
        material_set, created = IconSet.objects.get_or_create(
            name='material',
            defaults={
                'display_name': 'Material Icons',
                'description': 'Google Material Design 图标集',
                'version': '1.0.0',
                'icon_type': 'font',
                'prefix': 'material-icons',
                'css_url': 'https://fonts.googleapis.com/icon?family=Material+Icons',
                'is_active': True,
                'is_default': False,
            }
        )
        if created:
            self.stdout.write(self.style.SUCCESS('✓ Material Icons 图标集创建成功'))
        else:
            self.stdout.write('Material Icons 图标集已存在')
        
        # FontAwesome 图标集
        fontawesome_set, created = IconSet.objects.get_or_create(
            name='fontawesome',
            defaults={
                'display_name': 'Font Awesome',
                'description': '最受欢迎的图标字体库',
                'version': '6.0.0',
                'icon_type': 'font',
                'prefix': 'fas fa-',
                'css_url': 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css',
                'is_active': True,
                'is_default': False,
            }
        )
        if created:
            self.stdout.write(self.style.SUCCESS('✓ Font Awesome 图标集创建成功'))
        else:
            self.stdout.write('Font Awesome 图标集已存在')
        
        self.stdout.write(self.style.SUCCESS('默认图标集创建完成！'))
