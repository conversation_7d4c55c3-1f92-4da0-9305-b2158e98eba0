// 全局样式文件

// 重置样式
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

// Element Plus 样式覆盖
.el-button {
  font-weight: 400;
}

.el-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.el-table {
  .el-table__header {
    th {
      background-color: var(--el-bg-color-page);
      color: var(--el-text-color-primary);
      font-weight: 600;
    }
  }
}

.el-dialog {
  border-radius: 8px;
}

.el-form {
  .el-form-item__label {
    font-weight: 500;
  }
}

// 自定义工具类
.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-left {
  text-align: left;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-1 {
  flex: 1;
}

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 8px; }
.mb-2 { margin-bottom: 16px; }
.mb-3 { margin-bottom: 24px; }
.mb-4 { margin-bottom: 32px; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 8px; }
.mt-2 { margin-top: 16px; }
.mt-3 { margin-top: 24px; }
.mt-4 { margin-top: 32px; }

.p-0 { padding: 0; }
.p-1 { padding: 8px; }
.p-2 { padding: 16px; }
.p-3 { padding: 24px; }
.p-4 { padding: 32px; }

// 状态颜色类
.status-normal {
  color: var(--el-color-success);
}

.status-warning {
  color: var(--el-color-warning);
}

.status-danger {
  color: var(--el-color-danger);
}

.status-info {
  color: var(--el-color-info);
}

// 主题变量
:root {
  // 浅色主题
  --theme-primary: #409EFF;
  --theme-success: #67C23A;
  --theme-warning: #E6A23C;
  --theme-danger: #F56C6C;
  --theme-info: #909399;
  --theme-background: #ffffff;
  --theme-surface: #f5f7fa;
  --theme-text: #303133;
  --theme-text-secondary: #606266;
}

.dark {
  // 深色主题变量
  --theme-background: #1d1e1f;
  --theme-surface: #2b2b2b;
  --theme-text: #E5EAF3;
  --theme-text-secondary: #CFD3DC;
}

.medical {
  // 医疗主题变量
  --theme-primary: #00A86B;
  --theme-success: #52C41A;
  --theme-warning: #FAAD14;
  --theme-danger: #FF4D4F;
  --theme-info: #1890FF;
  --theme-background: #f0f8f0;
  --theme-surface: #ffffff;
  --theme-text: #2c3e50;
  --theme-text-secondary: #5a6c7d;
}

// 响应式断点
@media (max-width: 768px) {
  .mobile-hidden {
    display: none !important;
  }
}

@media (min-width: 769px) {
  .desktop-hidden {
    display: none !important;
  }
}

// 动画类
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s ease;
}

.slide-up-enter-from {
  transform: translateY(30px);
  opacity: 0;
}

.slide-up-leave-to {
  transform: translateY(-30px);
  opacity: 0;
}

// 加载状态
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  color: var(--el-text-color-secondary);
}

// 空状态
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  color: var(--el-text-color-secondary);
  
  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
  }
  
  .empty-text {
    font-size: 14px;
  }
}

// 卡片网格布局
.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

// 统计卡片样式
.stat-card {
  .stat-value {
    font-size: 28px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }
  
  .stat-label {
    font-size: 14px;
    color: var(--el-text-color-secondary);
    margin-top: 4px;
  }
  
  .stat-icon {
    font-size: 32px;
    opacity: 0.8;
  }
}
