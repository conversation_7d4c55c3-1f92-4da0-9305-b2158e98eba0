"""
核心异常处理
"""
from rest_framework.views import exception_handler
from rest_framework.response import Response
from rest_framework import status
import logging

logger = logging.getLogger(__name__)


def custom_exception_handler(exc, context):
    """
    自定义异常处理器
    """
    # 调用REST framework默认的异常处理器
    response = exception_handler(exc, context)
    
    if response is not None:
        # 记录异常日志
        logger.error(f"API Exception: {exc}", exc_info=True)
        
        # 自定义错误响应格式
        custom_response_data = {
            'success': False,
            'message': '请求处理失败',
            'errors': response.data,
            'status_code': response.status_code
        }
        
        response.data = custom_response_data
    
    return response


class BusinessException(Exception):
    """业务异常基类"""
    
    def __init__(self, message, code=None):
        self.message = message
        self.code = code
        super().__init__(message)


class ValidationException(BusinessException):
    """验证异常"""
    pass


class PermissionException(BusinessException):
    """权限异常"""
    pass


class ResourceNotFoundException(BusinessException):
    """资源未找到异常"""
    pass
