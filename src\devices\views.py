"""
设备模块视图
"""
from rest_framework import viewsets, permissions, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db.models import Count
from django.utils import timezone
from datetime import timed<PERSON><PERSON>

from .models import (
    <PERSON>ceCategory, <PERSON>ceManufacturer, <PERSON>ceModel, Device,
    DeviceStatusHistory, DeviceAttachment, DeviceLocation
)
from .serializers import (
    DeviceCategorySerializer, DeviceManufacturerSerializer, DeviceModelSerializer,
    DeviceSerializer, DeviceListSerializer, DeviceStatusHistorySerializer,
    DeviceAttachmentSerializer, DeviceLocationSerializer, DeviceStatsSerializer
)


class DeviceCategoryViewSet(viewsets.ModelViewSet):
    """设备分类视图集"""
    queryset = DeviceCategory.objects.filter(is_active=True)
    serializer_class = DeviceCategorySerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'code', 'description']
    ordering_fields = ['name', 'sort_order', 'created_at']
    ordering = ['sort_order', 'name']

    def get_permissions(self):
        """根据动作设置权限"""
        if self.action in ['list', 'retrieve', 'tree']:
            permission_classes = [permissions.IsAuthenticated]
        else:
            permission_classes = [permissions.IsAdminUser]
        return [permission() for permission in permission_classes]

    @action(detail=False, methods=['get'])
    def tree(self, request):
        """获取分类树"""
        def build_tree(parent=None):
            categories = DeviceCategory.objects.filter(
                parent=parent,
                is_active=True
            ).order_by('sort_order', 'name')

            tree = []
            for category in categories:
                node = DeviceCategorySerializer(category).data
                node['children'] = build_tree(category)
                tree.append(node)
            return tree

        tree = build_tree()
        return Response(tree)

    def perform_create(self, serializer):
        """创建时设置创建者"""
        serializer.save(created_by=self.request.user)


class DeviceManufacturerViewSet(viewsets.ModelViewSet):
    """设备制造商视图集"""
    queryset = DeviceManufacturer.objects.filter(is_active=True)
    serializer_class = DeviceManufacturerSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'code', 'contact_person']
    ordering_fields = ['name', 'created_at']
    ordering = ['name']

    def get_permissions(self):
        """根据动作设置权限"""
        if self.action in ['list', 'retrieve']:
            permission_classes = [permissions.IsAuthenticated]
        else:
            permission_classes = [permissions.IsAdminUser]
        return [permission() for permission in permission_classes]


class DeviceModelViewSet(viewsets.ModelViewSet):
    """设备型号视图集"""
    queryset = DeviceModel.objects.filter(is_active=True)
    serializer_class = DeviceModelSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'model_number', 'description']
    ordering_fields = ['name', 'created_at']
    ordering = ['manufacturer__name', 'name']

    def get_permissions(self):
        """根据动作设置权限"""
        if self.action in ['list', 'retrieve']:
            permission_classes = [permissions.IsAuthenticated]
        else:
            permission_classes = [permissions.IsAdminUser]
        return [permission() for permission in permission_classes]


class DeviceLocationViewSet(viewsets.ModelViewSet):
    """设备位置视图集"""
    queryset = DeviceLocation.objects.filter(is_active=True)
    serializer_class = DeviceLocationSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'code', 'description']
    ordering_fields = ['name', 'created_at']
    ordering = ['name']

    def get_permissions(self):
        """根据动作设置权限"""
        if self.action in ['list', 'retrieve', 'tree']:
            permission_classes = [permissions.IsAuthenticated]
        else:
            permission_classes = [permissions.IsAdminUser]
        return [permission() for permission in permission_classes]

    @action(detail=False, methods=['get'])
    def tree(self, request):
        """获取位置树"""
        def build_tree(parent=None):
            locations = DeviceLocation.objects.filter(
                parent=parent,
                is_active=True
            ).order_by('name')

            tree = []
            for location in locations:
                node = DeviceLocationSerializer(location).data
                node['children'] = build_tree(location)
                tree.append(node)
            return tree

        tree = build_tree()
        return Response(tree)


class DeviceViewSet(viewsets.ModelViewSet):
    """设备视图集"""
    queryset = Device.objects.filter(is_active=True)
    serializer_class = DeviceSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['asset_number', 'name', 'serial_number', 'department', 'location']
    ordering_fields = ['asset_number', 'name', 'created_at', 'last_check_date']
    ordering = ['-created_at']

    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'list':
            return DeviceListSerializer
        return DeviceSerializer

    def get_queryset(self):
        """获取查询集"""
        queryset = super().get_queryset()

        # 状态过滤
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # 使用状态过滤
        usage_status = self.request.query_params.get('usage_status')
        if usage_status:
            queryset = queryset.filter(usage_status=usage_status)

        # 重要程度过滤
        importance = self.request.query_params.get('importance')
        if importance:
            queryset = queryset.filter(importance=importance)

        # 分类过滤
        category = self.request.query_params.get('category')
        if category:
            queryset = queryset.filter(device_model__category__id=category)

        # 制造商过滤
        manufacturer = self.request.query_params.get('manufacturer')
        if manufacturer:
            queryset = queryset.filter(device_model__manufacturer__id=manufacturer)

        # 科室过滤
        department = self.request.query_params.get('department')
        if department:
            queryset = queryset.filter(department=department)

        # 保修状态过滤
        warranty_status = self.request.query_params.get('warranty_status')
        if warranty_status == 'under_warranty':
            queryset = queryset.filter(
                warranty_end_date__gte=timezone.now().date()
            )
        elif warranty_status == 'expired':
            queryset = queryset.filter(
                warranty_end_date__lt=timezone.now().date()
            )

        return queryset.select_related(
            'device_model', 'device_model__manufacturer', 'device_model__category',
            'responsible_person', 'operator', 'created_by'
        )

    def perform_create(self, serializer):
        """创建时设置创建者"""
        serializer.save(created_by=self.request.user)

    @action(detail=True, methods=['post'])
    def change_status(self, request, pk=None):
        """更改设备状态"""
        device = self.get_object()
        new_status = request.data.get('status')
        new_usage_status = request.data.get('usage_status')
        reason = request.data.get('reason', '状态更新')
        description = request.data.get('description', '')

        if not new_status and not new_usage_status:
            return Response(
                {'error': '必须提供新的状态或使用状态'},
                status=status.HTTP_400_BAD_REQUEST
            )

        old_status = device.status
        old_usage_status = device.usage_status

        # 更新状态
        if new_status:
            device.status = new_status
        if new_usage_status:
            device.usage_status = new_usage_status
        device.save()

        # 记录状态历史
        DeviceStatusHistory.objects.create(
            device=device,
            old_status=old_status,
            new_status=device.status,
            old_usage_status=old_usage_status,
            new_usage_status=device.usage_status,
            reason=reason,
            description=description,
            changed_by=request.user
        )

        return Response({
            'message': '设备状态更新成功',
            'device': DeviceSerializer(device).data
        })

    @action(detail=True, methods=['post'])
    def add_attachment(self, request, pk=None):
        """添加设备附件"""
        device = self.get_object()
        serializer = DeviceAttachmentSerializer(
            data=request.data,
            context={'request': request}
        )

        if serializer.is_valid():
            serializer.save(device=device)
            return Response({
                'message': '附件添加成功',
                'attachment': serializer.data
            })

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['get'])
    def status_history(self, request, pk=None):
        """获取设备状态历史"""
        device = self.get_object()
        history = device.status_history.all()
        serializer = DeviceStatusHistorySerializer(history, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def attachments(self, request, pk=None):
        """获取设备附件"""
        device = self.get_object()
        attachments = device.attachments.all()
        serializer = DeviceAttachmentSerializer(attachments, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def dashboard(self, request):
        """设备仪表板数据"""
        # 基础统计
        total_devices = Device.objects.filter(is_active=True).count()

        # 状态分布
        status_stats = Device.objects.filter(is_active=True).values('status').annotate(
            count=Count('id')
        )
        status_distribution = {item['status']: item['count'] for item in status_stats}

        # 需要检查的设备
        today = timezone.now().date()
        need_check = Device.objects.filter(
            is_active=True,
            next_check_date__lte=today + timedelta(days=7)
        ).count()

        # 保修即将到期的设备
        warranty_expiring = Device.objects.filter(
            is_active=True,
            warranty_end_date__lte=today + timedelta(days=30),
            warranty_end_date__gte=today
        ).count()

        # 故障设备
        fault_devices = Device.objects.filter(
            is_active=True,
            status='fault'
        ).count()

        return Response({
            'total_devices': total_devices,
            'status_distribution': status_distribution,
            'need_check': need_check,
            'warranty_expiring': warranty_expiring,
            'fault_devices': fault_devices,
        })
