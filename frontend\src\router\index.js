import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

// 配置NProgress
NProgress.configure({ showSpinner: false })

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/Login.vue'),
    meta: { requiresAuth: false, title: '登录' }
  },
  {
    path: '/',
    component: () => import('@/layout/index.vue'),
    redirect: '/dashboard',
    meta: { requiresAuth: true },
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/index.vue'),
        meta: { title: '仪表板', icon: 'DataBoard' }
      },
      {
        path: 'devices',
        name: 'Devices',
        component: () => import('@/views/devices/index.vue'),
        meta: { title: '设备管理', icon: 'Monitor' },
        children: [
          {
            path: '',
            name: 'DeviceList',
            component: () => import('@/views/devices/DeviceList.vue'),
            meta: { title: '设备列表' }
          },
          {
            path: 'categories',
            name: 'DeviceCategories',
            component: () => import('@/views/devices/Categories.vue'),
            meta: { title: '设备分类' }
          },
          {
            path: 'manufacturers',
            name: 'DeviceManufacturers',
            component: () => import('@/views/devices/Manufacturers.vue'),
            meta: { title: '制造商管理' }
          },
          {
            path: 'locations',
            name: 'DeviceLocations',
            component: () => import('@/views/devices/Locations.vue'),
            meta: { title: '位置管理' }
          }
        ]
      },
      {
        path: 'maintenance',
        name: 'Maintenance',
        component: () => import('@/views/maintenance/index.vue'),
        meta: { title: '维护管理', icon: 'Tools' },
        children: [
          {
            path: '',
            name: 'MaintenanceList',
            component: () => import('@/views/maintenance/MaintenanceList.vue'),
            meta: { title: '维护记录' }
          },
          {
            path: 'plans',
            name: 'MaintenancePlans',
            component: () => import('@/views/maintenance/Plans.vue'),
            meta: { title: '维护计划' }
          },
          {
            path: 'faults',
            name: 'FaultReports',
            component: () => import('@/views/maintenance/Faults.vue'),
            meta: { title: '故障报告' }
          }
        ]
      },
      {
        path: 'inventory',
        name: 'Inventory',
        component: () => import('@/views/inventory/index.vue'),
        meta: { title: '库存管理', icon: 'Box' },
        children: [
          {
            path: '',
            name: 'InventoryList',
            component: () => import('@/views/inventory/InventoryList.vue'),
            meta: { title: '库存列表' }
          },
          {
            path: 'suppliers',
            name: 'Suppliers',
            component: () => import('@/views/inventory/Suppliers.vue'),
            meta: { title: '供应商管理' }
          },
          {
            path: 'movements',
            name: 'StockMovements',
            component: () => import('@/views/inventory/Movements.vue'),
            meta: { title: '库存变动' }
          }
        ]
      },
      {
        path: 'reports',
        name: 'Reports',
        component: () => import('@/views/reports/index.vue'),
        meta: { title: '报表分析', icon: 'DataAnalysis' },
        children: [
          {
            path: '',
            name: 'ReportList',
            component: () => import('@/views/reports/ReportList.vue'),
            meta: { title: '报表列表' }
          },
          {
            path: 'dashboards',
            name: 'Dashboards',
            component: () => import('@/views/reports/Dashboards.vue'),
            meta: { title: '仪表板管理' }
          }
        ]
      },
      {
        path: 'settings',
        name: 'Settings',
        component: () => import('@/views/settings/index.vue'),
        meta: { title: '系统设置', icon: 'Setting' },
        children: [
          {
            path: '',
            name: 'SystemSettings',
            component: () => import('@/views/settings/System.vue'),
            meta: { title: '系统配置' }
          },
          {
            path: 'themes',
            name: 'ThemeSettings',
            component: () => import('@/views/settings/Themes.vue'),
            meta: { title: '主题设置' }
          },
          {
            path: 'profile',
            name: 'UserProfile',
            component: () => import('@/views/settings/Profile.vue'),
            meta: { title: '个人资料' }
          }
        ]
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/error/404.vue'),
    meta: { title: '页面未找到' }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  NProgress.start()
  
  const authStore = useAuthStore()
  
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 医疗设备管理系统`
  }
  
  // 检查是否需要认证
  if (to.meta.requiresAuth !== false) {
    if (!authStore.isAuthenticated) {
      // 尝试从本地存储恢复认证状态
      await authStore.checkAuth()
      
      if (!authStore.isAuthenticated) {
        next('/login')
        return
      }
    }
  }
  
  // 如果已登录用户访问登录页，重定向到首页
  if (to.name === 'Login' && authStore.isAuthenticated) {
    next('/')
    return
  }
  
  next()
})

router.afterEach(() => {
  NProgress.done()
})

export default router
