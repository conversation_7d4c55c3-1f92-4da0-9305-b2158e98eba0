"""
核心工具函数
"""
import re
import hashlib
import secrets
from typing import Any, Dict, List, Optional, Union
from datetime import datetime, timedelta
from decimal import Decimal
from django.utils import timezone
from django.core.exceptions import ValidationError
from django.core.validators import validate_email
import logging

logger = logging.getLogger(__name__)


def generate_code(prefix: str = '', length: int = 8) -> str:
    """生成唯一编码"""
    timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
    random_part = secrets.token_hex(length // 2)
    return f"{prefix}{timestamp}{random_part}".upper()


def validate_phone_number(phone: str) -> bool:
    """验证手机号码"""
    pattern = r'^1[3-9]\d{9}$'
    return bool(re.match(pattern, phone))


def validate_id_card(id_card: str) -> bool:
    """验证身份证号码"""
    if len(id_card) != 18:
        return False
    
    # 验证前17位是否为数字
    if not id_card[:17].isdigit():
        return False
    
    # 验证最后一位校验码
    weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
    check_codes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']
    
    sum_value = sum(int(id_card[i]) * weights[i] for i in range(17))
    check_code = check_codes[sum_value % 11]
    
    return id_card[17].upper() == check_code


def format_currency(amount: Union[int, float, Decimal], currency: str = '¥') -> str:
    """格式化货币"""
    if isinstance(amount, (int, float)):
        amount = Decimal(str(amount))
    
    return f"{currency}{amount:,.2f}"


def calculate_age(birth_date: datetime) -> int:
    """计算年龄"""
    today = timezone.now().date()
    if isinstance(birth_date, datetime):
        birth_date = birth_date.date()
    
    age = today.year - birth_date.year
    if today.month < birth_date.month or (today.month == birth_date.month and today.day < birth_date.day):
        age -= 1
    
    return age


def get_file_hash(file_content: bytes, algorithm: str = 'md5') -> str:
    """计算文件哈希值"""
    hash_obj = hashlib.new(algorithm)
    hash_obj.update(file_content)
    return hash_obj.hexdigest()


def sanitize_filename(filename: str) -> str:
    """清理文件名"""
    # 移除或替换不安全的字符
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    # 移除控制字符
    filename = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', filename)
    # 限制长度
    if len(filename) > 255:
        name, ext = filename.rsplit('.', 1) if '.' in filename else (filename, '')
        max_name_length = 255 - len(ext) - 1
        filename = f"{name[:max_name_length]}.{ext}" if ext else name[:255]
    
    return filename


def parse_size_string(size_str: str) -> int:
    """解析大小字符串（如 '10MB', '1.5GB'）为字节数"""
    size_str = size_str.upper().strip()
    
    units = {
        'B': 1,
        'KB': 1024,
        'MB': 1024 ** 2,
        'GB': 1024 ** 3,
        'TB': 1024 ** 4,
    }
    
    # 提取数字和单位
    match = re.match(r'^([\d.]+)\s*([A-Z]+)$', size_str)
    if not match:
        raise ValueError(f"Invalid size format: {size_str}")
    
    number, unit = match.groups()
    
    if unit not in units:
        raise ValueError(f"Unknown unit: {unit}")
    
    return int(float(number) * units[unit])


def format_file_size(size_bytes: int) -> str:
    """格式化文件大小"""
    if size_bytes == 0:
        return "0B"
    
    units = ['B', 'KB', 'MB', 'GB', 'TB']
    unit_index = 0
    size = float(size_bytes)
    
    while size >= 1024 and unit_index < len(units) - 1:
        size /= 1024
        unit_index += 1
    
    if unit_index == 0:
        return f"{int(size)}{units[unit_index]}"
    else:
        return f"{size:.1f}{units[unit_index]}"


def mask_sensitive_data(data: str, mask_char: str = '*', visible_chars: int = 4) -> str:
    """遮蔽敏感数据"""
    if len(data) <= visible_chars:
        return mask_char * len(data)
    
    visible_start = visible_chars // 2
    visible_end = visible_chars - visible_start
    
    masked_length = len(data) - visible_chars
    masked_part = mask_char * min(masked_length, 6)  # 最多显示6个遮蔽字符
    
    return data[:visible_start] + masked_part + data[-visible_end:] if visible_end > 0 else data[:visible_start] + masked_part


def get_client_ip(request) -> str:
    """获取客户端IP地址"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0].strip()
    else:
        ip = request.META.get('REMOTE_ADDR', '')
    
    return ip


def is_ajax_request(request) -> bool:
    """判断是否为AJAX请求"""
    return request.META.get('HTTP_X_REQUESTED_WITH') == 'XMLHttpRequest'


def get_model_fields(model_class) -> List[str]:
    """获取模型的所有字段名"""
    return [field.name for field in model_class._meta.fields]


def dict_to_choices(data: Dict[str, str]) -> List[tuple]:
    """将字典转换为Django choices格式"""
    return [(key, value) for key, value in data.items()]


def choices_to_dict(choices: List[tuple]) -> Dict[str, str]:
    """将Django choices转换为字典"""
    return dict(choices)


class DateTimeHelper:
    """日期时间辅助类"""
    
    @staticmethod
    def get_date_range(days: int = 30) -> tuple:
        """获取日期范围"""
        end_date = timezone.now()
        start_date = end_date - timedelta(days=days)
        return start_date, end_date
    
    @staticmethod
    def get_month_range(year: int, month: int) -> tuple:
        """获取月份范围"""
        start_date = datetime(year, month, 1)
        if month == 12:
            end_date = datetime(year + 1, 1, 1) - timedelta(seconds=1)
        else:
            end_date = datetime(year, month + 1, 1) - timedelta(seconds=1)
        
        return timezone.make_aware(start_date), timezone.make_aware(end_date)
    
    @staticmethod
    def format_duration(seconds: int) -> str:
        """格式化时长"""
        if seconds < 60:
            return f"{seconds}秒"
        elif seconds < 3600:
            minutes = seconds // 60
            return f"{minutes}分钟"
        elif seconds < 86400:
            hours = seconds // 3600
            minutes = (seconds % 3600) // 60
            return f"{hours}小时{minutes}分钟"
        else:
            days = seconds // 86400
            hours = (seconds % 86400) // 3600
            return f"{days}天{hours}小时"


class ValidationHelper:
    """验证辅助类"""
    
    @staticmethod
    def validate_required_fields(data: Dict[str, Any], required_fields: List[str]) -> List[str]:
        """验证必填字段"""
        errors = []
        for field in required_fields:
            if field not in data or not data[field]:
                errors.append(f"字段 '{field}' 是必填的")
        return errors
    
    @staticmethod
    def validate_email_field(email: str) -> bool:
        """验证邮箱格式"""
        try:
            validate_email(email)
            return True
        except ValidationError:
            return False
    
    @staticmethod
    def validate_positive_number(value: Union[int, float, Decimal]) -> bool:
        """验证正数"""
        try:
            return float(value) > 0
        except (ValueError, TypeError):
            return False
