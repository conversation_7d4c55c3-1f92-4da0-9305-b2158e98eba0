"""
URL configuration for medical_device_management project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from drf_spectacular.views import SpectacularAPIView, SpectacularRedocView, SpectacularSwaggerView
from . import views, api_views

# 自定义管理后台标题
admin.site.site_header = '医疗设备管理系统'
admin.site.site_title = '医疗设备管理'
admin.site.index_title = '系统管理'

urlpatterns = [
    path('admin/', admin.site.urls),

    # 前端页面
    path('', views.index, name='index'),
    path('modern/', views.modern_index, name='modern_index'),
    path('api-docs/', views.api_docs, name='api_docs'),
    path('dashboard-data/', views.dashboard_data, name='dashboard_data'),

    # API文档路由
    path('api/schema/', SpectacularAPIView.as_view(), name='schema'),
    path('api/swagger/', SpectacularSwaggerView.as_view(url_name='schema'), name='swagger-ui'),
    path('api/redoc/', SpectacularRedocView.as_view(url_name='schema'), name='redoc'),

    # API 路由
    path('api/', api_views.APIRootView.as_view(), name='api_root'),
    path('api/health/', api_views.APIHealthView.as_view(), name='api_health'),
    path('api/stats/', api_views.APIStatsView.as_view(), name='api_stats'),
    path('api/auth/', include('authentication.urls')),
    path('api/themes/', include('themes.urls')),
    path('api/icons/', include('icons.urls')),
    path('api/devices/', include('devices.urls')),
    path('api/maintenance/', include('maintenance.urls')),
    path('api/inventory/', include('inventory.urls')),
    path('api/reports/', include('reports.urls')),
    path('api/monitoring/', include('monitoring.urls')),
]

# 开发环境下的静态文件和媒体文件服务
if settings.DEBUG:
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
