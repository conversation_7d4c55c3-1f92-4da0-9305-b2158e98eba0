"""
报表模块URL配置
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

app_name = 'reports'

router = DefaultRouter()
router.register(r'templates', views.ReportTemplateViewSet)
router.register(r'instances', views.ReportInstanceViewSet)
router.register(r'dashboards', views.DashboardViewSet)

urlpatterns = [
    # REST API 路由
    path('', include(router.urls)),

    # 仪表板统计
    path('dashboard-stats/', views.DashboardStatsView.as_view(), name='dashboard_stats'),
]
