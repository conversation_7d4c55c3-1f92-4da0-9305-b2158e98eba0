"""
认证模块视图
"""
from rest_framework import status, generics, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from django.contrib.auth import login, logout
from django.utils import timezone
from django.db import transaction
import uuid

from .models import User, Role, UserRole, LoginLog, DeviceToken
from .serializers import (
    UserSerializer, RoleSerializer, UserRoleSerializer,
    LoginSerializer, TokenRefreshSerializer, ChangePasswordSerializer,
    LoginLogSerializer, UserProfileSerializer
)
from .authentication import TokenManager


class LoginView(APIView):
    """登录视图"""
    permission_classes = [permissions.AllowAny]

    def post(self, request):
        """用户登录"""
        serializer = LoginSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.validated_data['user']
            device_id = serializer.validated_data.get('device_id', str(uuid.uuid4()))
            device_name = serializer.validated_data.get('device_name', 'Unknown Device')
            device_type = serializer.validated_data.get('device_type', 'web')
            remember_me = serializer.validated_data.get('remember_me', False)

            # 获取客户端信息
            ip_address = self.get_client_ip(request)
            user_agent = request.META.get('HTTP_USER_AGENT', '')

            try:
                with transaction.atomic():
                    # 生成设备令牌
                    device_token, device_token_obj = TokenManager.generate_device_token(
                        user=user,
                        device_id=device_id,
                        device_name=device_name,
                        device_type=device_type,
                        ip_address=ip_address,
                        user_agent=user_agent
                    )

                    # 生成访问令牌
                    access_token = TokenManager.generate_access_token(user, device_token_obj)

                    # 生成刷新令牌
                    refresh_token = TokenManager.generate_refresh_token(user)

                    # 记录登录日志
                    LoginLog.objects.create(
                        user=user,
                        ip_address=ip_address,
                        user_agent=user_agent,
                        device_type=device_type,
                        login_status='success'
                    )

                    # 更新用户最后登录时间
                    user.last_login = timezone.now()
                    user.save(update_fields=['last_login'])

                    return Response({
                        'message': '登录成功',
                        'user': UserProfileSerializer(user).data,
                        'tokens': {
                            'access_token': access_token,
                            'refresh_token': refresh_token,
                            'device_token': device_token,
                        },
                        'device_id': device_id,
                    }, status=status.HTTP_200_OK)

            except Exception as e:
                # 记录登录失败日志
                LoginLog.objects.create(
                    user=user,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    device_type=device_type,
                    login_status='failed'
                )
                return Response({
                    'error': '登录失败，请稍后重试'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class LogoutView(APIView):
    """登出视图"""

    def post(self, request):
        """用户登出"""
        user = request.user
        device_id = request.data.get('device_id')

        try:
            with transaction.atomic():
                # 禁用相关令牌
                if device_id:
                    DeviceToken.objects.filter(
                        user=user,
                        device_id=device_id
                    ).update(is_active=False)

                # 记录登出日志
                ip_address = self.get_client_ip(request)
                user_agent = request.META.get('HTTP_USER_AGENT', '')

                # 更新最近的登录日志
                recent_log = LoginLog.objects.filter(
                    user=user,
                    login_status='success',
                    logout_time__isnull=True
                ).order_by('-login_time').first()

                if recent_log:
                    recent_log.logout_time = timezone.now()
                    recent_log.save()

                return Response({
                    'message': '登出成功'
                }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'error': '登出失败，请稍后重试'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class TokenRefreshView(APIView):
    """令牌刷新视图"""
    permission_classes = [permissions.AllowAny]

    def post(self, request):
        """刷新访问令牌"""
        serializer = TokenRefreshSerializer(data=request.data)
        if serializer.is_valid():
            refresh_token = serializer.validated_data['refresh_token']
            device_id = serializer.validated_data.get('device_id')

            try:
                # 验证刷新令牌
                user, payload = TokenManager.verify_token(refresh_token, 'refresh')

                # 获取设备令牌（如果有）
                device_token_obj = None
                if device_id:
                    try:
                        device_token_obj = DeviceToken.objects.get(
                            user=user,
                            device_id=device_id,
                            is_active=True
                        )
                    except DeviceToken.DoesNotExist:
                        pass

                # 生成新的访问令牌
                access_token = TokenManager.generate_access_token(user, device_token_obj)

                return Response({
                    'access_token': access_token,
                    'message': '令牌刷新成功'
                }, status=status.HTTP_200_OK)

            except Exception as e:
                return Response({
                    'error': '令牌刷新失败'
                }, status=status.HTTP_400_BAD_REQUEST)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class UserProfileView(APIView):
    """用户资料视图"""

    def get(self, request):
        """获取用户资料"""
        serializer = UserProfileSerializer(request.user)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def put(self, request):
        """更新用户资料"""
        serializer = UserProfileSerializer(
            request.user,
            data=request.data,
            partial=True
        )
        if serializer.is_valid():
            serializer.save()
            return Response({
                'message': '资料更新成功',
                'user': serializer.data
            }, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ChangePasswordView(APIView):
    """修改密码视图"""

    def post(self, request):
        """修改密码"""
        serializer = ChangePasswordSerializer(
            data=request.data,
            context={'request': request}
        )
        if serializer.is_valid():
            user = request.user
            new_password = serializer.validated_data['new_password']

            # 更新密码
            user.set_password(new_password)
            user.save()

            # 禁用所有现有令牌，强制重新登录
            user.accesstoken_set.update(is_active=False)
            user.refreshtoken_set.update(is_active=False)
            user.devicetoken_set.update(is_active=False)

            return Response({
                'message': '密码修改成功，请重新登录'
            }, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
