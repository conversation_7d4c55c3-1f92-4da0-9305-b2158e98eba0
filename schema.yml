openapi: 3.0.3
info:
  title: 医疗设备管理系统 API
  version: 1.0.0
  description: 提供完整的医疗设备管理功能API接口，包括设备管理、维护计划、库存管理、报表分析等功能。
  contact:
    name: 医疗设备管理系统
    email: <EMAIL>
  license:
    name: MIT License
paths:
  /api/:
    get:
      operationId: root_retrieve
      description: 返回API概览
      security:
      - cookieAuth: []
      - {}
      responses:
        '200':
          description: No response body
  /api/health/:
    get:
      operationId: health_retrieve
      description: 返回API健康状态
      tags:
      - health
      security:
      - cookieAuth: []
      - {}
      responses:
        '200':
          description: No response body
  /api/stats/:
    get:
      operationId: stats_retrieve
      description: 返回API使用统计
      tags:
      - stats
      security:
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/auth/login/:
    post:
      operationId: auth_login_create
      description: 用户登录
      tags:
      - auth
      security:
      - cookieAuth: []
      - {}
      responses:
        '200':
          description: No response body
  /api/auth/logout/:
    post:
      operationId: auth_logout_create
      description: 用户登出
      tags:
      - auth
      security:
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/auth/refresh/:
    post:
      operationId: auth_refresh_create
      description: 刷新访问令牌
      tags:
      - auth
      security:
      - cookieAuth: []
      - {}
      responses:
        '200':
          description: No response body
  /api/auth/profile/:
    get:
      operationId: auth_profile_retrieve
      description: 获取用户资料
      tags:
      - auth
      security:
      - cookieAuth: []
      responses:
        '200':
          description: No response body
    put:
      operationId: auth_profile_update
      description: 更新用户资料
      tags:
      - auth
      security:
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/auth/change-password/:
    post:
      operationId: auth_change_password_create
      description: 修改密码
      tags:
      - auth
      security:
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/themes/themes/:
    get:
      operationId: themes_themes_list
      description: 主题视图集
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - themes
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedThemeListList'
          description: ''
    post:
      operationId: themes_themes_create
      description: 主题视图集
      tags:
      - themes
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ThemeRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ThemeRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ThemeRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Theme'
          description: ''
  /api/themes/themes/current/:
    get:
      operationId: themes_themes_current_retrieve
      description: 获取当前用户的主题
      tags:
      - themes
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Theme'
          description: ''
  /api/themes/themes/preview/:
    post:
      operationId: themes_themes_preview_create
      description: 预览主题
      tags:
      - themes
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ThemeRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ThemeRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ThemeRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Theme'
          description: ''
  /api/themes/themes/{id}/:
    get:
      operationId: themes_themes_retrieve
      description: 主题视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 主题.
        required: true
      tags:
      - themes
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Theme'
          description: ''
    patch:
      operationId: themes_themes_partial_update
      description: 主题视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 主题.
        required: true
      tags:
      - themes
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedThemeRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedThemeRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedThemeRequest'
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Theme'
          description: ''
    put:
      operationId: themes_themes_update
      description: 主题视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 主题.
        required: true
      tags:
      - themes
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ThemeRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ThemeRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ThemeRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Theme'
          description: ''
    delete:
      operationId: themes_themes_destroy
      description: 主题视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 主题.
        required: true
      tags:
      - themes
      security:
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/themes/themes/{id}/apply/:
    post:
      operationId: themes_themes_apply_create
      description: 应用主题
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 主题.
        required: true
      tags:
      - themes
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ThemeRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ThemeRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ThemeRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Theme'
          description: ''
  /api/themes/preference/:
    get:
      operationId: themes_preference_retrieve
      description: 获取用户主题偏好
      tags:
      - themes
      security:
      - cookieAuth: []
      responses:
        '200':
          description: No response body
    post:
      operationId: themes_preference_create
      description: 设置用户主题偏好
      tags:
      - themes
      security:
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/themes/css/:
    get:
      operationId: themes_css_retrieve
      description: 生成用户当前主题的CSS
      tags:
      - themes
      security:
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/themes/default/:
    get:
      operationId: themes_default_retrieve
      description: 获取默认主题
      tags:
      - themes
      security:
      - cookieAuth: []
      - {}
      responses:
        '200':
          description: No response body
  /api/icons/icon-sets/:
    get:
      operationId: icons_icon_sets_list
      description: 图标集视图集
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - icons
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedIconSetListList'
          description: ''
    post:
      operationId: icons_icon_sets_create
      description: 图标集视图集
      tags:
      - icons
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IconSetRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/IconSetRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/IconSetRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IconSet'
          description: ''
  /api/icons/icon-sets/default/:
    get:
      operationId: icons_icon_sets_default_retrieve
      description: 获取默认图标集
      tags:
      - icons
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IconSet'
          description: ''
  /api/icons/icon-sets/{id}/:
    get:
      operationId: icons_icon_sets_retrieve
      description: 图标集视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 图标集.
        required: true
      tags:
      - icons
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IconSet'
          description: ''
    patch:
      operationId: icons_icon_sets_partial_update
      description: 图标集视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 图标集.
        required: true
      tags:
      - icons
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedIconSetRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedIconSetRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedIconSetRequest'
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IconSet'
          description: ''
    put:
      operationId: icons_icon_sets_update
      description: 图标集视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 图标集.
        required: true
      tags:
      - icons
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IconSetRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/IconSetRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/IconSetRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IconSet'
          description: ''
    delete:
      operationId: icons_icon_sets_destroy
      description: 图标集视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 图标集.
        required: true
      tags:
      - icons
      security:
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/icons/icon-sets/{id}/icons/:
    get:
      operationId: icons_icon_sets_icons_retrieve
      description: 获取图标集中的所有图标
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 图标集.
        required: true
      tags:
      - icons
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IconSet'
          description: ''
  /api/icons/icons/:
    get:
      operationId: icons_icons_list
      description: 图标视图集
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - icons
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedIconListList'
          description: ''
    post:
      operationId: icons_icons_create
      description: 图标视图集
      tags:
      - icons
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IconRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/IconRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/IconRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Icon'
          description: ''
  /api/icons/icons/batch/:
    post:
      operationId: icons_icons_batch_create
      description: 批量操作图标
      tags:
      - icons
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IconRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/IconRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/IconRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Icon'
          description: ''
  /api/icons/icons/search/:
    post:
      operationId: icons_icons_search_create
      description: 搜索图标
      tags:
      - icons
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IconRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/IconRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/IconRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Icon'
          description: ''
  /api/icons/icons/{id}/:
    get:
      operationId: icons_icons_retrieve
      description: 图标视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 图标.
        required: true
      tags:
      - icons
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Icon'
          description: ''
    patch:
      operationId: icons_icons_partial_update
      description: 图标视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 图标.
        required: true
      tags:
      - icons
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedIconRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedIconRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedIconRequest'
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Icon'
          description: ''
    put:
      operationId: icons_icons_update
      description: 图标视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 图标.
        required: true
      tags:
      - icons
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IconRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/IconRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/IconRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Icon'
          description: ''
    delete:
      operationId: icons_icons_destroy
      description: 图标视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 图标.
        required: true
      tags:
      - icons
      security:
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/icons/icons/{id}/render/:
    post:
      operationId: icons_icons_render_create
      description: 渲染图标
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 图标.
        required: true
      tags:
      - icons
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IconRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/IconRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/IconRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Icon'
          description: ''
  /api/icons/icons/{id}/use/:
    post:
      operationId: icons_icons_use_create
      description: 记录图标使用
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 图标.
        required: true
      tags:
      - icons
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IconRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/IconRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/IconRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Icon'
          description: ''
  /api/icons/stats/:
    get:
      operationId: icons_stats_retrieve
      description: 获取图标统计信息
      tags:
      - icons
      security:
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/devices/categories/:
    get:
      operationId: devices_categories_list
      description: 获取所有激活状态的设备分类，支持搜索和排序
      summary: 获取设备分类列表
      parameters:
      - in: query
        name: ordering
        schema:
          type: string
        description: 排序字段，可选：name, sort_order, created_at
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - in: query
        name: search
        schema:
          type: string
        description: 搜索关键词，可搜索名称、代码、描述
      tags:
      - devices
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedDeviceCategoryList'
          description: ''
    post:
      operationId: devices_categories_create
      description: 创建新的设备分类
      summary: 创建设备分类
      tags:
      - devices
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeviceCategoryRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/DeviceCategoryRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/DeviceCategoryRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeviceCategory'
          description: ''
  /api/devices/categories/tree/:
    get:
      operationId: devices_categories_tree_list
      description: 获取设备分类的层级树结构
      summary: 获取设备分类树
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - devices
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedDeviceCategoryList'
          description: ''
  /api/devices/categories/{id}/:
    get:
      operationId: devices_categories_retrieve
      description: 根据ID获取设备分类详细信息
      summary: 获取设备分类详情
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 设备分类.
        required: true
      tags:
      - devices
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeviceCategory'
          description: ''
    patch:
      operationId: devices_categories_partial_update
      description: 部分更新设备分类信息
      summary: 部分更新设备分类
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 设备分类.
        required: true
      tags:
      - devices
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedDeviceCategoryRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedDeviceCategoryRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedDeviceCategoryRequest'
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeviceCategory'
          description: ''
    put:
      operationId: devices_categories_update
      description: 更新设备分类信息
      summary: 更新设备分类
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 设备分类.
        required: true
      tags:
      - devices
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeviceCategoryRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/DeviceCategoryRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/DeviceCategoryRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeviceCategory'
          description: ''
    delete:
      operationId: devices_categories_destroy
      description: 软删除设备分类（设置为非激活状态）
      summary: 删除设备分类
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 设备分类.
        required: true
      tags:
      - devices
      security:
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/devices/manufacturers/:
    get:
      operationId: devices_manufacturers_list
      description: 设备制造商视图集
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - devices
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedDeviceManufacturerList'
          description: ''
    post:
      operationId: devices_manufacturers_create
      description: 设备制造商视图集
      tags:
      - devices
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeviceManufacturerRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/DeviceManufacturerRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/DeviceManufacturerRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeviceManufacturer'
          description: ''
  /api/devices/manufacturers/{id}/:
    get:
      operationId: devices_manufacturers_retrieve
      description: 设备制造商视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 设备制造商.
        required: true
      tags:
      - devices
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeviceManufacturer'
          description: ''
    patch:
      operationId: devices_manufacturers_partial_update
      description: 设备制造商视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 设备制造商.
        required: true
      tags:
      - devices
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedDeviceManufacturerRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedDeviceManufacturerRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedDeviceManufacturerRequest'
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeviceManufacturer'
          description: ''
    put:
      operationId: devices_manufacturers_update
      description: 设备制造商视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 设备制造商.
        required: true
      tags:
      - devices
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeviceManufacturerRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/DeviceManufacturerRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/DeviceManufacturerRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeviceManufacturer'
          description: ''
    delete:
      operationId: devices_manufacturers_destroy
      description: 设备制造商视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 设备制造商.
        required: true
      tags:
      - devices
      security:
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/devices/models/:
    get:
      operationId: devices_models_list
      description: 设备型号视图集
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - devices
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedDeviceModelList'
          description: ''
    post:
      operationId: devices_models_create
      description: 设备型号视图集
      tags:
      - devices
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeviceModelRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/DeviceModelRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/DeviceModelRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeviceModel'
          description: ''
  /api/devices/models/{id}/:
    get:
      operationId: devices_models_retrieve
      description: 设备型号视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 设备型号.
        required: true
      tags:
      - devices
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeviceModel'
          description: ''
    patch:
      operationId: devices_models_partial_update
      description: 设备型号视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 设备型号.
        required: true
      tags:
      - devices
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedDeviceModelRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedDeviceModelRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedDeviceModelRequest'
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeviceModel'
          description: ''
    put:
      operationId: devices_models_update
      description: 设备型号视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 设备型号.
        required: true
      tags:
      - devices
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeviceModelRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/DeviceModelRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/DeviceModelRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeviceModel'
          description: ''
    delete:
      operationId: devices_models_destroy
      description: 设备型号视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 设备型号.
        required: true
      tags:
      - devices
      security:
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/devices/locations/:
    get:
      operationId: devices_locations_list
      description: 设备位置视图集
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - devices
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedDeviceLocationList'
          description: ''
    post:
      operationId: devices_locations_create
      description: 设备位置视图集
      tags:
      - devices
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeviceLocationRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/DeviceLocationRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/DeviceLocationRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeviceLocation'
          description: ''
  /api/devices/locations/tree/:
    get:
      operationId: devices_locations_tree_retrieve
      description: 获取位置树
      tags:
      - devices
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeviceLocation'
          description: ''
  /api/devices/locations/{id}/:
    get:
      operationId: devices_locations_retrieve
      description: 设备位置视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 设备位置.
        required: true
      tags:
      - devices
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeviceLocation'
          description: ''
    patch:
      operationId: devices_locations_partial_update
      description: 设备位置视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 设备位置.
        required: true
      tags:
      - devices
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedDeviceLocationRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedDeviceLocationRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedDeviceLocationRequest'
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeviceLocation'
          description: ''
    put:
      operationId: devices_locations_update
      description: 设备位置视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 设备位置.
        required: true
      tags:
      - devices
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeviceLocationRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/DeviceLocationRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/DeviceLocationRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeviceLocation'
          description: ''
    delete:
      operationId: devices_locations_destroy
      description: 设备位置视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 设备位置.
        required: true
      tags:
      - devices
      security:
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/devices/devices/:
    get:
      operationId: devices_devices_list
      description: 设备视图集
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - devices
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedDeviceListList'
          description: ''
    post:
      operationId: devices_devices_create
      description: 设备视图集
      tags:
      - devices
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeviceRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/DeviceRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/DeviceRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Device'
          description: ''
  /api/devices/devices/dashboard/:
    get:
      operationId: devices_devices_dashboard_retrieve
      description: 设备仪表板数据
      tags:
      - devices
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Device'
          description: ''
  /api/devices/devices/{id}/:
    get:
      operationId: devices_devices_retrieve
      description: 设备视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 医疗设备.
        required: true
      tags:
      - devices
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Device'
          description: ''
    patch:
      operationId: devices_devices_partial_update
      description: 设备视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 医疗设备.
        required: true
      tags:
      - devices
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedDeviceRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedDeviceRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedDeviceRequest'
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Device'
          description: ''
    put:
      operationId: devices_devices_update
      description: 设备视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 医疗设备.
        required: true
      tags:
      - devices
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeviceRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/DeviceRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/DeviceRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Device'
          description: ''
    delete:
      operationId: devices_devices_destroy
      description: 设备视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 医疗设备.
        required: true
      tags:
      - devices
      security:
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/devices/devices/{id}/add_attachment/:
    post:
      operationId: devices_devices_add_attachment_create
      description: 添加设备附件
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 医疗设备.
        required: true
      tags:
      - devices
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeviceRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/DeviceRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/DeviceRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Device'
          description: ''
  /api/devices/devices/{id}/attachments/:
    get:
      operationId: devices_devices_attachments_retrieve
      description: 获取设备附件
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 医疗设备.
        required: true
      tags:
      - devices
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Device'
          description: ''
  /api/devices/devices/{id}/change_status/:
    post:
      operationId: devices_devices_change_status_create
      description: 更改设备状态
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 医疗设备.
        required: true
      tags:
      - devices
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeviceRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/DeviceRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/DeviceRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Device'
          description: ''
  /api/devices/devices/{id}/status_history/:
    get:
      operationId: devices_devices_status_history_retrieve
      description: 获取设备状态历史
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 医疗设备.
        required: true
      tags:
      - devices
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Device'
          description: ''
  /api/maintenance/types/:
    get:
      operationId: maintenance_types_list
      description: 维护类型视图集
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - maintenance
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedMaintenanceTypeList'
          description: ''
    post:
      operationId: maintenance_types_create
      description: 维护类型视图集
      tags:
      - maintenance
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MaintenanceTypeRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/MaintenanceTypeRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/MaintenanceTypeRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MaintenanceType'
          description: ''
  /api/maintenance/types/{id}/:
    get:
      operationId: maintenance_types_retrieve
      description: 维护类型视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 维护类型.
        required: true
      tags:
      - maintenance
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MaintenanceType'
          description: ''
    patch:
      operationId: maintenance_types_partial_update
      description: 维护类型视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 维护类型.
        required: true
      tags:
      - maintenance
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedMaintenanceTypeRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedMaintenanceTypeRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedMaintenanceTypeRequest'
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MaintenanceType'
          description: ''
    put:
      operationId: maintenance_types_update
      description: 维护类型视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 维护类型.
        required: true
      tags:
      - maintenance
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MaintenanceTypeRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/MaintenanceTypeRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/MaintenanceTypeRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MaintenanceType'
          description: ''
    delete:
      operationId: maintenance_types_destroy
      description: 维护类型视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 维护类型.
        required: true
      tags:
      - maintenance
      security:
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/maintenance/plans/:
    get:
      operationId: maintenance_plans_list
      description: 维护计划视图集
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - maintenance
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedMaintenancePlanList'
          description: ''
    post:
      operationId: maintenance_plans_create
      description: 维护计划视图集
      tags:
      - maintenance
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MaintenancePlanRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/MaintenancePlanRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/MaintenancePlanRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MaintenancePlan'
          description: ''
  /api/maintenance/plans/overdue/:
    get:
      operationId: maintenance_plans_overdue_retrieve
      description: 获取逾期的维护计划
      tags:
      - maintenance
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MaintenancePlan'
          description: ''
  /api/maintenance/plans/upcoming/:
    get:
      operationId: maintenance_plans_upcoming_retrieve
      description: 获取即将到期的维护计划
      tags:
      - maintenance
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MaintenancePlan'
          description: ''
  /api/maintenance/plans/{id}/:
    get:
      operationId: maintenance_plans_retrieve
      description: 维护计划视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 维护计划.
        required: true
      tags:
      - maintenance
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MaintenancePlan'
          description: ''
    patch:
      operationId: maintenance_plans_partial_update
      description: 维护计划视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 维护计划.
        required: true
      tags:
      - maintenance
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedMaintenancePlanRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedMaintenancePlanRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedMaintenancePlanRequest'
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MaintenancePlan'
          description: ''
    put:
      operationId: maintenance_plans_update
      description: 维护计划视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 维护计划.
        required: true
      tags:
      - maintenance
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MaintenancePlanRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/MaintenancePlanRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/MaintenancePlanRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MaintenancePlan'
          description: ''
    delete:
      operationId: maintenance_plans_destroy
      description: 维护计划视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 维护计划.
        required: true
      tags:
      - maintenance
      security:
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/maintenance/plans/{id}/create_maintenance/:
    post:
      operationId: maintenance_plans_create_maintenance_create
      description: 从计划创建维护记录
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 维护计划.
        required: true
      tags:
      - maintenance
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MaintenancePlanRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/MaintenancePlanRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/MaintenancePlanRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MaintenancePlan'
          description: ''
  /api/maintenance/records/:
    get:
      operationId: maintenance_records_list
      description: 维护记录视图集
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - maintenance
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedMaintenanceListList'
          description: ''
    post:
      operationId: maintenance_records_create
      description: 维护记录视图集
      tags:
      - maintenance
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MaintenanceRecordRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/MaintenanceRecordRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/MaintenanceRecordRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MaintenanceRecord'
          description: ''
  /api/maintenance/records/{id}/:
    get:
      operationId: maintenance_records_retrieve
      description: 维护记录视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 维护记录.
        required: true
      tags:
      - maintenance
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MaintenanceRecord'
          description: ''
    patch:
      operationId: maintenance_records_partial_update
      description: 维护记录视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 维护记录.
        required: true
      tags:
      - maintenance
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedMaintenanceRecordRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedMaintenanceRecordRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedMaintenanceRecordRequest'
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MaintenanceRecord'
          description: ''
    put:
      operationId: maintenance_records_update
      description: 维护记录视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 维护记录.
        required: true
      tags:
      - maintenance
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MaintenanceRecordRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/MaintenanceRecordRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/MaintenanceRecordRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MaintenanceRecord'
          description: ''
    delete:
      operationId: maintenance_records_destroy
      description: 维护记录视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 维护记录.
        required: true
      tags:
      - maintenance
      security:
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/maintenance/records/{id}/complete_maintenance/:
    post:
      operationId: maintenance_records_complete_maintenance_create
      description: 完成维护
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 维护记录.
        required: true
      tags:
      - maintenance
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MaintenanceRecordRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/MaintenanceRecordRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/MaintenanceRecordRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MaintenanceRecord'
          description: ''
  /api/maintenance/records/{id}/start_maintenance/:
    post:
      operationId: maintenance_records_start_maintenance_create
      description: 开始维护
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 维护记录.
        required: true
      tags:
      - maintenance
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MaintenanceRecordRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/MaintenanceRecordRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/MaintenanceRecordRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MaintenanceRecord'
          description: ''
  /api/maintenance/faults/:
    get:
      operationId: maintenance_faults_list
      description: 故障报告视图集
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - maintenance
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedFaultListList'
          description: ''
    post:
      operationId: maintenance_faults_create
      description: 故障报告视图集
      tags:
      - maintenance
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FaultReportRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/FaultReportRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/FaultReportRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FaultReport'
          description: ''
  /api/maintenance/faults/{id}/:
    get:
      operationId: maintenance_faults_retrieve
      description: 故障报告视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 故障报告.
        required: true
      tags:
      - maintenance
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FaultReport'
          description: ''
    patch:
      operationId: maintenance_faults_partial_update
      description: 故障报告视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 故障报告.
        required: true
      tags:
      - maintenance
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedFaultReportRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedFaultReportRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedFaultReportRequest'
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FaultReport'
          description: ''
    put:
      operationId: maintenance_faults_update
      description: 故障报告视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 故障报告.
        required: true
      tags:
      - maintenance
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FaultReportRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/FaultReportRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/FaultReportRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FaultReport'
          description: ''
    delete:
      operationId: maintenance_faults_destroy
      description: 故障报告视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 故障报告.
        required: true
      tags:
      - maintenance
      security:
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/maintenance/faults/{id}/assign/:
    post:
      operationId: maintenance_faults_assign_create
      description: 分配故障
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 故障报告.
        required: true
      tags:
      - maintenance
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FaultReportRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/FaultReportRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/FaultReportRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FaultReport'
          description: ''
  /api/maintenance/faults/{id}/resolve/:
    post:
      operationId: maintenance_faults_resolve_create
      description: 解决故障
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 故障报告.
        required: true
      tags:
      - maintenance
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FaultReportRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/FaultReportRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/FaultReportRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FaultReport'
          description: ''
  /api/inventory/suppliers/:
    get:
      operationId: inventory_suppliers_list
      description: 供应商视图集
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - inventory
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedSupplierList'
          description: ''
    post:
      operationId: inventory_suppliers_create
      description: 供应商视图集
      tags:
      - inventory
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SupplierRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/SupplierRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/SupplierRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Supplier'
          description: ''
  /api/inventory/suppliers/{id}/:
    get:
      operationId: inventory_suppliers_retrieve
      description: 供应商视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 供应商.
        required: true
      tags:
      - inventory
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Supplier'
          description: ''
    patch:
      operationId: inventory_suppliers_partial_update
      description: 供应商视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 供应商.
        required: true
      tags:
      - inventory
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedSupplierRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedSupplierRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedSupplierRequest'
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Supplier'
          description: ''
    put:
      operationId: inventory_suppliers_update
      description: 供应商视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 供应商.
        required: true
      tags:
      - inventory
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SupplierRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/SupplierRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/SupplierRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Supplier'
          description: ''
    delete:
      operationId: inventory_suppliers_destroy
      description: 供应商视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 供应商.
        required: true
      tags:
      - inventory
      security:
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/inventory/categories/:
    get:
      operationId: inventory_categories_list
      description: 物品分类视图集
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - inventory
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedItemCategoryList'
          description: ''
    post:
      operationId: inventory_categories_create
      description: 物品分类视图集
      tags:
      - inventory
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ItemCategoryRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ItemCategoryRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ItemCategoryRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ItemCategory'
          description: ''
  /api/inventory/categories/tree/:
    get:
      operationId: inventory_categories_tree_retrieve
      description: 获取分类树
      tags:
      - inventory
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ItemCategory'
          description: ''
  /api/inventory/categories/{id}/:
    get:
      operationId: inventory_categories_retrieve
      description: 物品分类视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 物品分类.
        required: true
      tags:
      - inventory
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ItemCategory'
          description: ''
    patch:
      operationId: inventory_categories_partial_update
      description: 物品分类视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 物品分类.
        required: true
      tags:
      - inventory
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedItemCategoryRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedItemCategoryRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedItemCategoryRequest'
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ItemCategory'
          description: ''
    put:
      operationId: inventory_categories_update
      description: 物品分类视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 物品分类.
        required: true
      tags:
      - inventory
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ItemCategoryRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ItemCategoryRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ItemCategoryRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ItemCategory'
          description: ''
    delete:
      operationId: inventory_categories_destroy
      description: 物品分类视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 物品分类.
        required: true
      tags:
      - inventory
      security:
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/inventory/items/:
    get:
      operationId: inventory_items_list
      description: 物品视图集
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - inventory
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedItemListList'
          description: ''
    post:
      operationId: inventory_items_create
      description: 物品视图集
      tags:
      - inventory
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ItemRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ItemRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ItemRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Item'
          description: ''
  /api/inventory/items/low_stock/:
    get:
      operationId: inventory_items_low_stock_retrieve
      description: 获取低库存物品
      tags:
      - inventory
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Item'
          description: ''
  /api/inventory/items/{id}/:
    get:
      operationId: inventory_items_retrieve
      description: 物品视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 物品.
        required: true
      tags:
      - inventory
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Item'
          description: ''
    patch:
      operationId: inventory_items_partial_update
      description: 物品视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 物品.
        required: true
      tags:
      - inventory
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedItemRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedItemRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedItemRequest'
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Item'
          description: ''
    put:
      operationId: inventory_items_update
      description: 物品视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 物品.
        required: true
      tags:
      - inventory
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ItemRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ItemRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ItemRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Item'
          description: ''
    delete:
      operationId: inventory_items_destroy
      description: 物品视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 物品.
        required: true
      tags:
      - inventory
      security:
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/inventory/items/{id}/adjust_stock/:
    post:
      operationId: inventory_items_adjust_stock_create
      description: 调整库存
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 物品.
        required: true
      tags:
      - inventory
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ItemRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ItemRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ItemRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Item'
          description: ''
  /api/inventory/movements/:
    get:
      operationId: inventory_movements_list
      description: 库存变动视图集
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - inventory
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedStockMovementList'
          description: ''
    post:
      operationId: inventory_movements_create
      description: 库存变动视图集
      tags:
      - inventory
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/StockMovementRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/StockMovementRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/StockMovementRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StockMovement'
          description: ''
  /api/inventory/movements/{id}/:
    get:
      operationId: inventory_movements_retrieve
      description: 库存变动视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 库存变动记录.
        required: true
      tags:
      - inventory
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StockMovement'
          description: ''
    patch:
      operationId: inventory_movements_partial_update
      description: 库存变动视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 库存变动记录.
        required: true
      tags:
      - inventory
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedStockMovementRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedStockMovementRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedStockMovementRequest'
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StockMovement'
          description: ''
    put:
      operationId: inventory_movements_update
      description: 库存变动视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 库存变动记录.
        required: true
      tags:
      - inventory
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/StockMovementRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/StockMovementRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/StockMovementRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StockMovement'
          description: ''
    delete:
      operationId: inventory_movements_destroy
      description: 库存变动视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 库存变动记录.
        required: true
      tags:
      - inventory
      security:
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/inventory/purchase-orders/:
    get:
      operationId: inventory_purchase_orders_list
      description: 采购订单视图集
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - inventory
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedPurchaseOrderListList'
          description: ''
    post:
      operationId: inventory_purchase_orders_create
      description: 采购订单视图集
      tags:
      - inventory
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PurchaseOrderRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PurchaseOrderRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PurchaseOrderRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PurchaseOrder'
          description: ''
  /api/inventory/purchase-orders/{id}/:
    get:
      operationId: inventory_purchase_orders_retrieve
      description: 采购订单视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 采购订单.
        required: true
      tags:
      - inventory
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PurchaseOrder'
          description: ''
    patch:
      operationId: inventory_purchase_orders_partial_update
      description: 采购订单视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 采购订单.
        required: true
      tags:
      - inventory
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedPurchaseOrderRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedPurchaseOrderRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedPurchaseOrderRequest'
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PurchaseOrder'
          description: ''
    put:
      operationId: inventory_purchase_orders_update
      description: 采购订单视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 采购订单.
        required: true
      tags:
      - inventory
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PurchaseOrderRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PurchaseOrderRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PurchaseOrderRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PurchaseOrder'
          description: ''
    delete:
      operationId: inventory_purchase_orders_destroy
      description: 采购订单视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 采购订单.
        required: true
      tags:
      - inventory
      security:
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/inventory/purchase-orders/{id}/add_item/:
    post:
      operationId: inventory_purchase_orders_add_item_create
      description: 添加采购明细
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 采购订单.
        required: true
      tags:
      - inventory
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PurchaseOrderRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PurchaseOrderRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PurchaseOrderRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PurchaseOrder'
          description: ''
  /api/inventory/purchase-orders/{id}/receive/:
    post:
      operationId: inventory_purchase_orders_receive_create
      description: 收货
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 采购订单.
        required: true
      tags:
      - inventory
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PurchaseOrderRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PurchaseOrderRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PurchaseOrderRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PurchaseOrder'
          description: ''
  /api/inventory/purchase-orders/{id}/submit/:
    post:
      operationId: inventory_purchase_orders_submit_create
      description: 提交订单
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 采购订单.
        required: true
      tags:
      - inventory
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PurchaseOrderRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PurchaseOrderRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PurchaseOrderRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PurchaseOrder'
          description: ''
  /api/reports/templates/:
    get:
      operationId: reports_templates_list
      description: 报表模板视图集
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - reports
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedReportTemplateListList'
          description: ''
    post:
      operationId: reports_templates_create
      description: 报表模板视图集
      tags:
      - reports
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReportTemplateRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ReportTemplateRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ReportTemplateRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReportTemplate'
          description: ''
  /api/reports/templates/{id}/:
    get:
      operationId: reports_templates_retrieve
      description: 报表模板视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 报表模板.
        required: true
      tags:
      - reports
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReportTemplate'
          description: ''
    patch:
      operationId: reports_templates_partial_update
      description: 报表模板视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 报表模板.
        required: true
      tags:
      - reports
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedReportTemplateRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedReportTemplateRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedReportTemplateRequest'
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReportTemplate'
          description: ''
    put:
      operationId: reports_templates_update
      description: 报表模板视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 报表模板.
        required: true
      tags:
      - reports
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReportTemplateRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ReportTemplateRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ReportTemplateRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReportTemplate'
          description: ''
    delete:
      operationId: reports_templates_destroy
      description: 报表模板视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 报表模板.
        required: true
      tags:
      - reports
      security:
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/reports/templates/{id}/generate/:
    post:
      operationId: reports_templates_generate_create
      description: 生成报表
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 报表模板.
        required: true
      tags:
      - reports
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReportTemplateRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ReportTemplateRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ReportTemplateRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReportTemplate'
          description: ''
  /api/reports/instances/:
    get:
      operationId: reports_instances_list
      description: 报表实例视图集
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - reports
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedReportInstanceListList'
          description: ''
    post:
      operationId: reports_instances_create
      description: 报表实例视图集
      tags:
      - reports
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReportInstanceRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ReportInstanceRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ReportInstanceRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReportInstance'
          description: ''
  /api/reports/instances/{id}/:
    get:
      operationId: reports_instances_retrieve
      description: 报表实例视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 报表实例.
        required: true
      tags:
      - reports
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReportInstance'
          description: ''
    patch:
      operationId: reports_instances_partial_update
      description: 报表实例视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 报表实例.
        required: true
      tags:
      - reports
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedReportInstanceRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedReportInstanceRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedReportInstanceRequest'
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReportInstance'
          description: ''
    put:
      operationId: reports_instances_update
      description: 报表实例视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 报表实例.
        required: true
      tags:
      - reports
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReportInstanceRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ReportInstanceRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ReportInstanceRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReportInstance'
          description: ''
    delete:
      operationId: reports_instances_destroy
      description: 报表实例视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 报表实例.
        required: true
      tags:
      - reports
      security:
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/reports/instances/{id}/download/:
    get:
      operationId: reports_instances_download_retrieve
      description: 下载报表文件
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 报表实例.
        required: true
      tags:
      - reports
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReportInstance'
          description: ''
  /api/reports/dashboards/:
    get:
      operationId: reports_dashboards_list
      description: 仪表板视图集
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - reports
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedDashboardListList'
          description: ''
    post:
      operationId: reports_dashboards_create
      description: 仪表板视图集
      tags:
      - reports
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DashboardRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/DashboardRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/DashboardRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Dashboard'
          description: ''
  /api/reports/dashboards/{id}/:
    get:
      operationId: reports_dashboards_retrieve
      description: 仪表板视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 仪表板.
        required: true
      tags:
      - reports
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Dashboard'
          description: ''
    patch:
      operationId: reports_dashboards_partial_update
      description: 仪表板视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 仪表板.
        required: true
      tags:
      - reports
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedDashboardRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedDashboardRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedDashboardRequest'
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Dashboard'
          description: ''
    put:
      operationId: reports_dashboards_update
      description: 仪表板视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 仪表板.
        required: true
      tags:
      - reports
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DashboardRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/DashboardRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/DashboardRequest'
        required: true
      security:
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Dashboard'
          description: ''
    delete:
      operationId: reports_dashboards_destroy
      description: 仪表板视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 仪表板.
        required: true
      tags:
      - reports
      security:
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/reports/dashboard-stats/:
    get:
      operationId: reports_dashboard_stats_retrieve
      description: 获取仪表板统计数据
      tags:
      - reports
      security:
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/monitoring/system-stats/:
    get:
      operationId: monitoring_system_stats_retrieve
      description: 获取系统统计信息
      tags:
      - monitoring
      security:
      - cookieAuth: []
      responses:
        '200':
          description: No response body
components:
  schemas:
    Dashboard:
      type: object
      description: 仪表板序列化器
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        name:
          type: string
          title: 仪表板名称
          maxLength: 200
        description:
          type: string
          nullable: true
          title: 描述
        layout:
          title: 布局配置
        widgets:
          title: 组件配置
        auto_refresh:
          type: boolean
          title: 自动刷新
        refresh_interval:
          type: integer
          maximum: 922337*************
          minimum: 0
          format: int64
          title: 刷新间隔(秒)
        is_public:
          type: boolean
          title: 是否公开
        allowed_users:
          type: array
          items:
            type: string
            format: uuid
            title: 允许访问的用户
          title: 允许访问的用户
        allowed_users_names:
          type: string
          readOnly: true
        is_active:
          type: boolean
          title: 是否激活
        created_by:
          type: string
          format: uuid
          readOnly: true
          title: 创建者
        created_by_name:
          type: string
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 创建时间
        updated_at:
          type: string
          format: date-time
          readOnly: true
          title: 更新时间
      required:
      - allowed_users_names
      - created_at
      - created_by
      - created_by_name
      - id
      - name
      - updated_at
    DashboardList:
      type: object
      description: 仪表板列表序列化器
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        name:
          type: string
          title: 仪表板名称
          maxLength: 200
        description:
          type: string
          nullable: true
          title: 描述
        is_public:
          type: boolean
          title: 是否公开
        auto_refresh:
          type: boolean
          title: 自动刷新
        widget_count:
          type: string
          readOnly: true
        created_by_name:
          type: string
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 创建时间
      required:
      - created_at
      - created_by_name
      - id
      - name
      - widget_count
    DashboardRequest:
      type: object
      description: 仪表板序列化器
      properties:
        name:
          type: string
          minLength: 1
          title: 仪表板名称
          maxLength: 200
        description:
          type: string
          nullable: true
          title: 描述
        layout:
          title: 布局配置
        widgets:
          title: 组件配置
        auto_refresh:
          type: boolean
          title: 自动刷新
        refresh_interval:
          type: integer
          maximum: 922337*************
          minimum: 0
          format: int64
          title: 刷新间隔(秒)
        is_public:
          type: boolean
          title: 是否公开
        allowed_users:
          type: array
          items:
            type: string
            format: uuid
            title: 允许访问的用户
          title: 允许访问的用户
        is_active:
          type: boolean
          title: 是否激活
      required:
      - name
    Device:
      type: object
      description: 设备序列化器
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        asset_number:
          type: string
          title: 资产编号
          maxLength: 50
        serial_number:
          type: string
          nullable: true
          title: 序列号
          maxLength: 100
        name:
          type: string
          title: 设备名称
          maxLength: 200
        device_model:
          type: string
          format: uuid
          title: 设备型号
        device_model_name:
          type: string
          readOnly: true
        manufacturer_name:
          type: string
          readOnly: true
        category_name:
          type: string
          readOnly: true
        department:
          type: string
          nullable: true
          title: 所属科室
          maxLength: 100
        location:
          type: string
          nullable: true
          title: 具体位置
          maxLength: 200
        room:
          type: string
          nullable: true
          title: 房间号
          maxLength: 50
        responsible_person:
          type: string
          format: uuid
          nullable: true
          title: 责任人
        responsible_person_name:
          type: string
          readOnly: true
        operator:
          type: string
          format: uuid
          nullable: true
          title: 操作员
        operator_name:
          type: string
          readOnly: true
        purchase_date:
          type: string
          format: date
          nullable: true
          title: 采购日期
        purchase_price:
          type: string
          format: decimal
          pattern: ^-?\d{0,10}(?:\.\d{0,2})?$
          nullable: true
          title: 采购价格
        supplier:
          type: string
          nullable: true
          title: 供应商
          maxLength: 200
        warranty_start_date:
          type: string
          format: date
          nullable: true
          title: 保修开始日期
        warranty_end_date:
          type: string
          format: date
          nullable: true
          title: 保修结束日期
        is_under_warranty:
          type: string
          readOnly: true
        warranty_days_left:
          type: string
          readOnly: true
        status:
          enum:
          - normal
          - maintenance
          - repair
          - fault
          - retired
          - idle
          type: string
          description: |-
            * `normal` - 正常
            * `maintenance` - 维护中
            * `repair` - 维修中
            * `fault` - 故障
            * `retired` - 报废
            * `idle` - 闲置
          x-spec-enum-id: 69dbaf18a0c31a0a
          title: 设备状态
        usage_status:
          enum:
          - in_use
          - available
          - unavailable
          - reserved
          type: string
          description: |-
            * `in_use` - 使用中
            * `available` - 可用
            * `unavailable` - 不可用
            * `reserved` - 预约中
          x-spec-enum-id: b23b06164f8cd341
          title: 使用状态
        importance:
          enum:
          - critical
          - important
          - normal
          - low
          type: string
          description: |-
            * `critical` - 关键
            * `important` - 重要
            * `normal` - 一般
            * `low` - 较低
          x-spec-enum-id: c9ebac9f4bc32fe0
          title: 重要程度
        status_color:
          type: string
          readOnly: true
        parameters:
          title: 设备参数
        notes:
          type: string
          nullable: true
          title: 备注
        last_check_date:
          type: string
          format: date-time
          nullable: true
          title: 最后检查时间
        last_check_result:
          type: string
          nullable: true
          title: 最后检查结果
        next_check_date:
          type: string
          format: date
          nullable: true
          title: 下次检查日期
        is_active:
          type: boolean
          title: 是否激活
        attachments:
          type: array
          items:
            $ref: '#/components/schemas/DeviceAttachment'
          readOnly: true
        recent_status_history:
          type: string
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 创建时间
        updated_at:
          type: string
          format: date-time
          readOnly: true
          title: 更新时间
        created_by:
          type: string
          format: uuid
          readOnly: true
          nullable: true
          title: 创建者
        created_by_name:
          type: string
          readOnly: true
      required:
      - asset_number
      - attachments
      - category_name
      - created_at
      - created_by
      - created_by_name
      - device_model
      - device_model_name
      - id
      - is_under_warranty
      - manufacturer_name
      - name
      - operator_name
      - recent_status_history
      - responsible_person_name
      - status_color
      - updated_at
      - warranty_days_left
    DeviceAttachment:
      type: object
      description: 设备附件序列化器
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        device:
          type: string
          format: uuid
          title: 设备
        device_name:
          type: string
          readOnly: true
        name:
          type: string
          title: 附件名称
          maxLength: 200
        description:
          type: string
          nullable: true
          title: 描述
        file:
          type: string
          format: uri
          title: 文件
        file_size:
          type: integer
          readOnly: true
          nullable: true
          title: 文件大小
        file_size_mb:
          type: string
          readOnly: true
        attachment_type:
          enum:
          - manual
          - certificate
          - warranty
          - photo
          - report
          - other
          type: string
          description: |-
            * `manual` - 使用手册
            * `certificate` - 合格证书
            * `warranty` - 保修单
            * `photo` - 照片
            * `report` - 检测报告
            * `other` - 其他
          x-spec-enum-id: 39e015d2adaa1377
          title: 附件类型
        uploaded_by:
          type: string
          format: uuid
          readOnly: true
          nullable: true
          title: 上传者
        uploaded_by_name:
          type: string
          readOnly: true
        uploaded_at:
          type: string
          format: date-time
          readOnly: true
          title: 上传时间
      required:
      - device
      - device_name
      - file
      - file_size
      - file_size_mb
      - id
      - name
      - uploaded_at
      - uploaded_by
      - uploaded_by_name
    DeviceAttachmentRequest:
      type: object
      description: 设备附件序列化器
      properties:
        device:
          type: string
          format: uuid
          title: 设备
        name:
          type: string
          minLength: 1
          title: 附件名称
          maxLength: 200
        description:
          type: string
          nullable: true
          title: 描述
        file:
          type: string
          format: binary
          title: 文件
        attachment_type:
          enum:
          - manual
          - certificate
          - warranty
          - photo
          - report
          - other
          type: string
          description: |-
            * `manual` - 使用手册
            * `certificate` - 合格证书
            * `warranty` - 保修单
            * `photo` - 照片
            * `report` - 检测报告
            * `other` - 其他
          x-spec-enum-id: 39e015d2adaa1377
          title: 附件类型
      required:
      - device
      - file
      - name
    DeviceCategory:
      type: object
      description: 设备分类序列化器
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        name:
          type: string
          title: 分类名称
          maxLength: 100
        code:
          type: string
          title: 分类代码
          maxLength: 20
        description:
          type: string
          nullable: true
          title: 分类描述
        parent:
          type: string
          format: uuid
          nullable: true
          title: 父分类
        icon:
          type: string
          nullable: true
          title: 图标
          maxLength: 50
        color:
          type: string
          title: 颜色
          maxLength: 7
        sort_order:
          type: integer
          maximum: 922337*************
          minimum: 0
          format: int64
          title: 排序
        is_active:
          type: boolean
          title: 是否激活
        full_path:
          type: string
          readOnly: true
        children_count:
          type: string
          readOnly: true
        device_count:
          type: string
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 创建时间
        updated_at:
          type: string
          format: date-time
          readOnly: true
          title: 更新时间
        created_by:
          type: string
          format: uuid
          readOnly: true
          nullable: true
          title: 创建者
      required:
      - children_count
      - code
      - created_at
      - created_by
      - device_count
      - full_path
      - id
      - name
      - updated_at
    DeviceCategoryRequest:
      type: object
      description: 设备分类序列化器
      properties:
        name:
          type: string
          minLength: 1
          title: 分类名称
          maxLength: 100
        code:
          type: string
          minLength: 1
          title: 分类代码
          maxLength: 20
        description:
          type: string
          nullable: true
          title: 分类描述
        parent:
          type: string
          format: uuid
          nullable: true
          title: 父分类
        icon:
          type: string
          nullable: true
          title: 图标
          maxLength: 50
        color:
          type: string
          minLength: 1
          title: 颜色
          maxLength: 7
        sort_order:
          type: integer
          maximum: 922337*************
          minimum: 0
          format: int64
          title: 排序
        is_active:
          type: boolean
          title: 是否激活
      required:
      - code
      - name
    DeviceList:
      type: object
      description: 设备列表序列化器
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        asset_number:
          type: string
          title: 资产编号
          maxLength: 50
        name:
          type: string
          title: 设备名称
          maxLength: 200
        device_model_name:
          type: string
          readOnly: true
        manufacturer_name:
          type: string
          readOnly: true
        category_name:
          type: string
          readOnly: true
        department:
          type: string
          nullable: true
          title: 所属科室
          maxLength: 100
        location:
          type: string
          nullable: true
          title: 具体位置
          maxLength: 200
        responsible_person_name:
          type: string
          readOnly: true
        status:
          enum:
          - normal
          - maintenance
          - repair
          - fault
          - retired
          - idle
          type: string
          description: |-
            * `normal` - 正常
            * `maintenance` - 维护中
            * `repair` - 维修中
            * `fault` - 故障
            * `retired` - 报废
            * `idle` - 闲置
          x-spec-enum-id: 69dbaf18a0c31a0a
          title: 设备状态
        usage_status:
          enum:
          - in_use
          - available
          - unavailable
          - reserved
          type: string
          description: |-
            * `in_use` - 使用中
            * `available` - 可用
            * `unavailable` - 不可用
            * `reserved` - 预约中
          x-spec-enum-id: b23b06164f8cd341
          title: 使用状态
        importance:
          enum:
          - critical
          - important
          - normal
          - low
          type: string
          description: |-
            * `critical` - 关键
            * `important` - 重要
            * `normal` - 一般
            * `low` - 较低
          x-spec-enum-id: c9ebac9f4bc32fe0
          title: 重要程度
        status_color:
          type: string
          readOnly: true
        is_under_warranty:
          type: string
          readOnly: true
        last_check_date:
          type: string
          format: date-time
          nullable: true
          title: 最后检查时间
        next_check_date:
          type: string
          format: date
          nullable: true
          title: 下次检查日期
      required:
      - asset_number
      - category_name
      - device_model_name
      - id
      - is_under_warranty
      - manufacturer_name
      - name
      - responsible_person_name
      - status_color
    DeviceLocation:
      type: object
      description: 设备位置序列化器
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        name:
          type: string
          title: 位置名称
          maxLength: 100
        code:
          type: string
          title: 位置代码
          maxLength: 20
        description:
          type: string
          nullable: true
          title: 描述
        parent:
          type: string
          format: uuid
          nullable: true
          title: 父位置
        location_type:
          enum:
          - building
          - floor
          - department
          - room
          - area
          type: string
          description: |-
            * `building` - 建筑
            * `floor` - 楼层
            * `department` - 科室
            * `room` - 房间
            * `area` - 区域
          x-spec-enum-id: 04ce14d026949d40
          title: 位置类型
        manager:
          type: string
          format: uuid
          nullable: true
          title: 负责人
        manager_name:
          type: string
          readOnly: true
        is_active:
          type: boolean
          title: 是否激活
        full_path:
          type: string
          readOnly: true
        device_count:
          type: string
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 创建时间
        updated_at:
          type: string
          format: date-time
          readOnly: true
          title: 更新时间
      required:
      - code
      - created_at
      - device_count
      - full_path
      - id
      - manager_name
      - name
      - updated_at
    DeviceLocationRequest:
      type: object
      description: 设备位置序列化器
      properties:
        name:
          type: string
          minLength: 1
          title: 位置名称
          maxLength: 100
        code:
          type: string
          minLength: 1
          title: 位置代码
          maxLength: 20
        description:
          type: string
          nullable: true
          title: 描述
        parent:
          type: string
          format: uuid
          nullable: true
          title: 父位置
        location_type:
          enum:
          - building
          - floor
          - department
          - room
          - area
          type: string
          description: |-
            * `building` - 建筑
            * `floor` - 楼层
            * `department` - 科室
            * `room` - 房间
            * `area` - 区域
          x-spec-enum-id: 04ce14d026949d40
          title: 位置类型
        manager:
          type: string
          format: uuid
          nullable: true
          title: 负责人
        is_active:
          type: boolean
          title: 是否激活
      required:
      - code
      - name
    DeviceManufacturer:
      type: object
      description: 设备制造商序列化器
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        name:
          type: string
          title: 制造商名称
          maxLength: 200
        code:
          type: string
          title: 制造商代码
          maxLength: 50
        description:
          type: string
          nullable: true
          title: 描述
        contact_person:
          type: string
          nullable: true
          title: 联系人
          maxLength: 100
        phone:
          type: string
          nullable: true
          title: 电话
          maxLength: 20
        email:
          type: string
          format: email
          nullable: true
          title: 邮箱
          maxLength: 254
        address:
          type: string
          nullable: true
          title: 地址
        website:
          type: string
          format: uri
          nullable: true
          title: 网站
          maxLength: 200
        is_active:
          type: boolean
          title: 是否激活
        device_model_count:
          type: string
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 创建时间
        updated_at:
          type: string
          format: date-time
          readOnly: true
          title: 更新时间
      required:
      - code
      - created_at
      - device_model_count
      - id
      - name
      - updated_at
    DeviceManufacturerRequest:
      type: object
      description: 设备制造商序列化器
      properties:
        name:
          type: string
          minLength: 1
          title: 制造商名称
          maxLength: 200
        code:
          type: string
          minLength: 1
          title: 制造商代码
          maxLength: 50
        description:
          type: string
          nullable: true
          title: 描述
        contact_person:
          type: string
          nullable: true
          title: 联系人
          maxLength: 100
        phone:
          type: string
          nullable: true
          title: 电话
          maxLength: 20
        email:
          type: string
          format: email
          nullable: true
          title: 邮箱
          maxLength: 254
        address:
          type: string
          nullable: true
          title: 地址
        website:
          type: string
          format: uri
          nullable: true
          title: 网站
          maxLength: 200
        is_active:
          type: boolean
          title: 是否激活
      required:
      - code
      - name
    DeviceModel:
      type: object
      description: 设备型号序列化器
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        name:
          type: string
          title: 型号名称
          maxLength: 200
        model_number:
          type: string
          title: 型号编号
          maxLength: 100
        manufacturer:
          type: string
          format: uuid
          title: 制造商
        manufacturer_name:
          type: string
          readOnly: true
        category:
          type: string
          format: uuid
          title: 设备分类
        category_name:
          type: string
          readOnly: true
        specifications:
          title: 技术规格
        description:
          type: string
          nullable: true
          title: 描述
        image:
          type: string
          format: uri
          nullable: true
          title: 产品图片
        manual_url:
          type: string
          format: uri
          nullable: true
          title: 说明书链接
          maxLength: 200
        purchase_price:
          type: string
          format: decimal
          pattern: ^-?\d{0,10}(?:\.\d{0,2})?$
          nullable: true
          title: 采购价格
        is_active:
          type: boolean
          title: 是否激活
        device_count:
          type: string
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 创建时间
        updated_at:
          type: string
          format: date-time
          readOnly: true
          title: 更新时间
      required:
      - category
      - category_name
      - created_at
      - device_count
      - id
      - manufacturer
      - manufacturer_name
      - model_number
      - name
      - updated_at
    DeviceModelRequest:
      type: object
      description: 设备型号序列化器
      properties:
        name:
          type: string
          minLength: 1
          title: 型号名称
          maxLength: 200
        model_number:
          type: string
          minLength: 1
          title: 型号编号
          maxLength: 100
        manufacturer:
          type: string
          format: uuid
          title: 制造商
        category:
          type: string
          format: uuid
          title: 设备分类
        specifications:
          title: 技术规格
        description:
          type: string
          nullable: true
          title: 描述
        image:
          type: string
          format: binary
          nullable: true
          title: 产品图片
        manual_url:
          type: string
          format: uri
          nullable: true
          title: 说明书链接
          maxLength: 200
        purchase_price:
          type: string
          format: decimal
          pattern: ^-?\d{0,10}(?:\.\d{0,2})?$
          nullable: true
          title: 采购价格
        is_active:
          type: boolean
          title: 是否激活
      required:
      - category
      - manufacturer
      - model_number
      - name
    DeviceRequest:
      type: object
      description: 设备序列化器
      properties:
        asset_number:
          type: string
          minLength: 1
          title: 资产编号
          maxLength: 50
        serial_number:
          type: string
          nullable: true
          title: 序列号
          maxLength: 100
        name:
          type: string
          minLength: 1
          title: 设备名称
          maxLength: 200
        device_model:
          type: string
          format: uuid
          title: 设备型号
        department:
          type: string
          nullable: true
          title: 所属科室
          maxLength: 100
        location:
          type: string
          nullable: true
          title: 具体位置
          maxLength: 200
        room:
          type: string
          nullable: true
          title: 房间号
          maxLength: 50
        responsible_person:
          type: string
          format: uuid
          nullable: true
          title: 责任人
        operator:
          type: string
          format: uuid
          nullable: true
          title: 操作员
        purchase_date:
          type: string
          format: date
          nullable: true
          title: 采购日期
        purchase_price:
          type: string
          format: decimal
          pattern: ^-?\d{0,10}(?:\.\d{0,2})?$
          nullable: true
          title: 采购价格
        supplier:
          type: string
          nullable: true
          title: 供应商
          maxLength: 200
        warranty_start_date:
          type: string
          format: date
          nullable: true
          title: 保修开始日期
        warranty_end_date:
          type: string
          format: date
          nullable: true
          title: 保修结束日期
        status:
          enum:
          - normal
          - maintenance
          - repair
          - fault
          - retired
          - idle
          type: string
          description: |-
            * `normal` - 正常
            * `maintenance` - 维护中
            * `repair` - 维修中
            * `fault` - 故障
            * `retired` - 报废
            * `idle` - 闲置
          x-spec-enum-id: 69dbaf18a0c31a0a
          title: 设备状态
        usage_status:
          enum:
          - in_use
          - available
          - unavailable
          - reserved
          type: string
          description: |-
            * `in_use` - 使用中
            * `available` - 可用
            * `unavailable` - 不可用
            * `reserved` - 预约中
          x-spec-enum-id: b23b06164f8cd341
          title: 使用状态
        importance:
          enum:
          - critical
          - important
          - normal
          - low
          type: string
          description: |-
            * `critical` - 关键
            * `important` - 重要
            * `normal` - 一般
            * `low` - 较低
          x-spec-enum-id: c9ebac9f4bc32fe0
          title: 重要程度
        parameters:
          title: 设备参数
        notes:
          type: string
          nullable: true
          title: 备注
        last_check_date:
          type: string
          format: date-time
          nullable: true
          title: 最后检查时间
        last_check_result:
          type: string
          nullable: true
          title: 最后检查结果
        next_check_date:
          type: string
          format: date
          nullable: true
          title: 下次检查日期
        is_active:
          type: boolean
          title: 是否激活
      required:
      - asset_number
      - device_model
      - name
    FaultList:
      type: object
      description: 故障报告列表序列化器
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        device_asset_number:
          type: string
          readOnly: true
        device_name:
          type: string
          readOnly: true
        title:
          type: string
          title: 故障标题
          maxLength: 200
        severity:
          enum:
          - low
          - medium
          - high
          - critical
          type: string
          description: |-
            * `low` - 轻微
            * `medium` - 中等
            * `high` - 严重
            * `critical` - 致命
          x-spec-enum-id: 9c552eeb562b30ce
          title: 严重程度
        fault_type:
          enum:
          - mechanical
          - electrical
          - software
          - calibration
          - wear
          - other
          type: string
          description: |-
            * `mechanical` - 机械故障
            * `electrical` - 电气故障
            * `software` - 软件故障
            * `calibration` - 校准问题
            * `wear` - 磨损
            * `other` - 其他
          x-spec-enum-id: 03b6f600188dbbbc
          title: 故障类型
        status:
          enum:
          - open
          - assigned
          - in_progress
          - resolved
          - closed
          type: string
          description: |-
            * `open` - 待处理
            * `assigned` - 已分配
            * `in_progress` - 处理中
            * `resolved` - 已解决
            * `closed` - 已关闭
          x-spec-enum-id: a04ace4625a8999f
          title: 处理状态
        occurred_at:
          type: string
          format: date-time
          title: 发生时间
        reported_by_name:
          type: string
          readOnly: true
        assigned_to_name:
          type: string
          readOnly: true
      required:
      - assigned_to_name
      - device_asset_number
      - device_name
      - id
      - occurred_at
      - reported_by_name
      - title
    FaultReport:
      type: object
      description: 故障报告序列化器
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        device:
          type: string
          format: uuid
          title: 设备
        device_name:
          type: string
          readOnly: true
        device_asset_number:
          type: string
          readOnly: true
        title:
          type: string
          title: 故障标题
          maxLength: 200
        description:
          type: string
          title: 故障描述
        fault_code:
          type: string
          nullable: true
          title: 故障代码
          maxLength: 50
        severity:
          enum:
          - low
          - medium
          - high
          - critical
          type: string
          description: |-
            * `low` - 轻微
            * `medium` - 中等
            * `high` - 严重
            * `critical` - 致命
          x-spec-enum-id: 9c552eeb562b30ce
          title: 严重程度
        fault_type:
          enum:
          - mechanical
          - electrical
          - software
          - calibration
          - wear
          - other
          type: string
          description: |-
            * `mechanical` - 机械故障
            * `electrical` - 电气故障
            * `software` - 软件故障
            * `calibration` - 校准问题
            * `wear` - 磨损
            * `other` - 其他
          x-spec-enum-id: 03b6f600188dbbbc
          title: 故障类型
        occurred_at:
          type: string
          format: date-time
          title: 发生时间
        reported_at:
          type: string
          format: date-time
          readOnly: true
          title: 报告时间
        reported_by:
          type: string
          format: uuid
          readOnly: true
          nullable: true
          title: 报告人
        reported_by_name:
          type: string
          readOnly: true
        status:
          enum:
          - open
          - assigned
          - in_progress
          - resolved
          - closed
          type: string
          description: |-
            * `open` - 待处理
            * `assigned` - 已分配
            * `in_progress` - 处理中
            * `resolved` - 已解决
            * `closed` - 已关闭
          x-spec-enum-id: a04ace4625a8999f
          title: 处理状态
        assigned_to:
          type: string
          format: uuid
          nullable: true
          title: 分配给
        assigned_to_name:
          type: string
          readOnly: true
        resolution:
          type: string
          nullable: true
          title: 解决方案
        resolved_at:
          type: string
          format: date-time
          nullable: true
          title: 解决时间
        resolved_by:
          type: string
          format: uuid
          nullable: true
          title: 解决人
        resolved_by_name:
          type: string
          readOnly: true
        maintenance_record:
          type: string
          format: uuid
          nullable: true
          title: 关联维护记录
        maintenance_record_title:
          type: string
          readOnly: true
        resolution_time_display:
          type: string
          readOnly: true
      required:
      - assigned_to_name
      - description
      - device
      - device_asset_number
      - device_name
      - id
      - maintenance_record_title
      - occurred_at
      - reported_at
      - reported_by
      - reported_by_name
      - resolution_time_display
      - resolved_by_name
      - title
    FaultReportRequest:
      type: object
      description: 故障报告序列化器
      properties:
        device:
          type: string
          format: uuid
          title: 设备
        title:
          type: string
          minLength: 1
          title: 故障标题
          maxLength: 200
        description:
          type: string
          minLength: 1
          title: 故障描述
        fault_code:
          type: string
          nullable: true
          title: 故障代码
          maxLength: 50
        severity:
          enum:
          - low
          - medium
          - high
          - critical
          type: string
          description: |-
            * `low` - 轻微
            * `medium` - 中等
            * `high` - 严重
            * `critical` - 致命
          x-spec-enum-id: 9c552eeb562b30ce
          title: 严重程度
        fault_type:
          enum:
          - mechanical
          - electrical
          - software
          - calibration
          - wear
          - other
          type: string
          description: |-
            * `mechanical` - 机械故障
            * `electrical` - 电气故障
            * `software` - 软件故障
            * `calibration` - 校准问题
            * `wear` - 磨损
            * `other` - 其他
          x-spec-enum-id: 03b6f600188dbbbc
          title: 故障类型
        occurred_at:
          type: string
          format: date-time
          title: 发生时间
        status:
          enum:
          - open
          - assigned
          - in_progress
          - resolved
          - closed
          type: string
          description: |-
            * `open` - 待处理
            * `assigned` - 已分配
            * `in_progress` - 处理中
            * `resolved` - 已解决
            * `closed` - 已关闭
          x-spec-enum-id: a04ace4625a8999f
          title: 处理状态
        assigned_to:
          type: string
          format: uuid
          nullable: true
          title: 分配给
        resolution:
          type: string
          nullable: true
          title: 解决方案
        resolved_at:
          type: string
          format: date-time
          nullable: true
          title: 解决时间
        resolved_by:
          type: string
          format: uuid
          nullable: true
          title: 解决人
        maintenance_record:
          type: string
          format: uuid
          nullable: true
          title: 关联维护记录
      required:
      - description
      - device
      - occurred_at
      - title
    Icon:
      type: object
      description: 图标序列化器
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        icon_set:
          type: string
          format: uuid
          title: 图标集
        icon_set_name:
          type: string
          readOnly: true
        name:
          type: string
          title: 图标名称
          maxLength: 100
        display_name:
          type: string
          title: 显示名称
          maxLength: 100
        description:
          type: string
          nullable: true
          title: 描述
        icon_class:
          type: string
          nullable: true
          title: 图标类名
          maxLength: 100
        svg_content:
          type: string
          nullable: true
          title: SVG内容
        image_url:
          type: string
          format: uri
          nullable: true
          title: 图片URL
          maxLength: 200
        category:
          type: string
          nullable: true
          title: 分类
          maxLength: 50
        tags:
          title: 标签
        default_size:
          type: string
          title: 默认大小
          maxLength: 20
        default_color:
          type: string
          title: 默认颜色
          maxLength: 7
        full_class:
          type: string
          readOnly: true
        usage_count:
          type: string
          readOnly: true
        is_active:
          type: boolean
          title: 是否激活
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 创建时间
        updated_at:
          type: string
          format: date-time
          readOnly: true
          title: 更新时间
      required:
      - created_at
      - display_name
      - full_class
      - icon_set
      - icon_set_name
      - id
      - name
      - updated_at
      - usage_count
    IconList:
      type: object
      description: 图标列表序列化器
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        name:
          type: string
          title: 图标名称
          maxLength: 100
        display_name:
          type: string
          title: 显示名称
          maxLength: 100
        icon_set_name:
          type: string
          readOnly: true
        icon_class:
          type: string
          nullable: true
          title: 图标类名
          maxLength: 100
        svg_content:
          type: string
          nullable: true
          title: SVG内容
        image_url:
          type: string
          format: uri
          nullable: true
          title: 图片URL
          maxLength: 200
        category:
          type: string
          nullable: true
          title: 分类
          maxLength: 50
        tags:
          title: 标签
        full_class:
          type: string
          readOnly: true
      required:
      - display_name
      - full_class
      - icon_set_name
      - id
      - name
    IconRequest:
      type: object
      description: 图标序列化器
      properties:
        icon_set:
          type: string
          format: uuid
          title: 图标集
        name:
          type: string
          minLength: 1
          title: 图标名称
          maxLength: 100
        display_name:
          type: string
          minLength: 1
          title: 显示名称
          maxLength: 100
        description:
          type: string
          nullable: true
          title: 描述
        icon_class:
          type: string
          nullable: true
          title: 图标类名
          maxLength: 100
        svg_content:
          type: string
          nullable: true
          title: SVG内容
        image_url:
          type: string
          format: uri
          nullable: true
          title: 图片URL
          maxLength: 200
        category:
          type: string
          nullable: true
          title: 分类
          maxLength: 50
        tags:
          title: 标签
        default_size:
          type: string
          minLength: 1
          title: 默认大小
          maxLength: 20
        default_color:
          type: string
          minLength: 1
          title: 默认颜色
          maxLength: 7
        is_active:
          type: boolean
          title: 是否激活
      required:
      - display_name
      - icon_set
      - name
    IconSet:
      type: object
      description: 图标集序列化器
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        name:
          type: string
          title: 图标集名称
          maxLength: 50
        display_name:
          type: string
          title: 显示名称
          maxLength: 100
        description:
          type: string
          nullable: true
          title: 描述
        version:
          type: string
          title: 版本
          maxLength: 20
        base_url:
          type: string
          format: uri
          nullable: true
          title: 基础URL
          maxLength: 200
        css_url:
          type: string
          format: uri
          nullable: true
          title: CSS文件URL
          maxLength: 200
        js_url:
          type: string
          format: uri
          nullable: true
          title: JS文件URL
          maxLength: 200
        icon_type:
          enum:
          - font
          - svg
          - image
          type: string
          description: |-
            * `font` - 字体图标
            * `svg` - SVG图标
            * `image` - 图片图标
          x-spec-enum-id: 6763b15f00eb4732
          title: 图标类型
        prefix:
          type: string
          title: 图标前缀
          maxLength: 20
        is_active:
          type: boolean
          title: 是否激活
        is_default:
          type: boolean
          title: 是否默认图标集
        icon_count:
          type: string
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 创建时间
        updated_at:
          type: string
          format: date-time
          readOnly: true
          title: 更新时间
      required:
      - created_at
      - display_name
      - icon_count
      - id
      - name
      - updated_at
      - version
    IconSetList:
      type: object
      description: 图标集列表序列化器
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        name:
          type: string
          title: 图标集名称
          maxLength: 50
        display_name:
          type: string
          title: 显示名称
          maxLength: 100
        description:
          type: string
          nullable: true
          title: 描述
        icon_type:
          enum:
          - font
          - svg
          - image
          type: string
          description: |-
            * `font` - 字体图标
            * `svg` - SVG图标
            * `image` - 图片图标
          x-spec-enum-id: 6763b15f00eb4732
          title: 图标类型
        is_active:
          type: boolean
          title: 是否激活
        is_default:
          type: boolean
          title: 是否默认图标集
        icon_count:
          type: string
          readOnly: true
      required:
      - display_name
      - icon_count
      - id
      - name
    IconSetRequest:
      type: object
      description: 图标集序列化器
      properties:
        name:
          type: string
          minLength: 1
          title: 图标集名称
          maxLength: 50
        display_name:
          type: string
          minLength: 1
          title: 显示名称
          maxLength: 100
        description:
          type: string
          nullable: true
          title: 描述
        version:
          type: string
          minLength: 1
          title: 版本
          maxLength: 20
        base_url:
          type: string
          format: uri
          nullable: true
          title: 基础URL
          maxLength: 200
        css_url:
          type: string
          format: uri
          nullable: true
          title: CSS文件URL
          maxLength: 200
        js_url:
          type: string
          format: uri
          nullable: true
          title: JS文件URL
          maxLength: 200
        icon_type:
          enum:
          - font
          - svg
          - image
          type: string
          description: |-
            * `font` - 字体图标
            * `svg` - SVG图标
            * `image` - 图片图标
          x-spec-enum-id: 6763b15f00eb4732
          title: 图标类型
        prefix:
          type: string
          minLength: 1
          title: 图标前缀
          maxLength: 20
        is_active:
          type: boolean
          title: 是否激活
        is_default:
          type: boolean
          title: 是否默认图标集
      required:
      - display_name
      - name
      - version
    Item:
      type: object
      description: 物品序列化器
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        name:
          type: string
          title: 物品名称
          maxLength: 200
        code:
          type: string
          title: 物品编码
          maxLength: 50
        description:
          type: string
          nullable: true
          title: 描述
        category:
          type: string
          format: uuid
          title: 分类
        category_name:
          type: string
          readOnly: true
        specifications:
          title: 规格参数
        unit:
          type: string
          title: 单位
          maxLength: 20
        brand:
          type: string
          nullable: true
          title: 品牌
          maxLength: 100
        model:
          type: string
          nullable: true
          title: 型号
          maxLength: 100
        standard_price:
          type: string
          format: decimal
          pattern: ^-?\d{0,10}(?:\.\d{0,2})?$
          nullable: true
          title: 标准价格
        min_stock:
          type: integer
          maximum: 922337*************
          minimum: 0
          format: int64
          title: 最小库存
        max_stock:
          type: integer
          maximum: 922337*************
          minimum: 0
          format: int64
          title: 最大库存
        reorder_point:
          type: integer
          maximum: 922337*************
          minimum: 0
          format: int64
          title: 补货点
        item_type:
          enum:
          - consumable
          - spare_part
          - tool
          - material
          - other
          type: string
          description: |-
            * `consumable` - 消耗品
            * `spare_part` - 备件
            * `tool` - 工具
            * `material` - 材料
            * `other` - 其他
          x-spec-enum-id: f23810a86fc098ba
          title: 物品类型
        image:
          type: string
          format: uri
          nullable: true
          title: 物品图片
        is_active:
          type: boolean
          title: 是否激活
        current_stock:
          type: string
          readOnly: true
        is_low_stock:
          type: string
          readOnly: true
        stock_status:
          type: string
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 创建时间
        updated_at:
          type: string
          format: date-time
          readOnly: true
          title: 更新时间
        created_by:
          type: string
          format: uuid
          readOnly: true
          nullable: true
          title: 创建者
        created_by_name:
          type: string
          readOnly: true
      required:
      - category
      - category_name
      - code
      - created_at
      - created_by
      - created_by_name
      - current_stock
      - id
      - is_low_stock
      - name
      - stock_status
      - updated_at
    ItemCategory:
      type: object
      description: 物品分类序列化器
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        name:
          type: string
          title: 分类名称
          maxLength: 100
        code:
          type: string
          title: 分类代码
          maxLength: 20
        description:
          type: string
          nullable: true
          title: 分类描述
        parent:
          type: string
          format: uuid
          nullable: true
          title: 父分类
        icon:
          type: string
          nullable: true
          title: 图标
          maxLength: 50
        color:
          type: string
          title: 颜色
          maxLength: 7
        sort_order:
          type: integer
          maximum: 922337*************
          minimum: 0
          format: int64
          title: 排序
        is_active:
          type: boolean
          title: 是否激活
        full_path:
          type: string
          readOnly: true
        children_count:
          type: string
          readOnly: true
        item_count:
          type: string
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 创建时间
        updated_at:
          type: string
          format: date-time
          readOnly: true
          title: 更新时间
      required:
      - children_count
      - code
      - created_at
      - full_path
      - id
      - item_count
      - name
      - updated_at
    ItemCategoryRequest:
      type: object
      description: 物品分类序列化器
      properties:
        name:
          type: string
          minLength: 1
          title: 分类名称
          maxLength: 100
        code:
          type: string
          minLength: 1
          title: 分类代码
          maxLength: 20
        description:
          type: string
          nullable: true
          title: 分类描述
        parent:
          type: string
          format: uuid
          nullable: true
          title: 父分类
        icon:
          type: string
          nullable: true
          title: 图标
          maxLength: 50
        color:
          type: string
          minLength: 1
          title: 颜色
          maxLength: 7
        sort_order:
          type: integer
          maximum: 922337*************
          minimum: 0
          format: int64
          title: 排序
        is_active:
          type: boolean
          title: 是否激活
      required:
      - code
      - name
    ItemList:
      type: object
      description: 物品列表序列化器
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        name:
          type: string
          title: 物品名称
          maxLength: 200
        code:
          type: string
          title: 物品编码
          maxLength: 50
        category_name:
          type: string
          readOnly: true
        unit:
          type: string
          title: 单位
          maxLength: 20
        brand:
          type: string
          nullable: true
          title: 品牌
          maxLength: 100
        standard_price:
          type: string
          format: decimal
          pattern: ^-?\d{0,10}(?:\.\d{0,2})?$
          nullable: true
          title: 标准价格
        current_stock:
          type: string
          readOnly: true
        reorder_point:
          type: integer
          maximum: 922337*************
          minimum: 0
          format: int64
          title: 补货点
        stock_status:
          type: string
          readOnly: true
        is_active:
          type: boolean
          title: 是否激活
      required:
      - category_name
      - code
      - current_stock
      - id
      - name
      - stock_status
    ItemRequest:
      type: object
      description: 物品序列化器
      properties:
        name:
          type: string
          minLength: 1
          title: 物品名称
          maxLength: 200
        code:
          type: string
          minLength: 1
          title: 物品编码
          maxLength: 50
        description:
          type: string
          nullable: true
          title: 描述
        category:
          type: string
          format: uuid
          title: 分类
        specifications:
          title: 规格参数
        unit:
          type: string
          minLength: 1
          title: 单位
          maxLength: 20
        brand:
          type: string
          nullable: true
          title: 品牌
          maxLength: 100
        model:
          type: string
          nullable: true
          title: 型号
          maxLength: 100
        standard_price:
          type: string
          format: decimal
          pattern: ^-?\d{0,10}(?:\.\d{0,2})?$
          nullable: true
          title: 标准价格
        min_stock:
          type: integer
          maximum: 922337*************
          minimum: 0
          format: int64
          title: 最小库存
        max_stock:
          type: integer
          maximum: 922337*************
          minimum: 0
          format: int64
          title: 最大库存
        reorder_point:
          type: integer
          maximum: 922337*************
          minimum: 0
          format: int64
          title: 补货点
        item_type:
          enum:
          - consumable
          - spare_part
          - tool
          - material
          - other
          type: string
          description: |-
            * `consumable` - 消耗品
            * `spare_part` - 备件
            * `tool` - 工具
            * `material` - 材料
            * `other` - 其他
          x-spec-enum-id: f23810a86fc098ba
          title: 物品类型
        image:
          type: string
          format: binary
          nullable: true
          title: 物品图片
        is_active:
          type: boolean
          title: 是否激活
      required:
      - category
      - code
      - name
    MaintenanceList:
      type: object
      description: 维护记录列表序列化器
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        device_asset_number:
          type: string
          readOnly: true
        device_name:
          type: string
          readOnly: true
        maintenance_type_name:
          type: string
          readOnly: true
        title:
          type: string
          title: 维护标题
          maxLength: 200
        scheduled_date:
          type: string
          format: date-time
          title: 计划时间
        status:
          enum:
          - scheduled
          - in_progress
          - completed
          - cancelled
          - failed
          type: string
          description: |-
            * `scheduled` - 已计划
            * `in_progress` - 进行中
            * `completed` - 已完成
            * `cancelled` - 已取消
            * `failed` - 失败
          x-spec-enum-id: 9ebcb723b8f8041e
          title: 状态
        performed_by_name:
          type: string
          readOnly: true
        duration_display:
          type: string
          readOnly: true
        rating:
          enum:
          - 1
          - 2
          - 3
          - 4
          - 5
          - null
          type: integer
          description: |-
            * `1` - 很差
            * `2` - 差
            * `3` - 一般
            * `4` - 好
            * `5` - 很好
          x-spec-enum-id: affa594dfcb08b0b
          nullable: true
          title: 维护评级
          minimum: 0
          maximum: 922337*************
      required:
      - device_asset_number
      - device_name
      - duration_display
      - id
      - maintenance_type_name
      - performed_by_name
      - scheduled_date
      - title
    MaintenancePlan:
      type: object
      description: 维护计划序列化器
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        device:
          type: string
          format: uuid
          title: 设备
        device_name:
          type: string
          readOnly: true
        device_asset_number:
          type: string
          readOnly: true
        maintenance_type:
          type: string
          format: uuid
          title: 维护类型
        maintenance_type_name:
          type: string
          readOnly: true
        title:
          type: string
          title: 计划标题
          maxLength: 200
        description:
          type: string
          nullable: true
          title: 计划描述
        start_date:
          type: string
          format: date
          title: 开始日期
        interval_days:
          type: integer
          maximum: 922337*************
          minimum: 0
          format: int64
          title: 间隔天数
        estimated_duration:
          type: integer
          maximum: 922337*************
          minimum: 0
          format: int64
          title: 预计时长(分钟)
        next_maintenance_date:
          type: string
          format: date
          title: 下次维护日期
        assigned_to:
          type: string
          format: uuid
          nullable: true
          title: 负责人
        assigned_to_name:
          type: string
          readOnly: true
        priority:
          enum:
          - low
          - normal
          - high
          - urgent
          type: string
          description: |-
            * `low` - 低
            * `normal` - 普通
            * `high` - 高
            * `urgent` - 紧急
          x-spec-enum-id: b4c18fd9205140ec
          title: 优先级
        is_active:
          type: boolean
          title: 是否激活
        is_overdue:
          type: string
          readOnly: true
        days_until_maintenance:
          type: string
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 创建时间
        updated_at:
          type: string
          format: date-time
          readOnly: true
          title: 更新时间
        created_by:
          type: string
          format: uuid
          readOnly: true
          nullable: true
          title: 创建者
        created_by_name:
          type: string
          readOnly: true
      required:
      - assigned_to_name
      - created_at
      - created_by
      - created_by_name
      - days_until_maintenance
      - device
      - device_asset_number
      - device_name
      - estimated_duration
      - id
      - interval_days
      - is_overdue
      - maintenance_type
      - maintenance_type_name
      - next_maintenance_date
      - start_date
      - title
      - updated_at
    MaintenancePlanRequest:
      type: object
      description: 维护计划序列化器
      properties:
        device:
          type: string
          format: uuid
          title: 设备
        maintenance_type:
          type: string
          format: uuid
          title: 维护类型
        title:
          type: string
          minLength: 1
          title: 计划标题
          maxLength: 200
        description:
          type: string
          nullable: true
          title: 计划描述
        start_date:
          type: string
          format: date
          title: 开始日期
        interval_days:
          type: integer
          maximum: 922337*************
          minimum: 0
          format: int64
          title: 间隔天数
        estimated_duration:
          type: integer
          maximum: 922337*************
          minimum: 0
          format: int64
          title: 预计时长(分钟)
        next_maintenance_date:
          type: string
          format: date
          title: 下次维护日期
        assigned_to:
          type: string
          format: uuid
          nullable: true
          title: 负责人
        priority:
          enum:
          - low
          - normal
          - high
          - urgent
          type: string
          description: |-
            * `low` - 低
            * `normal` - 普通
            * `high` - 高
            * `urgent` - 紧急
          x-spec-enum-id: b4c18fd9205140ec
          title: 优先级
        is_active:
          type: boolean
          title: 是否激活
      required:
      - device
      - estimated_duration
      - interval_days
      - maintenance_type
      - next_maintenance_date
      - start_date
      - title
    MaintenanceRecord:
      type: object
      description: 维护记录序列化器
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        device:
          type: string
          format: uuid
          title: 设备
        device_name:
          type: string
          readOnly: true
        device_asset_number:
          type: string
          readOnly: true
        maintenance_plan:
          type: string
          format: uuid
          nullable: true
          title: 维护计划
        maintenance_plan_title:
          type: string
          readOnly: true
        maintenance_type:
          type: string
          format: uuid
          title: 维护类型
        maintenance_type_name:
          type: string
          readOnly: true
        title:
          type: string
          title: 维护标题
          maxLength: 200
        description:
          type: string
          title: 维护描述
        scheduled_date:
          type: string
          format: date-time
          title: 计划时间
        start_time:
          type: string
          format: date-time
          nullable: true
          title: 开始时间
        end_time:
          type: string
          format: date-time
          nullable: true
          title: 结束时间
        actual_duration:
          type: integer
          readOnly: true
          nullable: true
          title: 实际时长(分钟)
        duration_display:
          type: string
          readOnly: true
        performed_by:
          type: string
          format: uuid
          nullable: true
          title: 执行人
        performed_by_name:
          type: string
          readOnly: true
        status:
          enum:
          - scheduled
          - in_progress
          - completed
          - cancelled
          - failed
          type: string
          description: |-
            * `scheduled` - 已计划
            * `in_progress` - 进行中
            * `completed` - 已完成
            * `cancelled` - 已取消
            * `failed` - 失败
          x-spec-enum-id: 9ebcb723b8f8041e
          title: 状态
        result:
          type: string
          nullable: true
          title: 维护结果
        issues_found:
          type: string
          nullable: true
          title: 发现问题
        parts_replaced:
          title: 更换部件
        cost:
          type: string
          format: decimal
          pattern: ^-?\d{0,8}(?:\.\d{0,2})?$
          nullable: true
          title: 维护成本
        rating:
          enum:
          - 1
          - 2
          - 3
          - 4
          - 5
          - null
          type: integer
          description: |-
            * `1` - 很差
            * `2` - 差
            * `3` - 一般
            * `4` - 好
            * `5` - 很好
          x-spec-enum-id: affa594dfcb08b0b
          nullable: true
          title: 维护评级
          minimum: 0
          maximum: 922337*************
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 创建时间
        updated_at:
          type: string
          format: date-time
          readOnly: true
          title: 更新时间
        created_by:
          type: string
          format: uuid
          readOnly: true
          nullable: true
          title: 创建者
        created_by_name:
          type: string
          readOnly: true
      required:
      - actual_duration
      - created_at
      - created_by
      - created_by_name
      - description
      - device
      - device_asset_number
      - device_name
      - duration_display
      - id
      - maintenance_plan_title
      - maintenance_type
      - maintenance_type_name
      - performed_by_name
      - scheduled_date
      - title
      - updated_at
    MaintenanceRecordRequest:
      type: object
      description: 维护记录序列化器
      properties:
        device:
          type: string
          format: uuid
          title: 设备
        maintenance_plan:
          type: string
          format: uuid
          nullable: true
          title: 维护计划
        maintenance_type:
          type: string
          format: uuid
          title: 维护类型
        title:
          type: string
          minLength: 1
          title: 维护标题
          maxLength: 200
        description:
          type: string
          minLength: 1
          title: 维护描述
        scheduled_date:
          type: string
          format: date-time
          title: 计划时间
        start_time:
          type: string
          format: date-time
          nullable: true
          title: 开始时间
        end_time:
          type: string
          format: date-time
          nullable: true
          title: 结束时间
        performed_by:
          type: string
          format: uuid
          nullable: true
          title: 执行人
        status:
          enum:
          - scheduled
          - in_progress
          - completed
          - cancelled
          - failed
          type: string
          description: |-
            * `scheduled` - 已计划
            * `in_progress` - 进行中
            * `completed` - 已完成
            * `cancelled` - 已取消
            * `failed` - 失败
          x-spec-enum-id: 9ebcb723b8f8041e
          title: 状态
        result:
          type: string
          nullable: true
          title: 维护结果
        issues_found:
          type: string
          nullable: true
          title: 发现问题
        parts_replaced:
          title: 更换部件
        cost:
          type: string
          format: decimal
          pattern: ^-?\d{0,8}(?:\.\d{0,2})?$
          nullable: true
          title: 维护成本
        rating:
          enum:
          - 1
          - 2
          - 3
          - 4
          - 5
          - null
          type: integer
          description: |-
            * `1` - 很差
            * `2` - 差
            * `3` - 一般
            * `4` - 好
            * `5` - 很好
          x-spec-enum-id: affa594dfcb08b0b
          nullable: true
          title: 维护评级
          minimum: 0
          maximum: 922337*************
      required:
      - description
      - device
      - maintenance_type
      - scheduled_date
      - title
    MaintenanceType:
      type: object
      description: 维护类型序列化器
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        name:
          type: string
          title: 维护类型名称
          maxLength: 100
        code:
          type: string
          title: 类型代码
          maxLength: 20
        description:
          type: string
          nullable: true
          title: 描述
        default_duration:
          type: integer
          maximum: 922337*************
          minimum: 0
          format: int64
          title: 默认时长(分钟)
        default_interval_days:
          type: integer
          maximum: 922337*************
          minimum: 0
          format: int64
          title: 默认间隔(天)
        is_active:
          type: boolean
          title: 是否激活
        plan_count:
          type: string
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 创建时间
        updated_at:
          type: string
          format: date-time
          readOnly: true
          title: 更新时间
      required:
      - code
      - created_at
      - id
      - name
      - plan_count
      - updated_at
    MaintenanceTypeRequest:
      type: object
      description: 维护类型序列化器
      properties:
        name:
          type: string
          minLength: 1
          title: 维护类型名称
          maxLength: 100
        code:
          type: string
          minLength: 1
          title: 类型代码
          maxLength: 20
        description:
          type: string
          nullable: true
          title: 描述
        default_duration:
          type: integer
          maximum: 922337*************
          minimum: 0
          format: int64
          title: 默认时长(分钟)
        default_interval_days:
          type: integer
          maximum: 922337*************
          minimum: 0
          format: int64
          title: 默认间隔(天)
        is_active:
          type: boolean
          title: 是否激活
      required:
      - code
      - name
    PaginatedDashboardListList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/DashboardList'
    PaginatedDeviceCategoryList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/DeviceCategory'
    PaginatedDeviceListList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/DeviceList'
    PaginatedDeviceLocationList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/DeviceLocation'
    PaginatedDeviceManufacturerList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/DeviceManufacturer'
    PaginatedDeviceModelList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/DeviceModel'
    PaginatedFaultListList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/FaultList'
    PaginatedIconListList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/IconList'
    PaginatedIconSetListList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/IconSetList'
    PaginatedItemCategoryList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/ItemCategory'
    PaginatedItemListList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/ItemList'
    PaginatedMaintenanceListList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/MaintenanceList'
    PaginatedMaintenancePlanList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/MaintenancePlan'
    PaginatedMaintenanceTypeList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/MaintenanceType'
    PaginatedPurchaseOrderListList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/PurchaseOrderList'
    PaginatedReportInstanceListList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/ReportInstanceList'
    PaginatedReportTemplateListList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/ReportTemplateList'
    PaginatedStockMovementList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/StockMovement'
    PaginatedSupplierList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/Supplier'
    PaginatedThemeListList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/ThemeList'
    PatchedDashboardRequest:
      type: object
      description: 仪表板序列化器
      properties:
        name:
          type: string
          minLength: 1
          title: 仪表板名称
          maxLength: 200
        description:
          type: string
          nullable: true
          title: 描述
        layout:
          title: 布局配置
        widgets:
          title: 组件配置
        auto_refresh:
          type: boolean
          title: 自动刷新
        refresh_interval:
          type: integer
          maximum: 922337*************
          minimum: 0
          format: int64
          title: 刷新间隔(秒)
        is_public:
          type: boolean
          title: 是否公开
        allowed_users:
          type: array
          items:
            type: string
            format: uuid
            title: 允许访问的用户
          title: 允许访问的用户
        is_active:
          type: boolean
          title: 是否激活
    PatchedDeviceCategoryRequest:
      type: object
      description: 设备分类序列化器
      properties:
        name:
          type: string
          minLength: 1
          title: 分类名称
          maxLength: 100
        code:
          type: string
          minLength: 1
          title: 分类代码
          maxLength: 20
        description:
          type: string
          nullable: true
          title: 分类描述
        parent:
          type: string
          format: uuid
          nullable: true
          title: 父分类
        icon:
          type: string
          nullable: true
          title: 图标
          maxLength: 50
        color:
          type: string
          minLength: 1
          title: 颜色
          maxLength: 7
        sort_order:
          type: integer
          maximum: 922337*************
          minimum: 0
          format: int64
          title: 排序
        is_active:
          type: boolean
          title: 是否激活
    PatchedDeviceLocationRequest:
      type: object
      description: 设备位置序列化器
      properties:
        name:
          type: string
          minLength: 1
          title: 位置名称
          maxLength: 100
        code:
          type: string
          minLength: 1
          title: 位置代码
          maxLength: 20
        description:
          type: string
          nullable: true
          title: 描述
        parent:
          type: string
          format: uuid
          nullable: true
          title: 父位置
        location_type:
          enum:
          - building
          - floor
          - department
          - room
          - area
          type: string
          description: |-
            * `building` - 建筑
            * `floor` - 楼层
            * `department` - 科室
            * `room` - 房间
            * `area` - 区域
          x-spec-enum-id: 04ce14d026949d40
          title: 位置类型
        manager:
          type: string
          format: uuid
          nullable: true
          title: 负责人
        is_active:
          type: boolean
          title: 是否激活
    PatchedDeviceManufacturerRequest:
      type: object
      description: 设备制造商序列化器
      properties:
        name:
          type: string
          minLength: 1
          title: 制造商名称
          maxLength: 200
        code:
          type: string
          minLength: 1
          title: 制造商代码
          maxLength: 50
        description:
          type: string
          nullable: true
          title: 描述
        contact_person:
          type: string
          nullable: true
          title: 联系人
          maxLength: 100
        phone:
          type: string
          nullable: true
          title: 电话
          maxLength: 20
        email:
          type: string
          format: email
          nullable: true
          title: 邮箱
          maxLength: 254
        address:
          type: string
          nullable: true
          title: 地址
        website:
          type: string
          format: uri
          nullable: true
          title: 网站
          maxLength: 200
        is_active:
          type: boolean
          title: 是否激活
    PatchedDeviceModelRequest:
      type: object
      description: 设备型号序列化器
      properties:
        name:
          type: string
          minLength: 1
          title: 型号名称
          maxLength: 200
        model_number:
          type: string
          minLength: 1
          title: 型号编号
          maxLength: 100
        manufacturer:
          type: string
          format: uuid
          title: 制造商
        category:
          type: string
          format: uuid
          title: 设备分类
        specifications:
          title: 技术规格
        description:
          type: string
          nullable: true
          title: 描述
        image:
          type: string
          format: binary
          nullable: true
          title: 产品图片
        manual_url:
          type: string
          format: uri
          nullable: true
          title: 说明书链接
          maxLength: 200
        purchase_price:
          type: string
          format: decimal
          pattern: ^-?\d{0,10}(?:\.\d{0,2})?$
          nullable: true
          title: 采购价格
        is_active:
          type: boolean
          title: 是否激活
    PatchedDeviceRequest:
      type: object
      description: 设备序列化器
      properties:
        asset_number:
          type: string
          minLength: 1
          title: 资产编号
          maxLength: 50
        serial_number:
          type: string
          nullable: true
          title: 序列号
          maxLength: 100
        name:
          type: string
          minLength: 1
          title: 设备名称
          maxLength: 200
        device_model:
          type: string
          format: uuid
          title: 设备型号
        department:
          type: string
          nullable: true
          title: 所属科室
          maxLength: 100
        location:
          type: string
          nullable: true
          title: 具体位置
          maxLength: 200
        room:
          type: string
          nullable: true
          title: 房间号
          maxLength: 50
        responsible_person:
          type: string
          format: uuid
          nullable: true
          title: 责任人
        operator:
          type: string
          format: uuid
          nullable: true
          title: 操作员
        purchase_date:
          type: string
          format: date
          nullable: true
          title: 采购日期
        purchase_price:
          type: string
          format: decimal
          pattern: ^-?\d{0,10}(?:\.\d{0,2})?$
          nullable: true
          title: 采购价格
        supplier:
          type: string
          nullable: true
          title: 供应商
          maxLength: 200
        warranty_start_date:
          type: string
          format: date
          nullable: true
          title: 保修开始日期
        warranty_end_date:
          type: string
          format: date
          nullable: true
          title: 保修结束日期
        status:
          enum:
          - normal
          - maintenance
          - repair
          - fault
          - retired
          - idle
          type: string
          description: |-
            * `normal` - 正常
            * `maintenance` - 维护中
            * `repair` - 维修中
            * `fault` - 故障
            * `retired` - 报废
            * `idle` - 闲置
          x-spec-enum-id: 69dbaf18a0c31a0a
          title: 设备状态
        usage_status:
          enum:
          - in_use
          - available
          - unavailable
          - reserved
          type: string
          description: |-
            * `in_use` - 使用中
            * `available` - 可用
            * `unavailable` - 不可用
            * `reserved` - 预约中
          x-spec-enum-id: b23b06164f8cd341
          title: 使用状态
        importance:
          enum:
          - critical
          - important
          - normal
          - low
          type: string
          description: |-
            * `critical` - 关键
            * `important` - 重要
            * `normal` - 一般
            * `low` - 较低
          x-spec-enum-id: c9ebac9f4bc32fe0
          title: 重要程度
        parameters:
          title: 设备参数
        notes:
          type: string
          nullable: true
          title: 备注
        last_check_date:
          type: string
          format: date-time
          nullable: true
          title: 最后检查时间
        last_check_result:
          type: string
          nullable: true
          title: 最后检查结果
        next_check_date:
          type: string
          format: date
          nullable: true
          title: 下次检查日期
        is_active:
          type: boolean
          title: 是否激活
    PatchedFaultReportRequest:
      type: object
      description: 故障报告序列化器
      properties:
        device:
          type: string
          format: uuid
          title: 设备
        title:
          type: string
          minLength: 1
          title: 故障标题
          maxLength: 200
        description:
          type: string
          minLength: 1
          title: 故障描述
        fault_code:
          type: string
          nullable: true
          title: 故障代码
          maxLength: 50
        severity:
          enum:
          - low
          - medium
          - high
          - critical
          type: string
          description: |-
            * `low` - 轻微
            * `medium` - 中等
            * `high` - 严重
            * `critical` - 致命
          x-spec-enum-id: 9c552eeb562b30ce
          title: 严重程度
        fault_type:
          enum:
          - mechanical
          - electrical
          - software
          - calibration
          - wear
          - other
          type: string
          description: |-
            * `mechanical` - 机械故障
            * `electrical` - 电气故障
            * `software` - 软件故障
            * `calibration` - 校准问题
            * `wear` - 磨损
            * `other` - 其他
          x-spec-enum-id: 03b6f600188dbbbc
          title: 故障类型
        occurred_at:
          type: string
          format: date-time
          title: 发生时间
        status:
          enum:
          - open
          - assigned
          - in_progress
          - resolved
          - closed
          type: string
          description: |-
            * `open` - 待处理
            * `assigned` - 已分配
            * `in_progress` - 处理中
            * `resolved` - 已解决
            * `closed` - 已关闭
          x-spec-enum-id: a04ace4625a8999f
          title: 处理状态
        assigned_to:
          type: string
          format: uuid
          nullable: true
          title: 分配给
        resolution:
          type: string
          nullable: true
          title: 解决方案
        resolved_at:
          type: string
          format: date-time
          nullable: true
          title: 解决时间
        resolved_by:
          type: string
          format: uuid
          nullable: true
          title: 解决人
        maintenance_record:
          type: string
          format: uuid
          nullable: true
          title: 关联维护记录
    PatchedIconRequest:
      type: object
      description: 图标序列化器
      properties:
        icon_set:
          type: string
          format: uuid
          title: 图标集
        name:
          type: string
          minLength: 1
          title: 图标名称
          maxLength: 100
        display_name:
          type: string
          minLength: 1
          title: 显示名称
          maxLength: 100
        description:
          type: string
          nullable: true
          title: 描述
        icon_class:
          type: string
          nullable: true
          title: 图标类名
          maxLength: 100
        svg_content:
          type: string
          nullable: true
          title: SVG内容
        image_url:
          type: string
          format: uri
          nullable: true
          title: 图片URL
          maxLength: 200
        category:
          type: string
          nullable: true
          title: 分类
          maxLength: 50
        tags:
          title: 标签
        default_size:
          type: string
          minLength: 1
          title: 默认大小
          maxLength: 20
        default_color:
          type: string
          minLength: 1
          title: 默认颜色
          maxLength: 7
        is_active:
          type: boolean
          title: 是否激活
    PatchedIconSetRequest:
      type: object
      description: 图标集序列化器
      properties:
        name:
          type: string
          minLength: 1
          title: 图标集名称
          maxLength: 50
        display_name:
          type: string
          minLength: 1
          title: 显示名称
          maxLength: 100
        description:
          type: string
          nullable: true
          title: 描述
        version:
          type: string
          minLength: 1
          title: 版本
          maxLength: 20
        base_url:
          type: string
          format: uri
          nullable: true
          title: 基础URL
          maxLength: 200
        css_url:
          type: string
          format: uri
          nullable: true
          title: CSS文件URL
          maxLength: 200
        js_url:
          type: string
          format: uri
          nullable: true
          title: JS文件URL
          maxLength: 200
        icon_type:
          enum:
          - font
          - svg
          - image
          type: string
          description: |-
            * `font` - 字体图标
            * `svg` - SVG图标
            * `image` - 图片图标
          x-spec-enum-id: 6763b15f00eb4732
          title: 图标类型
        prefix:
          type: string
          minLength: 1
          title: 图标前缀
          maxLength: 20
        is_active:
          type: boolean
          title: 是否激活
        is_default:
          type: boolean
          title: 是否默认图标集
    PatchedItemCategoryRequest:
      type: object
      description: 物品分类序列化器
      properties:
        name:
          type: string
          minLength: 1
          title: 分类名称
          maxLength: 100
        code:
          type: string
          minLength: 1
          title: 分类代码
          maxLength: 20
        description:
          type: string
          nullable: true
          title: 分类描述
        parent:
          type: string
          format: uuid
          nullable: true
          title: 父分类
        icon:
          type: string
          nullable: true
          title: 图标
          maxLength: 50
        color:
          type: string
          minLength: 1
          title: 颜色
          maxLength: 7
        sort_order:
          type: integer
          maximum: 922337*************
          minimum: 0
          format: int64
          title: 排序
        is_active:
          type: boolean
          title: 是否激活
    PatchedItemRequest:
      type: object
      description: 物品序列化器
      properties:
        name:
          type: string
          minLength: 1
          title: 物品名称
          maxLength: 200
        code:
          type: string
          minLength: 1
          title: 物品编码
          maxLength: 50
        description:
          type: string
          nullable: true
          title: 描述
        category:
          type: string
          format: uuid
          title: 分类
        specifications:
          title: 规格参数
        unit:
          type: string
          minLength: 1
          title: 单位
          maxLength: 20
        brand:
          type: string
          nullable: true
          title: 品牌
          maxLength: 100
        model:
          type: string
          nullable: true
          title: 型号
          maxLength: 100
        standard_price:
          type: string
          format: decimal
          pattern: ^-?\d{0,10}(?:\.\d{0,2})?$
          nullable: true
          title: 标准价格
        min_stock:
          type: integer
          maximum: 922337*************
          minimum: 0
          format: int64
          title: 最小库存
        max_stock:
          type: integer
          maximum: 922337*************
          minimum: 0
          format: int64
          title: 最大库存
        reorder_point:
          type: integer
          maximum: 922337*************
          minimum: 0
          format: int64
          title: 补货点
        item_type:
          enum:
          - consumable
          - spare_part
          - tool
          - material
          - other
          type: string
          description: |-
            * `consumable` - 消耗品
            * `spare_part` - 备件
            * `tool` - 工具
            * `material` - 材料
            * `other` - 其他
          x-spec-enum-id: f23810a86fc098ba
          title: 物品类型
        image:
          type: string
          format: binary
          nullable: true
          title: 物品图片
        is_active:
          type: boolean
          title: 是否激活
    PatchedMaintenancePlanRequest:
      type: object
      description: 维护计划序列化器
      properties:
        device:
          type: string
          format: uuid
          title: 设备
        maintenance_type:
          type: string
          format: uuid
          title: 维护类型
        title:
          type: string
          minLength: 1
          title: 计划标题
          maxLength: 200
        description:
          type: string
          nullable: true
          title: 计划描述
        start_date:
          type: string
          format: date
          title: 开始日期
        interval_days:
          type: integer
          maximum: 922337*************
          minimum: 0
          format: int64
          title: 间隔天数
        estimated_duration:
          type: integer
          maximum: 922337*************
          minimum: 0
          format: int64
          title: 预计时长(分钟)
        next_maintenance_date:
          type: string
          format: date
          title: 下次维护日期
        assigned_to:
          type: string
          format: uuid
          nullable: true
          title: 负责人
        priority:
          enum:
          - low
          - normal
          - high
          - urgent
          type: string
          description: |-
            * `low` - 低
            * `normal` - 普通
            * `high` - 高
            * `urgent` - 紧急
          x-spec-enum-id: b4c18fd9205140ec
          title: 优先级
        is_active:
          type: boolean
          title: 是否激活
    PatchedMaintenanceRecordRequest:
      type: object
      description: 维护记录序列化器
      properties:
        device:
          type: string
          format: uuid
          title: 设备
        maintenance_plan:
          type: string
          format: uuid
          nullable: true
          title: 维护计划
        maintenance_type:
          type: string
          format: uuid
          title: 维护类型
        title:
          type: string
          minLength: 1
          title: 维护标题
          maxLength: 200
        description:
          type: string
          minLength: 1
          title: 维护描述
        scheduled_date:
          type: string
          format: date-time
          title: 计划时间
        start_time:
          type: string
          format: date-time
          nullable: true
          title: 开始时间
        end_time:
          type: string
          format: date-time
          nullable: true
          title: 结束时间
        performed_by:
          type: string
          format: uuid
          nullable: true
          title: 执行人
        status:
          enum:
          - scheduled
          - in_progress
          - completed
          - cancelled
          - failed
          type: string
          description: |-
            * `scheduled` - 已计划
            * `in_progress` - 进行中
            * `completed` - 已完成
            * `cancelled` - 已取消
            * `failed` - 失败
          x-spec-enum-id: 9ebcb723b8f8041e
          title: 状态
        result:
          type: string
          nullable: true
          title: 维护结果
        issues_found:
          type: string
          nullable: true
          title: 发现问题
        parts_replaced:
          title: 更换部件
        cost:
          type: string
          format: decimal
          pattern: ^-?\d{0,8}(?:\.\d{0,2})?$
          nullable: true
          title: 维护成本
        rating:
          enum:
          - 1
          - 2
          - 3
          - 4
          - 5
          - null
          type: integer
          description: |-
            * `1` - 很差
            * `2` - 差
            * `3` - 一般
            * `4` - 好
            * `5` - 很好
          x-spec-enum-id: affa594dfcb08b0b
          nullable: true
          title: 维护评级
          minimum: 0
          maximum: 922337*************
    PatchedMaintenanceTypeRequest:
      type: object
      description: 维护类型序列化器
      properties:
        name:
          type: string
          minLength: 1
          title: 维护类型名称
          maxLength: 100
        code:
          type: string
          minLength: 1
          title: 类型代码
          maxLength: 20
        description:
          type: string
          nullable: true
          title: 描述
        default_duration:
          type: integer
          maximum: 922337*************
          minimum: 0
          format: int64
          title: 默认时长(分钟)
        default_interval_days:
          type: integer
          maximum: 922337*************
          minimum: 0
          format: int64
          title: 默认间隔(天)
        is_active:
          type: boolean
          title: 是否激活
    PatchedPurchaseOrderRequest:
      type: object
      description: 采购订单序列化器
      properties:
        order_number:
          type: string
          minLength: 1
          title: 订单号
          maxLength: 50
        supplier:
          type: string
          format: uuid
          title: 供应商
        order_date:
          type: string
          format: date
          title: 订单日期
        expected_delivery_date:
          type: string
          format: date
          nullable: true
          title: 预计交货日期
        actual_delivery_date:
          type: string
          format: date
          nullable: true
          title: 实际交货日期
        status:
          enum:
          - draft
          - submitted
          - confirmed
          - partial_received
          - received
          - cancelled
          type: string
          description: |-
            * `draft` - 草稿
            * `submitted` - 已提交
            * `confirmed` - 已确认
            * `partial_received` - 部分收货
            * `received` - 已收货
            * `cancelled` - 已取消
          x-spec-enum-id: f54fcf7973bbcd80
          title: 状态
        total_amount:
          type: string
          format: decimal
          pattern: ^-?\d{0,10}(?:\.\d{0,2})?$
          title: 总金额
        notes:
          type: string
          nullable: true
          title: 备注
        approved_by:
          type: string
          format: uuid
          nullable: true
          title: 审批者
    PatchedReportInstanceRequest:
      type: object
      description: 报表实例序列化器
      properties:
        template:
          type: string
          format: uuid
          title: 报表模板
        title:
          type: string
          minLength: 1
          title: 报表标题
          maxLength: 200
        description:
          type: string
          nullable: true
          title: 报表描述
        parameters:
          title: 生成参数
    PatchedReportTemplateRequest:
      type: object
      description: 报表模板序列化器
      properties:
        name:
          type: string
          minLength: 1
          title: 模板名称
          maxLength: 200
        description:
          type: string
          nullable: true
          title: 模板描述
        report_type:
          enum:
          - device_usage
          - maintenance_summary
          - inventory_status
          - cost_analysis
          - fault_analysis
          - custom
          type: string
          description: |-
            * `device_usage` - 设备使用报表
            * `maintenance_summary` - 维护汇总报表
            * `inventory_status` - 库存状态报表
            * `cost_analysis` - 成本分析报表
            * `fault_analysis` - 故障分析报表
            * `custom` - 自定义报表
          x-spec-enum-id: d4ca4b6ac8a85604
          title: 报表类型
        config:
          title: 报表配置
        filters:
          title: 过滤条件
        fields:
          title: 字段配置
        ordering:
          title: 排序配置
        is_active:
          type: boolean
          title: 是否激活
        is_public:
          type: boolean
          title: 是否公开
    PatchedStockMovementRequest:
      type: object
      description: 库存变动序列化器
      properties:
        item:
          type: string
          format: uuid
          title: 物品
        movement_type:
          enum:
          - in
          - out
          - transfer
          - adjustment
          type: string
          description: |-
            * `in` - 入库
            * `out` - 出库
            * `transfer` - 调拨
            * `adjustment` - 调整
          x-spec-enum-id: 97efb4d992f07193
          title: 变动类型
        quantity:
          type: integer
          maximum: 922337*************
          minimum: -9223372036854775808
          format: int64
          title: 数量
        unit_price:
          type: string
          format: decimal
          pattern: ^-?\d{0,10}(?:\.\d{0,2})?$
          nullable: true
          title: 单价
        reference_number:
          type: string
          nullable: true
          title: 参考单号
          maxLength: 100
        supplier:
          type: string
          format: uuid
          nullable: true
          title: 供应商
        notes:
          type: string
          nullable: true
          title: 备注
    PatchedSupplierRequest:
      type: object
      description: 供应商序列化器
      properties:
        name:
          type: string
          minLength: 1
          title: 供应商名称
          maxLength: 200
        code:
          type: string
          minLength: 1
          title: 供应商代码
          maxLength: 50
        description:
          type: string
          nullable: true
          title: 描述
        contact_person:
          type: string
          nullable: true
          title: 联系人
          maxLength: 100
        phone:
          type: string
          nullable: true
          title: 电话
          maxLength: 20
        email:
          type: string
          format: email
          nullable: true
          title: 邮箱
          maxLength: 254
        address:
          type: string
          nullable: true
          title: 地址
        website:
          type: string
          format: uri
          nullable: true
          title: 网站
          maxLength: 200
        rating:
          enum:
          - 1
          - 2
          - 3
          - 4
          - 5
          - null
          type: integer
          description: |-
            * `1` - 很差
            * `2` - 差
            * `3` - 一般
            * `4` - 好
            * `5` - 很好
          x-spec-enum-id: affa594dfcb08b0b
          nullable: true
          title: 供应商评级
          minimum: 0
          maximum: 922337*************
        is_active:
          type: boolean
          title: 是否激活
    PatchedThemeRequest:
      type: object
      description: 主题序列化器
      properties:
        name:
          type: string
          minLength: 1
          title: 主题名称
          maxLength: 50
        display_name:
          type: string
          minLength: 1
          title: 显示名称
          maxLength: 100
        description:
          type: string
          nullable: true
          title: 主题描述
        theme_type:
          enum:
          - light
          - dark
          - medical
          - custom
          type: string
          description: |-
            * `light` - 浅色主题
            * `dark` - 深色主题
            * `medical` - 医疗主题
            * `custom` - 自定义主题
          x-spec-enum-id: fc1213086dbd37e1
          title: 主题类型
        primary_color:
          type: string
          minLength: 1
          title: 主色调
          maxLength: 7
        secondary_color:
          type: string
          minLength: 1
          title: 辅助色
          maxLength: 7
        success_color:
          type: string
          minLength: 1
          title: 成功色
          maxLength: 7
        warning_color:
          type: string
          minLength: 1
          title: 警告色
          maxLength: 7
        danger_color:
          type: string
          minLength: 1
          title: 危险色
          maxLength: 7
        info_color:
          type: string
          minLength: 1
          title: 信息色
          maxLength: 7
        background_color:
          type: string
          minLength: 1
          title: 背景色
          maxLength: 7
        text_color:
          type: string
          minLength: 1
          title: 文字色
          maxLength: 7
        border_color:
          type: string
          minLength: 1
          title: 边框色
          maxLength: 7
        navbar_bg_color:
          type: string
          minLength: 1
          title: 导航栏背景色
          maxLength: 7
        navbar_text_color:
          type: string
          minLength: 1
          title: 导航栏文字色
          maxLength: 7
        sidebar_bg_color:
          type: string
          minLength: 1
          title: 侧边栏背景色
          maxLength: 7
        sidebar_text_color:
          type: string
          minLength: 1
          title: 侧边栏文字色
          maxLength: 7
        custom_css:
          type: string
          nullable: true
          title: 自定义CSS
        is_active:
          type: boolean
          title: 是否激活
        is_default:
          type: boolean
          title: 是否默认主题
    PurchaseOrder:
      type: object
      description: 采购订单序列化器
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        order_number:
          type: string
          title: 订单号
          maxLength: 50
        supplier:
          type: string
          format: uuid
          title: 供应商
        supplier_name:
          type: string
          readOnly: true
        order_date:
          type: string
          format: date
          title: 订单日期
        expected_delivery_date:
          type: string
          format: date
          nullable: true
          title: 预计交货日期
        actual_delivery_date:
          type: string
          format: date
          nullable: true
          title: 实际交货日期
        status:
          enum:
          - draft
          - submitted
          - confirmed
          - partial_received
          - received
          - cancelled
          type: string
          description: |-
            * `draft` - 草稿
            * `submitted` - 已提交
            * `confirmed` - 已确认
            * `partial_received` - 部分收货
            * `received` - 已收货
            * `cancelled` - 已取消
          x-spec-enum-id: f54fcf7973bbcd80
          title: 状态
        total_amount:
          type: string
          format: decimal
          pattern: ^-?\d{0,10}(?:\.\d{0,2})?$
          title: 总金额
        notes:
          type: string
          nullable: true
          title: 备注
        created_by:
          type: string
          format: uuid
          readOnly: true
          nullable: true
          title: 创建者
        created_by_name:
          type: string
          readOnly: true
        approved_by:
          type: string
          format: uuid
          nullable: true
          title: 审批者
        approved_by_name:
          type: string
          readOnly: true
        items:
          type: array
          items:
            $ref: '#/components/schemas/PurchaseOrderItem'
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 创建时间
        updated_at:
          type: string
          format: date-time
          readOnly: true
          title: 更新时间
      required:
      - approved_by_name
      - created_at
      - created_by
      - created_by_name
      - id
      - items
      - order_date
      - order_number
      - supplier
      - supplier_name
      - updated_at
    PurchaseOrderItem:
      type: object
      description: 采购订单明细序列化器
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        item:
          type: string
          format: uuid
          title: 物品
        item_name:
          type: string
          readOnly: true
        item_code:
          type: string
          readOnly: true
        quantity:
          type: integer
          maximum: 922337*************
          minimum: 0
          format: int64
          title: 数量
        unit_price:
          type: string
          format: decimal
          pattern: ^-?\d{0,10}(?:\.\d{0,2})?$
          title: 单价
        total_price:
          type: string
          format: decimal
          pattern: ^-?\d{0,10}(?:\.\d{0,2})?$
          readOnly: true
          title: 总价
        received_quantity:
          type: integer
          maximum: 922337*************
          minimum: 0
          format: int64
          title: 已收货数量
        remaining_quantity:
          type: string
          readOnly: true
        is_fully_received:
          type: string
          readOnly: true
        notes:
          type: string
          nullable: true
          title: 备注
      required:
      - id
      - is_fully_received
      - item
      - item_code
      - item_name
      - quantity
      - remaining_quantity
      - total_price
      - unit_price
    PurchaseOrderItemRequest:
      type: object
      description: 采购订单明细序列化器
      properties:
        item:
          type: string
          format: uuid
          title: 物品
        quantity:
          type: integer
          maximum: 922337*************
          minimum: 0
          format: int64
          title: 数量
        unit_price:
          type: string
          format: decimal
          pattern: ^-?\d{0,10}(?:\.\d{0,2})?$
          title: 单价
        received_quantity:
          type: integer
          maximum: 922337*************
          minimum: 0
          format: int64
          title: 已收货数量
        notes:
          type: string
          nullable: true
          title: 备注
      required:
      - item
      - quantity
      - unit_price
    PurchaseOrderList:
      type: object
      description: 采购订单列表序列化器
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        order_number:
          type: string
          title: 订单号
          maxLength: 50
        supplier_name:
          type: string
          readOnly: true
        order_date:
          type: string
          format: date
          title: 订单日期
        expected_delivery_date:
          type: string
          format: date
          nullable: true
          title: 预计交货日期
        status:
          enum:
          - draft
          - submitted
          - confirmed
          - partial_received
          - received
          - cancelled
          type: string
          description: |-
            * `draft` - 草稿
            * `submitted` - 已提交
            * `confirmed` - 已确认
            * `partial_received` - 部分收货
            * `received` - 已收货
            * `cancelled` - 已取消
          x-spec-enum-id: f54fcf7973bbcd80
          title: 状态
        total_amount:
          type: string
          format: decimal
          pattern: ^-?\d{0,10}(?:\.\d{0,2})?$
          title: 总金额
        item_count:
          type: string
          readOnly: true
        created_by_name:
          type: string
          readOnly: true
      required:
      - created_by_name
      - id
      - item_count
      - order_date
      - order_number
      - supplier_name
    PurchaseOrderRequest:
      type: object
      description: 采购订单序列化器
      properties:
        order_number:
          type: string
          minLength: 1
          title: 订单号
          maxLength: 50
        supplier:
          type: string
          format: uuid
          title: 供应商
        order_date:
          type: string
          format: date
          title: 订单日期
        expected_delivery_date:
          type: string
          format: date
          nullable: true
          title: 预计交货日期
        actual_delivery_date:
          type: string
          format: date
          nullable: true
          title: 实际交货日期
        status:
          enum:
          - draft
          - submitted
          - confirmed
          - partial_received
          - received
          - cancelled
          type: string
          description: |-
            * `draft` - 草稿
            * `submitted` - 已提交
            * `confirmed` - 已确认
            * `partial_received` - 部分收货
            * `received` - 已收货
            * `cancelled` - 已取消
          x-spec-enum-id: f54fcf7973bbcd80
          title: 状态
        total_amount:
          type: string
          format: decimal
          pattern: ^-?\d{0,10}(?:\.\d{0,2})?$
          title: 总金额
        notes:
          type: string
          nullable: true
          title: 备注
        approved_by:
          type: string
          format: uuid
          nullable: true
          title: 审批者
      required:
      - order_date
      - order_number
      - supplier
    ReportInstance:
      type: object
      description: 报表实例序列化器
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        template:
          type: string
          format: uuid
          title: 报表模板
        template_name:
          type: string
          readOnly: true
        template_type:
          type: string
          readOnly: true
        title:
          type: string
          title: 报表标题
          maxLength: 200
        description:
          type: string
          nullable: true
          title: 报表描述
        parameters:
          title: 生成参数
        data:
          readOnly: true
          title: 报表数据
        total_records:
          type: integer
          readOnly: true
          title: 总记录数
        status:
          enum:
          - generating
          - completed
          - failed
          type: string
          description: |-
            * `generating` - 生成中
            * `completed` - 已完成
            * `failed` - 生成失败
          x-spec-enum-id: 4645a031e69e634a
          readOnly: true
          title: 状态
        error_message:
          type: string
          readOnly: true
          nullable: true
          title: 错误信息
        file_path:
          type: string
          readOnly: true
          nullable: true
          title: 文件路径
        file_size:
          type: integer
          readOnly: true
          nullable: true
          title: 文件大小
        generated_by:
          type: string
          format: uuid
          readOnly: true
          title: 生成者
        generated_by_name:
          type: string
          readOnly: true
        generated_at:
          type: string
          format: date-time
          readOnly: true
          title: 生成时间
        completed_at:
          type: string
          format: date-time
          readOnly: true
          nullable: true
          title: 完成时间
        duration:
          type: string
          readOnly: true
      required:
      - completed_at
      - data
      - duration
      - error_message
      - file_path
      - file_size
      - generated_at
      - generated_by
      - generated_by_name
      - id
      - status
      - template
      - template_name
      - template_type
      - title
      - total_records
    ReportInstanceList:
      type: object
      description: 报表实例列表序列化器
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        template_name:
          type: string
          readOnly: true
        template_type:
          type: string
          readOnly: true
        title:
          type: string
          title: 报表标题
          maxLength: 200
        status:
          enum:
          - generating
          - completed
          - failed
          type: string
          description: |-
            * `generating` - 生成中
            * `completed` - 已完成
            * `failed` - 生成失败
          x-spec-enum-id: 4645a031e69e634a
          title: 状态
        total_records:
          type: integer
          maximum: 922337*************
          minimum: 0
          format: int64
          title: 总记录数
        generated_by_name:
          type: string
          readOnly: true
        generated_at:
          type: string
          format: date-time
          readOnly: true
          title: 生成时间
        completed_at:
          type: string
          format: date-time
          nullable: true
          title: 完成时间
      required:
      - generated_at
      - generated_by_name
      - id
      - template_name
      - template_type
      - title
    ReportInstanceRequest:
      type: object
      description: 报表实例序列化器
      properties:
        template:
          type: string
          format: uuid
          title: 报表模板
        title:
          type: string
          minLength: 1
          title: 报表标题
          maxLength: 200
        description:
          type: string
          nullable: true
          title: 报表描述
        parameters:
          title: 生成参数
      required:
      - template
      - title
    ReportTemplate:
      type: object
      description: 报表模板序列化器
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        name:
          type: string
          title: 模板名称
          maxLength: 200
        description:
          type: string
          nullable: true
          title: 模板描述
        report_type:
          enum:
          - device_usage
          - maintenance_summary
          - inventory_status
          - cost_analysis
          - fault_analysis
          - custom
          type: string
          description: |-
            * `device_usage` - 设备使用报表
            * `maintenance_summary` - 维护汇总报表
            * `inventory_status` - 库存状态报表
            * `cost_analysis` - 成本分析报表
            * `fault_analysis` - 故障分析报表
            * `custom` - 自定义报表
          x-spec-enum-id: d4ca4b6ac8a85604
          title: 报表类型
        config:
          title: 报表配置
        filters:
          title: 过滤条件
        fields:
          title: 字段配置
        ordering:
          title: 排序配置
        is_active:
          type: boolean
          title: 是否激活
        is_public:
          type: boolean
          title: 是否公开
        created_by:
          type: string
          format: uuid
          readOnly: true
          title: 创建者
        created_by_name:
          type: string
          readOnly: true
        instance_count:
          type: string
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 创建时间
        updated_at:
          type: string
          format: date-time
          readOnly: true
          title: 更新时间
      required:
      - created_at
      - created_by
      - created_by_name
      - id
      - instance_count
      - name
      - report_type
      - updated_at
    ReportTemplateList:
      type: object
      description: 报表模板列表序列化器
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        name:
          type: string
          title: 模板名称
          maxLength: 200
        description:
          type: string
          nullable: true
          title: 模板描述
        report_type:
          enum:
          - device_usage
          - maintenance_summary
          - inventory_status
          - cost_analysis
          - fault_analysis
          - custom
          type: string
          description: |-
            * `device_usage` - 设备使用报表
            * `maintenance_summary` - 维护汇总报表
            * `inventory_status` - 库存状态报表
            * `cost_analysis` - 成本分析报表
            * `fault_analysis` - 故障分析报表
            * `custom` - 自定义报表
          x-spec-enum-id: d4ca4b6ac8a85604
          title: 报表类型
        is_active:
          type: boolean
          title: 是否激活
        is_public:
          type: boolean
          title: 是否公开
        created_by_name:
          type: string
          readOnly: true
        instance_count:
          type: string
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 创建时间
      required:
      - created_at
      - created_by_name
      - id
      - instance_count
      - name
      - report_type
    ReportTemplateRequest:
      type: object
      description: 报表模板序列化器
      properties:
        name:
          type: string
          minLength: 1
          title: 模板名称
          maxLength: 200
        description:
          type: string
          nullable: true
          title: 模板描述
        report_type:
          enum:
          - device_usage
          - maintenance_summary
          - inventory_status
          - cost_analysis
          - fault_analysis
          - custom
          type: string
          description: |-
            * `device_usage` - 设备使用报表
            * `maintenance_summary` - 维护汇总报表
            * `inventory_status` - 库存状态报表
            * `cost_analysis` - 成本分析报表
            * `fault_analysis` - 故障分析报表
            * `custom` - 自定义报表
          x-spec-enum-id: d4ca4b6ac8a85604
          title: 报表类型
        config:
          title: 报表配置
        filters:
          title: 过滤条件
        fields:
          title: 字段配置
        ordering:
          title: 排序配置
        is_active:
          type: boolean
          title: 是否激活
        is_public:
          type: boolean
          title: 是否公开
      required:
      - name
      - report_type
    StockMovement:
      type: object
      description: 库存变动序列化器
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        item:
          type: string
          format: uuid
          title: 物品
        item_name:
          type: string
          readOnly: true
        item_code:
          type: string
          readOnly: true
        movement_type:
          enum:
          - in
          - out
          - transfer
          - adjustment
          type: string
          description: |-
            * `in` - 入库
            * `out` - 出库
            * `transfer` - 调拨
            * `adjustment` - 调整
          x-spec-enum-id: 97efb4d992f07193
          title: 变动类型
        quantity:
          type: integer
          maximum: 922337*************
          minimum: -9223372036854775808
          format: int64
          title: 数量
        unit_price:
          type: string
          format: decimal
          pattern: ^-?\d{0,10}(?:\.\d{0,2})?$
          nullable: true
          title: 单价
        total_amount:
          type: string
          format: decimal
          pattern: ^-?\d{0,10}(?:\.\d{0,2})?$
          readOnly: true
          nullable: true
          title: 总金额
        reference_number:
          type: string
          nullable: true
          title: 参考单号
          maxLength: 100
        supplier:
          type: string
          format: uuid
          nullable: true
          title: 供应商
        supplier_name:
          type: string
          readOnly: true
        notes:
          type: string
          nullable: true
          title: 备注
        created_by:
          type: string
          format: uuid
          readOnly: true
          nullable: true
          title: 操作人
        created_by_name:
          type: string
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 创建时间
      required:
      - created_at
      - created_by
      - created_by_name
      - id
      - item
      - item_code
      - item_name
      - movement_type
      - quantity
      - supplier_name
      - total_amount
    StockMovementRequest:
      type: object
      description: 库存变动序列化器
      properties:
        item:
          type: string
          format: uuid
          title: 物品
        movement_type:
          enum:
          - in
          - out
          - transfer
          - adjustment
          type: string
          description: |-
            * `in` - 入库
            * `out` - 出库
            * `transfer` - 调拨
            * `adjustment` - 调整
          x-spec-enum-id: 97efb4d992f07193
          title: 变动类型
        quantity:
          type: integer
          maximum: 922337*************
          minimum: -9223372036854775808
          format: int64
          title: 数量
        unit_price:
          type: string
          format: decimal
          pattern: ^-?\d{0,10}(?:\.\d{0,2})?$
          nullable: true
          title: 单价
        reference_number:
          type: string
          nullable: true
          title: 参考单号
          maxLength: 100
        supplier:
          type: string
          format: uuid
          nullable: true
          title: 供应商
        notes:
          type: string
          nullable: true
          title: 备注
      required:
      - item
      - movement_type
      - quantity
    Supplier:
      type: object
      description: 供应商序列化器
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        name:
          type: string
          title: 供应商名称
          maxLength: 200
        code:
          type: string
          title: 供应商代码
          maxLength: 50
        description:
          type: string
          nullable: true
          title: 描述
        contact_person:
          type: string
          nullable: true
          title: 联系人
          maxLength: 100
        phone:
          type: string
          nullable: true
          title: 电话
          maxLength: 20
        email:
          type: string
          format: email
          nullable: true
          title: 邮箱
          maxLength: 254
        address:
          type: string
          nullable: true
          title: 地址
        website:
          type: string
          format: uri
          nullable: true
          title: 网站
          maxLength: 200
        rating:
          enum:
          - 1
          - 2
          - 3
          - 4
          - 5
          - null
          type: integer
          description: |-
            * `1` - 很差
            * `2` - 差
            * `3` - 一般
            * `4` - 好
            * `5` - 很好
          x-spec-enum-id: affa594dfcb08b0b
          nullable: true
          title: 供应商评级
          minimum: 0
          maximum: 922337*************
        is_active:
          type: boolean
          title: 是否激活
        item_count:
          type: string
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 创建时间
        updated_at:
          type: string
          format: date-time
          readOnly: true
          title: 更新时间
      required:
      - code
      - created_at
      - id
      - item_count
      - name
      - updated_at
    SupplierRequest:
      type: object
      description: 供应商序列化器
      properties:
        name:
          type: string
          minLength: 1
          title: 供应商名称
          maxLength: 200
        code:
          type: string
          minLength: 1
          title: 供应商代码
          maxLength: 50
        description:
          type: string
          nullable: true
          title: 描述
        contact_person:
          type: string
          nullable: true
          title: 联系人
          maxLength: 100
        phone:
          type: string
          nullable: true
          title: 电话
          maxLength: 20
        email:
          type: string
          format: email
          nullable: true
          title: 邮箱
          maxLength: 254
        address:
          type: string
          nullable: true
          title: 地址
        website:
          type: string
          format: uri
          nullable: true
          title: 网站
          maxLength: 200
        rating:
          enum:
          - 1
          - 2
          - 3
          - 4
          - 5
          - null
          type: integer
          description: |-
            * `1` - 很差
            * `2` - 差
            * `3` - 一般
            * `4` - 好
            * `5` - 很好
          x-spec-enum-id: affa594dfcb08b0b
          nullable: true
          title: 供应商评级
          minimum: 0
          maximum: 922337*************
        is_active:
          type: boolean
          title: 是否激活
      required:
      - code
      - name
    Theme:
      type: object
      description: 主题序列化器
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        name:
          type: string
          title: 主题名称
          maxLength: 50
        display_name:
          type: string
          title: 显示名称
          maxLength: 100
        description:
          type: string
          nullable: true
          title: 主题描述
        theme_type:
          enum:
          - light
          - dark
          - medical
          - custom
          type: string
          description: |-
            * `light` - 浅色主题
            * `dark` - 深色主题
            * `medical` - 医疗主题
            * `custom` - 自定义主题
          x-spec-enum-id: fc1213086dbd37e1
          title: 主题类型
        primary_color:
          type: string
          title: 主色调
          maxLength: 7
        secondary_color:
          type: string
          title: 辅助色
          maxLength: 7
        success_color:
          type: string
          title: 成功色
          maxLength: 7
        warning_color:
          type: string
          title: 警告色
          maxLength: 7
        danger_color:
          type: string
          title: 危险色
          maxLength: 7
        info_color:
          type: string
          title: 信息色
          maxLength: 7
        background_color:
          type: string
          title: 背景色
          maxLength: 7
        text_color:
          type: string
          title: 文字色
          maxLength: 7
        border_color:
          type: string
          title: 边框色
          maxLength: 7
        navbar_bg_color:
          type: string
          title: 导航栏背景色
          maxLength: 7
        navbar_text_color:
          type: string
          title: 导航栏文字色
          maxLength: 7
        sidebar_bg_color:
          type: string
          title: 侧边栏背景色
          maxLength: 7
        sidebar_text_color:
          type: string
          title: 侧边栏文字色
          maxLength: 7
        custom_css:
          type: string
          nullable: true
          title: 自定义CSS
        is_active:
          type: boolean
          title: 是否激活
        is_default:
          type: boolean
          title: 是否默认主题
        css_variables:
          type: string
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 创建时间
        updated_at:
          type: string
          format: date-time
          readOnly: true
          title: 更新时间
        created_by:
          type: string
          format: uuid
          readOnly: true
          nullable: true
          title: 创建者
        created_by_name:
          type: string
          readOnly: true
      required:
      - created_at
      - created_by
      - created_by_name
      - css_variables
      - display_name
      - id
      - name
      - updated_at
    ThemeList:
      type: object
      description: 主题列表序列化器
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        name:
          type: string
          title: 主题名称
          maxLength: 50
        display_name:
          type: string
          title: 显示名称
          maxLength: 100
        description:
          type: string
          nullable: true
          title: 主题描述
        theme_type:
          enum:
          - light
          - dark
          - medical
          - custom
          type: string
          description: |-
            * `light` - 浅色主题
            * `dark` - 深色主题
            * `medical` - 医疗主题
            * `custom` - 自定义主题
          x-spec-enum-id: fc1213086dbd37e1
          title: 主题类型
        primary_color:
          type: string
          title: 主色调
          maxLength: 7
        is_active:
          type: boolean
          title: 是否激活
        is_default:
          type: boolean
          title: 是否默认主题
      required:
      - display_name
      - id
      - name
    ThemeRequest:
      type: object
      description: 主题序列化器
      properties:
        name:
          type: string
          minLength: 1
          title: 主题名称
          maxLength: 50
        display_name:
          type: string
          minLength: 1
          title: 显示名称
          maxLength: 100
        description:
          type: string
          nullable: true
          title: 主题描述
        theme_type:
          enum:
          - light
          - dark
          - medical
          - custom
          type: string
          description: |-
            * `light` - 浅色主题
            * `dark` - 深色主题
            * `medical` - 医疗主题
            * `custom` - 自定义主题
          x-spec-enum-id: fc1213086dbd37e1
          title: 主题类型
        primary_color:
          type: string
          minLength: 1
          title: 主色调
          maxLength: 7
        secondary_color:
          type: string
          minLength: 1
          title: 辅助色
          maxLength: 7
        success_color:
          type: string
          minLength: 1
          title: 成功色
          maxLength: 7
        warning_color:
          type: string
          minLength: 1
          title: 警告色
          maxLength: 7
        danger_color:
          type: string
          minLength: 1
          title: 危险色
          maxLength: 7
        info_color:
          type: string
          minLength: 1
          title: 信息色
          maxLength: 7
        background_color:
          type: string
          minLength: 1
          title: 背景色
          maxLength: 7
        text_color:
          type: string
          minLength: 1
          title: 文字色
          maxLength: 7
        border_color:
          type: string
          minLength: 1
          title: 边框色
          maxLength: 7
        navbar_bg_color:
          type: string
          minLength: 1
          title: 导航栏背景色
          maxLength: 7
        navbar_text_color:
          type: string
          minLength: 1
          title: 导航栏文字色
          maxLength: 7
        sidebar_bg_color:
          type: string
          minLength: 1
          title: 侧边栏背景色
          maxLength: 7
        sidebar_text_color:
          type: string
          minLength: 1
          title: 侧边栏文字色
          maxLength: 7
        custom_css:
          type: string
          nullable: true
          title: 自定义CSS
        is_active:
          type: boolean
          title: 是否激活
        is_default:
          type: boolean
          title: 是否默认主题
      required:
      - display_name
      - name
  securitySchemes:
    cookieAuth:
      type: apiKey
      in: cookie
      name: sessionid
tags:
- name: authentication
  description: 认证相关接口
- name: themes
  description: 主题管理接口
- name: icons
  description: 图标库接口
- name: devices
  description: 设备管理接口
- name: maintenance
  description: 维护管理接口
- name: inventory
  description: 库存管理接口
- name: reports
  description: 报表分析接口
- name: monitoring
  description: 系统监控接口
