"""
图标模块序列化器
"""
from rest_framework import serializers
from .models import IconSet, Icon, IconUsage


class IconSetSerializer(serializers.ModelSerializer):
    """图标集序列化器"""
    icon_count = serializers.SerializerMethodField()
    
    class Meta:
        model = IconSet
        fields = [
            'id', 'name', 'display_name', 'description', 'version',
            'base_url', 'css_url', 'js_url', 'icon_type', 'prefix',
            'is_active', 'is_default', 'icon_count',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_icon_count(self, obj):
        """获取图标数量"""
        return Icon.objects.filter(icon_set=obj, is_active=True).count()


class IconSetListSerializer(serializers.ModelSerializer):
    """图标集列表序列化器"""
    icon_count = serializers.SerializerMethodField()
    
    class Meta:
        model = IconSet
        fields = [
            'id', 'name', 'display_name', 'description', 'icon_type',
            'is_active', 'is_default', 'icon_count'
        ]
    
    def get_icon_count(self, obj):
        """获取图标数量"""
        return Icon.objects.filter(icon_set=obj, is_active=True).count()


class IconSerializer(serializers.ModelSerializer):
    """图标序列化器"""
    icon_set_name = serializers.CharField(source='icon_set.name', read_only=True)
    full_class = serializers.SerializerMethodField()
    usage_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Icon
        fields = [
            'id', 'icon_set', 'icon_set_name', 'name', 'display_name',
            'description', 'icon_class', 'svg_content', 'image_url',
            'category', 'tags', 'default_size', 'default_color',
            'full_class', 'usage_count', 'is_active',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_full_class(self, obj):
        """获取完整的CSS类名"""
        return obj.get_full_class()
    
    def get_usage_count(self, obj):
        """获取使用次数"""
        usage = IconUsage.objects.filter(icon=obj).first()
        return usage.usage_count if usage else 0


class IconListSerializer(serializers.ModelSerializer):
    """图标列表序列化器"""
    icon_set_name = serializers.CharField(source='icon_set.name', read_only=True)
    full_class = serializers.SerializerMethodField()
    
    class Meta:
        model = Icon
        fields = [
            'id', 'name', 'display_name', 'icon_set_name',
            'icon_class', 'svg_content', 'image_url',
            'category', 'tags', 'full_class'
        ]
    
    def get_full_class(self, obj):
        """获取完整的CSS类名"""
        return obj.get_full_class()


class IconUsageSerializer(serializers.ModelSerializer):
    """图标使用记录序列化器"""
    icon_name = serializers.CharField(source='icon.name', read_only=True)
    icon_display_name = serializers.CharField(source='icon.display_name', read_only=True)
    icon_set_name = serializers.CharField(source='icon.icon_set.name', read_only=True)
    
    class Meta:
        model = IconUsage
        fields = [
            'id', 'icon', 'icon_name', 'icon_display_name', 'icon_set_name',
            'usage_count', 'last_used_at', 'context'
        ]
        read_only_fields = ['id', 'last_used_at']


class IconSearchSerializer(serializers.Serializer):
    """图标搜索序列化器"""
    query = serializers.CharField(max_length=100, required=False)
    icon_set = serializers.CharField(max_length=50, required=False)
    category = serializers.CharField(max_length=50, required=False)
    tags = serializers.ListField(
        child=serializers.CharField(max_length=50),
        required=False
    )
    icon_type = serializers.ChoiceField(
        choices=['font', 'svg', 'image'],
        required=False
    )


class IconRenderSerializer(serializers.Serializer):
    """图标渲染序列化器"""
    icon_id = serializers.UUIDField()
    size = serializers.CharField(max_length=20, required=False, default='16px')
    color = serializers.CharField(max_length=7, required=False)
    css_class = serializers.CharField(max_length=100, required=False)
    style = serializers.CharField(max_length=200, required=False)
    
    def validate_icon_id(self, value):
        """验证图标ID"""
        try:
            icon = Icon.objects.get(id=value, is_active=True)
            return value
        except Icon.DoesNotExist:
            raise serializers.ValidationError("图标不存在或已被禁用")
    
    def validate_color(self, value):
        """验证颜色格式"""
        if value and not value.startswith('#'):
            raise serializers.ValidationError("颜色格式必须为 #RRGGBB")
        return value


class IconBatchSerializer(serializers.Serializer):
    """批量图标操作序列化器"""
    icon_ids = serializers.ListField(
        child=serializers.UUIDField(),
        min_length=1
    )
    action = serializers.ChoiceField(
        choices=['activate', 'deactivate', 'delete', 'update_category']
    )
    category = serializers.CharField(max_length=50, required=False)
    
    def validate_icon_ids(self, value):
        """验证图标ID列表"""
        existing_ids = Icon.objects.filter(id__in=value).values_list('id', flat=True)
        missing_ids = set(value) - set(existing_ids)
        if missing_ids:
            raise serializers.ValidationError(f"以下图标不存在: {missing_ids}")
        return value
    
    def validate(self, attrs):
        """验证数据"""
        action = attrs.get('action')
        if action == 'update_category' and not attrs.get('category'):
            raise serializers.ValidationError("更新分类时必须提供分类名称")
        return attrs


class IconStatsSerializer(serializers.Serializer):
    """图标统计序列化器"""
    total_icons = serializers.IntegerField()
    total_icon_sets = serializers.IntegerField()
    most_used_icons = IconListSerializer(many=True)
    categories = serializers.DictField()
    icon_types = serializers.DictField()
