"""
图标模块URL配置
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

app_name = 'icons'

router = DefaultRouter()
router.register(r'icon-sets', views.IconSetViewSet)
router.register(r'icons', views.IconViewSet)

urlpatterns = [
    # REST API 路由
    path('', include(router.urls)),

    # 图标统计
    path('stats/', views.IconStatsView.as_view(), name='icon_stats'),
]
