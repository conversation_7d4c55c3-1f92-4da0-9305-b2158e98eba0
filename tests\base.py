"""
测试基础类和工具函数
"""
import json
from django.test import TestCase, TransactionTestCase
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from faker import Faker
import factory
from factory.django import DjangoModelFactory

User = get_user_model()
fake = Faker('zh_CN')


class BaseTestCase(TestCase):
    """基础测试类"""
    
    def setUp(self):
        """测试设置"""
        self.fake = fake
        self.user = self.create_test_user()
    
    def create_test_user(self, **kwargs):
        """创建测试用户"""
        defaults = {
            'username': fake.user_name(),
            'email': fake.email(),
            'first_name': fake.first_name(),
            'last_name': fake.last_name(),
            'is_active': True,
        }
        defaults.update(kwargs)
        
        password = defaults.pop('password', 'testpass123')
        user = User.objects.create_user(**defaults)
        user.set_password(password)
        user.save()
        return user
    
    def create_superuser(self, **kwargs):
        """创建超级用户"""
        defaults = {
            'username': 'admin',
            'email': '<EMAIL>',
            'is_staff': True,
            'is_superuser': True,
        }
        defaults.update(kwargs)
        return self.create_test_user(**defaults)


class BaseAPITestCase(APITestCase):
    """API测试基础类"""
    
    def setUp(self):
        """API测试设置"""
        self.fake = fake
        self.client = APIClient()
        self.user = self.create_test_user()
        self.superuser = self.create_superuser()
    
    def create_test_user(self, **kwargs):
        """创建测试用户"""
        defaults = {
            'username': fake.user_name(),
            'email': fake.email(),
            'first_name': fake.first_name(),
            'last_name': fake.last_name(),
            'is_active': True,
        }
        defaults.update(kwargs)
        
        password = defaults.pop('password', 'testpass123')
        user = User.objects.create_user(**defaults)
        user.set_password(password)
        user.save()
        return user
    
    def create_superuser(self, **kwargs):
        """创建超级用户"""
        defaults = {
            'username': 'admin',
            'email': '<EMAIL>',
            'is_staff': True,
            'is_superuser': True,
        }
        defaults.update(kwargs)
        return self.create_test_user(**defaults)
    
    def authenticate(self, user=None):
        """认证用户"""
        if user is None:
            user = self.user
        
        # 使用Session认证进行测试
        self.client.force_authenticate(user=user)
        return user
    
    def get_jwt_token(self, user=None):
        """获取JWT令牌"""
        if user is None:
            user = self.user
        
        # 这里需要根据实际的JWT认证实现来获取token
        # 暂时返回模拟token
        return f"test-token-{user.id}"
    
    def assert_response_success(self, response, status_code=status.HTTP_200_OK):
        """断言响应成功"""
        self.assertEqual(response.status_code, status_code)
        if hasattr(response, 'data'):
            return response.data
        return json.loads(response.content)
    
    def assert_response_error(self, response, status_code=status.HTTP_400_BAD_REQUEST):
        """断言响应错误"""
        self.assertEqual(response.status_code, status_code)
        if hasattr(response, 'data'):
            return response.data
        return json.loads(response.content)
    
    def assert_pagination_response(self, response, expected_count=None):
        """断言分页响应"""
        data = self.assert_response_success(response)
        self.assertIn('results', data)
        self.assertIn('count', data)
        self.assertIn('next', data)
        self.assertIn('previous', data)
        
        if expected_count is not None:
            self.assertEqual(data['count'], expected_count)
        
        return data


class UserFactory(DjangoModelFactory):
    """用户工厂类"""
    
    class Meta:
        model = User
    
    username = factory.Sequence(lambda n: f"user{n}")
    email = factory.LazyAttribute(lambda obj: f"{obj.username}@test.com")
    first_name = factory.Faker('first_name', locale='zh_CN')
    last_name = factory.Faker('last_name', locale='zh_CN')
    is_active = True
    
    @factory.post_generation
    def password(self, create, extracted, **kwargs):
        if not create:
            return
        
        password = extracted or 'testpass123'
        self.set_password(password)
        self.save()


class SuperUserFactory(UserFactory):
    """超级用户工厂类"""
    
    username = 'admin'
    email = '<EMAIL>'
    is_staff = True
    is_superuser = True


def create_test_data():
    """创建测试数据的辅助函数"""
    # 创建基础用户
    admin = SuperUserFactory()
    users = UserFactory.create_batch(5)
    
    return {
        'admin': admin,
        'users': users,
    }


class TestDataMixin:
    """测试数据混入类"""
    
    def create_test_data(self):
        """创建测试数据"""
        return create_test_data()
    
    def setUp(self):
        """设置测试数据"""
        super().setUp()
        self.test_data = self.create_test_data()


class PerformanceTestMixin:
    """性能测试混入类"""
    
    def assert_response_time(self, response, max_time=1.0):
        """断言响应时间"""
        # 这里可以添加响应时间检查逻辑
        pass
    
    def measure_query_count(self, func, max_queries=10):
        """测量查询次数"""
        from django.test.utils import override_settings
        from django.db import connection
        
        with override_settings(DEBUG=True):
            initial_queries = len(connection.queries)
            result = func()
            final_queries = len(connection.queries)
            query_count = final_queries - initial_queries
            
            self.assertLessEqual(
                query_count, 
                max_queries, 
                f"Too many queries: {query_count} > {max_queries}"
            )
            
            return result


class SecurityTestMixin:
    """安全测试混入类"""
    
    def test_unauthorized_access(self, url, method='GET'):
        """测试未授权访问"""
        self.client.logout()
        
        if method.upper() == 'GET':
            response = self.client.get(url)
        elif method.upper() == 'POST':
            response = self.client.post(url, {})
        elif method.upper() == 'PUT':
            response = self.client.put(url, {})
        elif method.upper() == 'DELETE':
            response = self.client.delete(url)
        else:
            raise ValueError(f"Unsupported method: {method}")
        
        self.assertIn(response.status_code, [
            status.HTTP_401_UNAUTHORIZED,
            status.HTTP_403_FORBIDDEN,
            status.HTTP_302_FOUND,  # 重定向到登录页
        ])
    
    def test_permission_denied(self, url, user, method='GET'):
        """测试权限拒绝"""
        self.authenticate(user)
        
        if method.upper() == 'GET':
            response = self.client.get(url)
        elif method.upper() == 'POST':
            response = self.client.post(url, {})
        elif method.upper() == 'PUT':
            response = self.client.put(url, {})
        elif method.upper() == 'DELETE':
            response = self.client.delete(url)
        else:
            raise ValueError(f"Unsupported method: {method}")
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
