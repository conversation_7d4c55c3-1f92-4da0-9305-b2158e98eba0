"""
主题模块序列化器
"""
from rest_framework import serializers
from .models import Theme, UserThemePreference


class ThemeSerializer(serializers.ModelSerializer):
    """主题序列化器"""
    css_variables = serializers.SerializerMethodField()
    created_by_name = serializers.CharField(source='created_by.username', read_only=True)
    
    class Meta:
        model = Theme
        fields = [
            'id', 'name', 'display_name', 'description', 'theme_type',
            'primary_color', 'secondary_color', 'success_color', 'warning_color',
            'danger_color', 'info_color', 'background_color', 'text_color',
            'border_color', 'navbar_bg_color', 'navbar_text_color',
            'sidebar_bg_color', 'sidebar_text_color', 'custom_css',
            'is_active', 'is_default', 'css_variables',
            'created_at', 'updated_at', 'created_by', 'created_by_name'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'created_by']
    
    def get_css_variables(self, obj):
        """获取CSS变量"""
        return obj.to_css_variables()
    
    def create(self, validated_data):
        """创建主题"""
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)


class ThemeListSerializer(serializers.ModelSerializer):
    """主题列表序列化器"""
    
    class Meta:
        model = Theme
        fields = [
            'id', 'name', 'display_name', 'description', 'theme_type',
            'primary_color', 'is_active', 'is_default'
        ]


class UserThemePreferenceSerializer(serializers.ModelSerializer):
    """用户主题偏好序列化器"""
    theme_name = serializers.CharField(source='theme.name', read_only=True)
    theme_display_name = serializers.CharField(source='theme.display_name', read_only=True)
    theme_details = ThemeSerializer(source='theme', read_only=True)
    
    class Meta:
        model = UserThemePreference
        fields = [
            'id', 'user', 'theme', 'theme_name', 'theme_display_name',
            'theme_details', 'custom_settings', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'user', 'created_at', 'updated_at']
    
    def create(self, validated_data):
        """创建用户主题偏好"""
        validated_data['user'] = self.context['request'].user
        # 如果用户已有主题偏好，则更新而不是创建
        preference, created = UserThemePreference.objects.update_or_create(
            user=validated_data['user'],
            defaults=validated_data
        )
        return preference


class ThemeConfigSerializer(serializers.Serializer):
    """主题配置序列化器"""
    theme_id = serializers.UUIDField()
    custom_settings = serializers.JSONField(required=False, default=dict)
    
    def validate_theme_id(self, value):
        """验证主题ID"""
        try:
            theme = Theme.objects.get(id=value, is_active=True)
            return value
        except Theme.DoesNotExist:
            raise serializers.ValidationError("主题不存在或已被禁用")


class ThemePreviewSerializer(serializers.Serializer):
    """主题预览序列化器"""
    primary_color = serializers.CharField(max_length=7)
    secondary_color = serializers.CharField(max_length=7)
    success_color = serializers.CharField(max_length=7)
    warning_color = serializers.CharField(max_length=7)
    danger_color = serializers.CharField(max_length=7)
    info_color = serializers.CharField(max_length=7)
    background_color = serializers.CharField(max_length=7)
    text_color = serializers.CharField(max_length=7)
    border_color = serializers.CharField(max_length=7)
    navbar_bg_color = serializers.CharField(max_length=7)
    navbar_text_color = serializers.CharField(max_length=7)
    sidebar_bg_color = serializers.CharField(max_length=7)
    sidebar_text_color = serializers.CharField(max_length=7)
    
    def validate_primary_color(self, value):
        """验证颜色格式"""
        if not value.startswith('#') or len(value) != 7:
            raise serializers.ValidationError("颜色格式必须为 #RRGGBB")
        return value
    
    def validate_secondary_color(self, value):
        if not value.startswith('#') or len(value) != 7:
            raise serializers.ValidationError("颜色格式必须为 #RRGGBB")
        return value
    
    def validate_success_color(self, value):
        if not value.startswith('#') or len(value) != 7:
            raise serializers.ValidationError("颜色格式必须为 #RRGGBB")
        return value
    
    def validate_warning_color(self, value):
        if not value.startswith('#') or len(value) != 7:
            raise serializers.ValidationError("颜色格式必须为 #RRGGBB")
        return value
    
    def validate_danger_color(self, value):
        if not value.startswith('#') or len(value) != 7:
            raise serializers.ValidationError("颜色格式必须为 #RRGGBB")
        return value
    
    def validate_info_color(self, value):
        if not value.startswith('#') or len(value) != 7:
            raise serializers.ValidationError("颜色格式必须为 #RRGGBB")
        return value
    
    def validate_background_color(self, value):
        if not value.startswith('#') or len(value) != 7:
            raise serializers.ValidationError("颜色格式必须为 #RRGGBB")
        return value
    
    def validate_text_color(self, value):
        if not value.startswith('#') or len(value) != 7:
            raise serializers.ValidationError("颜色格式必须为 #RRGGBB")
        return value
    
    def validate_border_color(self, value):
        if not value.startswith('#') or len(value) != 7:
            raise serializers.ValidationError("颜色格式必须为 #RRGGBB")
        return value
    
    def validate_navbar_bg_color(self, value):
        if not value.startswith('#') or len(value) != 7:
            raise serializers.ValidationError("颜色格式必须为 #RRGGBB")
        return value
    
    def validate_navbar_text_color(self, value):
        if not value.startswith('#') or len(value) != 7:
            raise serializers.ValidationError("颜色格式必须为 #RRGGBB")
        return value
    
    def validate_sidebar_bg_color(self, value):
        if not value.startswith('#') or len(value) != 7:
            raise serializers.ValidationError("颜色格式必须为 #RRGGBB")
        return value
    
    def validate_sidebar_text_color(self, value):
        if not value.startswith('#') or len(value) != 7:
            raise serializers.ValidationError("颜色格式必须为 #RRGGBB")
        return value
