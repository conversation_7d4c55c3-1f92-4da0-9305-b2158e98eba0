# 医疗设备管理系统

一个基于Django的现代化医疗设备管理系统，采用企业级项目结构，提供完整的设备生命周期管理、维护计划、库存管理和报表分析功能。

## 🏗️ 项目结构

```
medical_device_management/
├── 📁 src/                          # 源代码目录
│   ├── 📁 core/                     # 核心模块（共享功能）
│   │   ├── models.py                # 基础模型类
│   │   ├── services.py              # 业务逻辑服务层
│   │   ├── utils.py                 # 工具函数
│   │   ├── permissions.py           # 权限类
│   │   ├── serializers.py           # 基础序列化器
│   │   └── exceptions.py            # 异常处理
│   │
│   ├── 📁 medical_device_mgmt/      # Django主项目
│   ├── 📁 authentication/           # 认证模块
│   ├── 📁 themes/                   # 主题管理
│   ├── 📁 icons/                    # 图标库
│   ├── 📁 devices/                  # 设备管理
│   ├── 📁 maintenance/              # 维护管理
│   ├── 📁 inventory/                # 库存管理
│   ├── 📁 reports/                  # 报表分析
│   ├── 📁 monitoring/               # 系统监控
│   ├── 📁 templates/                # HTML模板
│   └── 📁 static/                   # 静态文件
│
├── 📁 tests/                        # 测试目录
│   ├── 📁 unit/                     # 单元测试
│   ├── 📁 integration/              # 集成测试
│   └── 📁 fixtures/                 # 测试数据
│
├── 📁 docs/                         # 文档目录
│   ├── README.md                    # 项目说明
│   └── PROJECT_STRUCTURE.md         # 项目结构说明
│
├── 📁 deployment/                   # 部署配置
│   ├── 📁 config/                   # 配置文件
│   │   ├── base_settings.py         # 基础配置
│   │   ├── test_settings.py         # 测试环境
│   │   └── production_settings.py   # 生产环境
│   └── 📁 scripts/                  # 部署脚本
│
├── 📁 data/                         # 数据目录
│   ├── 📁 backups/                  # 备份文件
│   ├── 📁 exports/                  # 导出文件
│   ├── 📁 imports/                  # 导入文件
│   ├── 📁 staticfiles/              # 收集的静态文件
│   └── 📄 db.sqlite3                # 数据库文件
│
├── 📁 logs/                         # 日志目录
├── 📁 media/                        # 媒体文件
│
├── 📄 manage.py                     # Django管理脚本
├── 📄 requirements.txt              # 基础依赖
├── 📄 requirements-dev.txt          # 开发依赖
├── 📄 requirements-prod.txt         # 生产依赖
├── 📄 start.py                      # 统一启动脚本
├── 📄 .env.example                  # 环境变量示例
├── 📄 .gitignore                    # Git忽略文件
└── 📄 README.md                     # 项目说明
```

## 🚀 快速开始

### 环境要求
- Python 3.10+
- Django 5.2.4
- 其他依赖见 requirements.txt

### 安装和运行

1. **克隆项目**
```bash
git clone <repository-url>
cd medical_device_management
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，设置必要的环境变量
```

4. **数据库迁移**
```bash
python manage.py migrate
```

5. **创建超级用户**
```bash
python manage.py createsuperuser
```

6. **创建测试数据**
```bash
python manage.py create_test_data
```

7. **启动服务器**
```bash
python start.py
# 或者
python manage.py runserver
```

## 📱 访问地址

- **首页**: http://127.0.0.1:8000/
- **管理后台**: http://127.0.0.1:8000/admin/
- **API根路径**: http://127.0.0.1:8000/api/
- **API文档**: http://127.0.0.1:8000/api-docs/
- **健康检查**: http://127.0.0.1:8000/health/

## 🏗️ 架构设计

### 核心设计原则

1. **关注点分离**: 源代码、测试、文档、部署配置分别存放
2. **分层架构**: Model-Service-View分层，业务逻辑在Service层
3. **模块化**: 每个功能模块独立，遵循单一职责原则
4. **配置分离**: 基础配置、开发环境、生产环境配置分离
5. **可扩展性**: 基于核心模块的可扩展架构

### 核心模块 (src/core/)

提供所有应用共享的基础功能：

- **BaseModel**: 基础模型类，包含通用字段和方法
- **AuditableModel**: 可审计模型类，自动记录创建者和修改者
- **CodeNameModel**: 编码名称模型类，适用于字典表
- **TreeModel**: 树形结构模型类，支持层级数据
- **BaseService**: 基础服务类，封装通用业务逻辑
- **权限类**: 基于角色、部门、时间等的权限控制
- **工具函数**: 通用的工具函数和验证器

### 业务模块

1. **authentication**: 三层令牌认证系统
2. **themes**: 动态主题管理
3. **icons**: 图标库封装
4. **devices**: 医疗设备管理
5. **maintenance**: 设备维护管理
6. **inventory**: 库存管理
7. **reports**: 报表分析
8. **monitoring**: 系统监控

## 🔧 开发指南

### 添加新模块

1. **创建Django应用**
```bash
cd src/
python ../manage.py startapp new_module
```

2. **继承核心基类**
```python
from core.models import BaseModel, AuditableModel
from core.services import BaseService
from core.serializers import BaseModelSerializer

class MyModel(AuditableModel):
    # 模型定义
    pass

class MyService(BaseService):
    model = MyModel
    # 业务逻辑
    pass
```

3. **注册应用**
在 `deployment/config/base_settings.py` 的 `LOCAL_APPS` 中添加新应用。

### 配置管理

- **基础配置**: `deployment/config/base_settings.py`
- **开发环境**: `src/medical_device_management/settings.py`
- **测试环境**: `deployment/config/test_settings.py`
- **生产环境**: `deployment/config/production_settings.py`

### 测试

```bash
# 运行所有测试
python manage.py test

# 运行特定模块测试
python manage.py test devices

# 使用测试配置
python manage.py test --settings=deployment.config.test_settings
```

## 📊 API文档

系统提供完整的RESTful API，支持：

- JWT三层令牌认证
- 基于角色的权限控制
- 统一的响应格式
- 完整的CRUD操作
- 批量操作支持
- 数据导入导出

详细API文档请访问：http://127.0.0.1:8000/api-docs/

## 🔒 安全特性

- JWT三层令牌认证（访问令牌、刷新令牌、设备令牌）
- 基于角色和部门的权限控制
- 请求日志记录和安全监控
- IP白名单和频率限制
- 数据脱敏和审计日志

## 📝 日志监控

- **请求日志**: 记录所有HTTP请求
- **性能日志**: 监控响应时间和系统资源
- **安全日志**: 记录安全相关事件
- **业务日志**: 记录业务操作和变更

## 🚀 部署

### 开发环境
```bash
python start.py
```

### 生产环境
```bash
# 使用生产配置
export DJANGO_SETTINGS_MODULE=deployment.config.production_settings
python deployment/scripts/deploy.py
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

---

**医疗设备管理系统** - 企业级Django项目的最佳实践示例
