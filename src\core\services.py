"""
核心服务层 - 业务逻辑处理
"""
from typing import Any, Dict, List, Optional, Type
from django.db import models, transaction
from django.core.exceptions import ValidationError
from django.contrib.auth import get_user_model
import logging

User = get_user_model()
logger = logging.getLogger(__name__)


class BaseService:
    """基础服务类"""
    model: Type[models.Model] = None
    
    def __init__(self, user: Optional[User] = None):
        self.user = user
    
    def get_queryset(self):
        """获取查询集"""
        if not self.model:
            raise NotImplementedError("Model must be defined")
        return self.model.objects.filter(is_active=True)
    
    def get_by_id(self, obj_id: Any) -> Optional[models.Model]:
        """根据ID获取对象"""
        try:
            return self.get_queryset().get(id=obj_id)
        except self.model.DoesNotExist:
            return None
    
    def create(self, data: Dict[str, Any]) -> models.Model:
        """创建对象"""
        with transaction.atomic():
            obj = self.model(**data)
            if hasattr(obj, 'created_by') and self.user:
                obj.created_by = self.user
            obj.full_clean()
            obj.save()
            logger.info(f"Created {self.model.__name__} with id {obj.id}")
            return obj
    
    def update(self, obj_id: Any, data: Dict[str, Any]) -> Optional[models.Model]:
        """更新对象"""
        obj = self.get_by_id(obj_id)
        if not obj:
            return None
        
        with transaction.atomic():
            for key, value in data.items():
                if hasattr(obj, key):
                    setattr(obj, key, value)
            
            if hasattr(obj, 'updated_by') and self.user:
                obj.updated_by = self.user
            
            obj.full_clean()
            obj.save()
            logger.info(f"Updated {self.model.__name__} with id {obj.id}")
            return obj
    
    def delete(self, obj_id: Any, soft: bool = True) -> bool:
        """删除对象"""
        obj = self.get_by_id(obj_id)
        if not obj:
            return False
        
        with transaction.atomic():
            if soft and hasattr(obj, 'soft_delete'):
                obj.soft_delete()
                logger.info(f"Soft deleted {self.model.__name__} with id {obj.id}")
            else:
                obj.delete()
                logger.info(f"Hard deleted {self.model.__name__} with id {obj.id}")
            return True
    
    def bulk_create(self, data_list: List[Dict[str, Any]]) -> List[models.Model]:
        """批量创建对象"""
        objects = []
        for data in data_list:
            obj = self.model(**data)
            if hasattr(obj, 'created_by') and self.user:
                obj.created_by = self.user
            objects.append(obj)
        
        with transaction.atomic():
            created_objects = self.model.objects.bulk_create(objects)
            logger.info(f"Bulk created {len(created_objects)} {self.model.__name__} objects")
            return created_objects
    
    def search(self, query: str, fields: List[str] = None) -> models.QuerySet:
        """搜索对象"""
        if not fields:
            fields = ['name']  # 默认搜索字段
        
        from django.db.models import Q
        q_objects = Q()
        
        for field in fields:
            if hasattr(self.model, field):
                q_objects |= Q(**{f"{field}__icontains": query})
        
        return self.get_queryset().filter(q_objects)


class TreeService(BaseService):
    """树形结构服务类"""
    
    def get_tree(self, parent_id: Any = None) -> List[Dict[str, Any]]:
        """获取树形结构"""
        def build_tree(parent=None):
            children = self.get_queryset().filter(parent=parent)
            tree = []
            for child in children:
                node = {
                    'id': str(child.id),
                    'code': child.code,
                    'name': child.name,
                    'level': child.level,
                    'children': build_tree(child)
                }
                tree.append(node)
            return tree
        
        parent = None
        if parent_id:
            parent = self.get_by_id(parent_id)
        
        return build_tree(parent)
    
    def move_node(self, node_id: Any, new_parent_id: Any = None) -> bool:
        """移动节点"""
        node = self.get_by_id(node_id)
        if not node:
            return False
        
        new_parent = None
        if new_parent_id:
            new_parent = self.get_by_id(new_parent_id)
            if not new_parent:
                return False
            
            # 检查是否会形成循环引用
            if node in new_parent.get_ancestors() or node == new_parent:
                raise ValidationError("Cannot move node to its descendant or itself")
        
        with transaction.atomic():
            node.parent = new_parent
            node.save()
            
            # 更新所有子节点的层级和路径
            descendants = node.get_descendants()
            for descendant in descendants:
                descendant.save()  # 触发save方法重新计算层级和路径
            
            logger.info(f"Moved node {node_id} to parent {new_parent_id}")
            return True


class AuditService:
    """审计服务类"""
    
    @staticmethod
    def get_model_changes(instance: models.Model, original_data: Dict[str, Any]) -> Dict[str, Any]:
        """获取模型变更"""
        changes = {}
        for field in instance._meta.fields:
            field_name = field.name
            old_value = original_data.get(field_name)
            new_value = getattr(instance, field_name)
            
            if old_value != new_value:
                changes[field_name] = {
                    'old': old_value,
                    'new': new_value
                }
        
        return changes
    
    @staticmethod
    def log_change(user: User, instance: models.Model, action: str, changes: Dict[str, Any] = None):
        """记录变更日志"""
        log_data = {
            'user': user.username if user else 'system',
            'model': instance._meta.label,
            'object_id': str(instance.pk),
            'action': action,
            'changes': changes or {}
        }
        
        logger.info(f"Model change: {log_data}")


class CacheService:
    """缓存服务类"""
    
    @staticmethod
    def get_cache_key(model_name: str, obj_id: Any, suffix: str = '') -> str:
        """生成缓存键"""
        key = f"{model_name}:{obj_id}"
        if suffix:
            key += f":{suffix}"
        return key
    
    @staticmethod
    def invalidate_cache(model_name: str, obj_id: Any = None):
        """清除缓存"""
        from django.core.cache import cache
        
        if obj_id:
            # 清除特定对象的缓存
            pattern = f"{model_name}:{obj_id}*"
        else:
            # 清除模型的所有缓存
            pattern = f"{model_name}:*"
        
        # 这里需要根据使用的缓存后端实现模式匹配删除
        # Redis可以使用SCAN命令，Memcached需要其他方式
        cache.clear()  # 简单实现，清除所有缓存
