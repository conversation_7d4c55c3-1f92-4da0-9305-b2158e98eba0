from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator
from decimal import Decimal
import uuid

User = get_user_model()


class Supplier(models.Model):
    """供应商模型"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=200, unique=True, verbose_name='供应商名称')
    code = models.CharField(max_length=50, unique=True, verbose_name='供应商代码')
    description = models.TextField(blank=True, null=True, verbose_name='描述')

    # 联系信息
    contact_person = models.CharField(max_length=100, blank=True, null=True, verbose_name='联系人')
    phone = models.CharField(max_length=20, blank=True, null=True, verbose_name='电话')
    email = models.EmailField(blank=True, null=True, verbose_name='邮箱')
    address = models.TextField(blank=True, null=True, verbose_name='地址')
    website = models.URLField(blank=True, null=True, verbose_name='网站')

    # 财务信息
    tax_number = models.CharField(max_length=50, blank=True, null=True, verbose_name='税号')
    bank_account = models.CharField(max_length=50, blank=True, null=True, verbose_name='银行账户')

    # 评级
    RATING_CHOICES = [
        (1, '很差'),
        (2, '差'),
        (3, '一般'),
        (4, '好'),
        (5, '很好'),
    ]
    rating = models.PositiveSmallIntegerField(
        choices=RATING_CHOICES,
        null=True,
        blank=True,
        verbose_name='供应商评级'
    )

    # 状态
    is_active = models.BooleanField(default=True, verbose_name='是否激活')

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '供应商'
        verbose_name_plural = '供应商'
        db_table = 'supplier'
        ordering = ['name']

    def __str__(self):
        return self.name


class ItemCategory(models.Model):
    """物品分类模型"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100, unique=True, verbose_name='分类名称')
    code = models.CharField(max_length=20, unique=True, verbose_name='分类代码')
    description = models.TextField(blank=True, null=True, verbose_name='分类描述')
    parent = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='children',
        verbose_name='父分类'
    )

    # 分类属性
    icon = models.CharField(max_length=50, blank=True, null=True, verbose_name='图标')
    color = models.CharField(max_length=7, default='#007bff', verbose_name='颜色')
    sort_order = models.PositiveIntegerField(default=0, verbose_name='排序')

    # 状态
    is_active = models.BooleanField(default=True, verbose_name='是否激活')

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '物品分类'
        verbose_name_plural = '物品分类'
        db_table = 'item_category'
        ordering = ['sort_order', 'name']

    def __str__(self):
        return self.name

    def get_full_path(self):
        """获取完整路径"""
        if self.parent:
            return f"{self.parent.get_full_path()} > {self.name}"
        return self.name


class Item(models.Model):
    """物品模型"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=200, verbose_name='物品名称')
    code = models.CharField(max_length=50, unique=True, verbose_name='物品编码')
    description = models.TextField(blank=True, null=True, verbose_name='描述')
    category = models.ForeignKey(
        ItemCategory,
        on_delete=models.CASCADE,
        verbose_name='分类'
    )

    # 规格信息
    specifications = models.JSONField(default=dict, blank=True, verbose_name='规格参数')
    unit = models.CharField(max_length=20, default='个', verbose_name='单位')
    brand = models.CharField(max_length=100, blank=True, null=True, verbose_name='品牌')
    model = models.CharField(max_length=100, blank=True, null=True, verbose_name='型号')

    # 价格信息
    standard_price = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name='标准价格'
    )

    # 库存配置
    min_stock = models.PositiveIntegerField(default=0, verbose_name='最小库存')
    max_stock = models.PositiveIntegerField(default=1000, verbose_name='最大库存')
    reorder_point = models.PositiveIntegerField(default=10, verbose_name='补货点')

    # 物品类型
    ITEM_TYPE_CHOICES = [
        ('consumable', '消耗品'),
        ('spare_part', '备件'),
        ('tool', '工具'),
        ('material', '材料'),
        ('other', '其他'),
    ]
    item_type = models.CharField(
        max_length=20,
        choices=ITEM_TYPE_CHOICES,
        default='consumable',
        verbose_name='物品类型'
    )

    # 图片
    image = models.ImageField(upload_to='items/', blank=True, null=True, verbose_name='物品图片')

    # 状态
    is_active = models.BooleanField(default=True, verbose_name='是否激活')

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name='创建者'
    )

    class Meta:
        verbose_name = '物品'
        verbose_name_plural = '物品'
        db_table = 'item'
        ordering = ['name']

    def __str__(self):
        return f"{self.code} - {self.name}"

    @property
    def current_stock(self):
        """当前库存"""
        from django.db.models import Sum
        stock_in = self.stockmovement_set.filter(
            movement_type='in'
        ).aggregate(total=Sum('quantity'))['total'] or 0

        stock_out = self.stockmovement_set.filter(
            movement_type='out'
        ).aggregate(total=Sum('quantity'))['total'] or 0

        return stock_in - stock_out

    @property
    def is_low_stock(self):
        """是否低库存"""
        return self.current_stock <= self.reorder_point

    @property
    def stock_status(self):
        """库存状态"""
        current = self.current_stock
        if current <= 0:
            return 'out_of_stock'
        elif current <= self.reorder_point:
            return 'low_stock'
        elif current >= self.max_stock:
            return 'overstock'
        else:
            return 'normal'


class StockMovement(models.Model):
    """库存变动记录"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    item = models.ForeignKey(
        Item,
        on_delete=models.CASCADE,
        verbose_name='物品'
    )

    # 变动信息
    MOVEMENT_TYPE_CHOICES = [
        ('in', '入库'),
        ('out', '出库'),
        ('transfer', '调拨'),
        ('adjustment', '调整'),
    ]
    movement_type = models.CharField(
        max_length=20,
        choices=MOVEMENT_TYPE_CHOICES,
        verbose_name='变动类型'
    )

    quantity = models.IntegerField(verbose_name='数量')
    unit_price = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name='单价'
    )
    total_amount = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name='总金额'
    )

    # 关联信息
    reference_number = models.CharField(max_length=100, blank=True, null=True, verbose_name='参考单号')
    supplier = models.ForeignKey(
        Supplier,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name='供应商'
    )

    # 备注
    notes = models.TextField(blank=True, null=True, verbose_name='备注')

    # 操作人员
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        verbose_name='操作人'
    )

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')

    class Meta:
        verbose_name = '库存变动记录'
        verbose_name_plural = '库存变动记录'
        db_table = 'stock_movement'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.item.name} - {self.get_movement_type_display()} - {self.quantity}"

    def save(self, *args, **kwargs):
        # 自动计算总金额
        if self.unit_price and self.quantity:
            self.total_amount = self.unit_price * abs(self.quantity)
        super().save(*args, **kwargs)


class PurchaseOrder(models.Model):
    """采购订单模型"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    order_number = models.CharField(max_length=50, unique=True, verbose_name='订单号')
    supplier = models.ForeignKey(
        Supplier,
        on_delete=models.CASCADE,
        verbose_name='供应商'
    )

    # 订单信息
    order_date = models.DateField(verbose_name='订单日期')
    expected_delivery_date = models.DateField(null=True, blank=True, verbose_name='预计交货日期')
    actual_delivery_date = models.DateField(null=True, blank=True, verbose_name='实际交货日期')

    # 状态
    STATUS_CHOICES = [
        ('draft', '草稿'),
        ('submitted', '已提交'),
        ('confirmed', '已确认'),
        ('partial_received', '部分收货'),
        ('received', '已收货'),
        ('cancelled', '已取消'),
    ]
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='draft',
        verbose_name='状态'
    )

    # 金额信息
    total_amount = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name='总金额'
    )

    # 备注
    notes = models.TextField(blank=True, null=True, verbose_name='备注')

    # 操作人员
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        verbose_name='创建者'
    )
    approved_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_purchase_orders',
        verbose_name='审批者'
    )

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '采购订单'
        verbose_name_plural = '采购订单'
        db_table = 'purchase_order'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.order_number} - {self.supplier.name}"


class PurchaseOrderItem(models.Model):
    """采购订单明细"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    purchase_order = models.ForeignKey(
        PurchaseOrder,
        on_delete=models.CASCADE,
        related_name='items',
        verbose_name='采购订单'
    )
    item = models.ForeignKey(
        Item,
        on_delete=models.CASCADE,
        verbose_name='物品'
    )

    # 数量和价格
    quantity = models.PositiveIntegerField(verbose_name='数量')
    unit_price = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        verbose_name='单价'
    )
    total_price = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        verbose_name='总价'
    )

    # 收货信息
    received_quantity = models.PositiveIntegerField(default=0, verbose_name='已收货数量')

    # 备注
    notes = models.TextField(blank=True, null=True, verbose_name='备注')

    class Meta:
        verbose_name = '采购订单明细'
        verbose_name_plural = '采购订单明细'
        db_table = 'purchase_order_item'
        unique_together = ['purchase_order', 'item']

    def __str__(self):
        return f"{self.purchase_order.order_number} - {self.item.name}"

    def save(self, *args, **kwargs):
        # 自动计算总价
        self.total_price = self.quantity * self.unit_price
        super().save(*args, **kwargs)

    @property
    def remaining_quantity(self):
        """剩余数量"""
        return self.quantity - self.received_quantity

    @property
    def is_fully_received(self):
        """是否完全收货"""
        return self.received_quantity >= self.quantity
