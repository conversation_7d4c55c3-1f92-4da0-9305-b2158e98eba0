import { apiRequest } from './request'

export const devicesApi = {
  // 设备管理
  devices: {
    // 获取设备列表
    list: (params) => apiRequest.get('/devices/devices/', params),
    
    // 获取设备详情
    get: (id) => apiRequest.get(`/devices/devices/${id}/`),
    
    // 创建设备
    create: (data) => apiRequest.post('/devices/devices/', data),
    
    // 更新设备
    update: (id, data) => apiRequest.put(`/devices/devices/${id}/`, data),
    
    // 部分更新设备
    patch: (id, data) => apiRequest.patch(`/devices/devices/${id}/`, data),
    
    // 删除设备
    delete: (id) => apiRequest.delete(`/devices/devices/${id}/`),
    
    // 更改设备状态
    changeStatus: (id, status) => apiRequest.post(`/devices/devices/${id}/change_status/`, { status }),
    
    // 获取设备统计
    dashboard: () => apiRequest.get('/devices/devices/dashboard/'),
    
    // 添加设备附件
    addAttachment: (id, formData) => apiRequest.upload(`/devices/devices/${id}/add_attachment/`, formData),
    
    // 获取状态历史
    statusHistory: (id) => apiRequest.get(`/devices/devices/${id}/status_history/`)
  },
  
  // 设备分类
  categories: {
    list: (params) => apiRequest.get('/devices/categories/', params),
    get: (id) => apiRequest.get(`/devices/categories/${id}/`),
    create: (data) => apiRequest.post('/devices/categories/', data),
    update: (id, data) => apiRequest.put(`/devices/categories/${id}/`, data),
    delete: (id) => apiRequest.delete(`/devices/categories/${id}/`),
    tree: () => apiRequest.get('/devices/categories/tree/')
  },
  
  // 设备制造商
  manufacturers: {
    list: (params) => apiRequest.get('/devices/manufacturers/', params),
    get: (id) => apiRequest.get(`/devices/manufacturers/${id}/`),
    create: (data) => apiRequest.post('/devices/manufacturers/', data),
    update: (id, data) => apiRequest.put(`/devices/manufacturers/${id}/`, data),
    delete: (id) => apiRequest.delete(`/devices/manufacturers/${id}/`)
  },
  
  // 设备型号
  models: {
    list: (params) => apiRequest.get('/devices/models/', params),
    get: (id) => apiRequest.get(`/devices/models/${id}/`),
    create: (data) => apiRequest.post('/devices/models/', data),
    update: (id, data) => apiRequest.put(`/devices/models/${id}/`, data),
    delete: (id) => apiRequest.delete(`/devices/models/${id}/`)
  },
  
  // 设备位置
  locations: {
    list: (params) => apiRequest.get('/devices/locations/', params),
    get: (id) => apiRequest.get(`/devices/locations/${id}/`),
    create: (data) => apiRequest.post('/devices/locations/', data),
    update: (id, data) => apiRequest.put(`/devices/locations/${id}/`, data),
    delete: (id) => apiRequest.delete(`/devices/locations/${id}/`),
    tree: () => apiRequest.get('/devices/locations/tree/')
  }
}
