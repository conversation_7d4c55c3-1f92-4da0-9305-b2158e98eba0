import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authApi } from '@/api/auth'
import { ElMessage } from 'element-plus'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref(localStorage.getItem('token') || '')
  const refreshToken = ref(localStorage.getItem('refreshToken') || '')
  const user = ref(null)
  const loading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)

  // 登录
  const login = async (credentials) => {
    loading.value = true
    try {
      const response = await authApi.login(credentials)
      const { access_token, refresh_token, user: userData } = response.data
      
      // 保存认证信息
      token.value = access_token
      refreshToken.value = refresh_token
      user.value = userData
      
      // 保存到本地存储
      localStorage.setItem('token', access_token)
      localStorage.setItem('refreshToken', refresh_token)
      localStorage.setItem('user', JSON.stringify(userData))
      
      ElMessage.success('登录成功')
      return true
    } catch (error) {
      ElMessage.error(error.response?.data?.message || '登录失败')
      return false
    } finally {
      loading.value = false
    }
  }

  // 登出
  const logout = async () => {
    try {
      if (token.value) {
        await authApi.logout()
      }
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      // 清除认证信息
      token.value = ''
      refreshToken.value = ''
      user.value = null
      
      // 清除本地存储
      localStorage.removeItem('token')
      localStorage.removeItem('refreshToken')
      localStorage.removeItem('user')
      
      ElMessage.success('已退出登录')
    }
  }

  // 刷新令牌
  const refreshAccessToken = async () => {
    if (!refreshToken.value) {
      return false
    }
    
    try {
      const response = await authApi.refreshToken(refreshToken.value)
      const { access_token } = response.data
      
      token.value = access_token
      localStorage.setItem('token', access_token)
      
      return true
    } catch (error) {
      console.error('Token refresh failed:', error)
      await logout()
      return false
    }
  }

  // 检查认证状态
  const checkAuth = async () => {
    const storedToken = localStorage.getItem('token')
    const storedUser = localStorage.getItem('user')
    
    if (storedToken && storedUser) {
      token.value = storedToken
      user.value = JSON.parse(storedUser)
      
      try {
        // 验证token是否有效
        await authApi.getProfile()
        return true
      } catch (error) {
        // Token无效，尝试刷新
        return await refreshAccessToken()
      }
    }
    
    return false
  }

  // 更新用户信息
  const updateProfile = async (profileData) => {
    loading.value = true
    try {
      const response = await authApi.updateProfile(profileData)
      user.value = { ...user.value, ...response.data }
      localStorage.setItem('user', JSON.stringify(user.value))
      ElMessage.success('个人信息更新成功')
      return true
    } catch (error) {
      ElMessage.error(error.response?.data?.message || '更新失败')
      return false
    } finally {
      loading.value = false
    }
  }

  // 修改密码
  const changePassword = async (passwordData) => {
    loading.value = true
    try {
      await authApi.changePassword(passwordData)
      ElMessage.success('密码修改成功，请重新登录')
      await logout()
      return true
    } catch (error) {
      ElMessage.error(error.response?.data?.message || '密码修改失败')
      return false
    } finally {
      loading.value = false
    }
  }

  return {
    // 状态
    token,
    refreshToken,
    user,
    loading,
    
    // 计算属性
    isAuthenticated,
    
    // 方法
    login,
    logout,
    refreshAccessToken,
    checkAuth,
    updateProfile,
    changePassword
  }
})
