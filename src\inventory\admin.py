from django.contrib import admin
from django.utils.html import format_html
from .models import Supplier, ItemCategory, Item, StockMovement, PurchaseOrder, PurchaseOrderItem


@admin.register(Supplier)
class SupplierAdmin(admin.ModelAdmin):
    """供应商管理"""
    list_display = [
        'name', 'code', 'contact_person', 'phone', 'email',
        'rating_stars', 'is_active', 'created_at'
    ]
    list_filter = ['rating', 'is_active', 'created_at']
    search_fields = ['name', 'code', 'contact_person']
    ordering = ['name']
    readonly_fields = ['created_at', 'updated_at']

    def rating_stars(self, obj):
        """评级星星"""
        if obj.rating:
            stars = '★' * obj.rating + '☆' * (5 - obj.rating)
            return format_html('<span style="color: #ffc107;">{}</span>', stars)
        return '-'
    rating_stars.short_description = '评级'


@admin.register(ItemCategory)
class ItemCategoryAdmin(admin.ModelAdmin):
    """物品分类管理"""
    list_display = [
        'name', 'code', 'parent', 'icon_preview', 'sort_order',
        'item_count', 'is_active', 'created_at'
    ]
    list_filter = ['is_active', 'parent', 'created_at']
    search_fields = ['name', 'code', 'description']
    ordering = ['sort_order', 'name']
    readonly_fields = ['created_at', 'updated_at']

    def icon_preview(self, obj):
        """图标预览"""
        if obj.icon:
            return format_html(
                '<i class="{}" style="color: {}; font-size: 16px;"></i>',
                obj.icon,
                obj.color
            )
        return '-'
    icon_preview.short_description = '图标'

    def item_count(self, obj):
        """物品数量"""
        return obj.item_set.filter(is_active=True).count()
    item_count.short_description = '物品数量'


@admin.register(Item)
class ItemAdmin(admin.ModelAdmin):
    """物品管理"""
    list_display = [
        'code', 'name', 'category', 'brand', 'unit',
        'current_stock_display', 'stock_status_badge',
        'standard_price', 'is_active', 'created_at'
    ]
    list_filter = [
        'category', 'item_type', 'is_active', 'created_at'
    ]
    search_fields = ['name', 'code', 'brand', 'model']
    ordering = ['name']
    readonly_fields = ['current_stock', 'is_low_stock', 'stock_status', 'created_at', 'updated_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'code', 'description', 'category')
        }),
        ('规格信息', {
            'fields': ('specifications', 'unit', 'brand', 'model', 'item_type')
        }),
        ('价格信息', {
            'fields': ('standard_price',)
        }),
        ('库存配置', {
            'fields': ('min_stock', 'max_stock', 'reorder_point')
        }),
        ('库存状态', {
            'fields': ('current_stock', 'is_low_stock', 'stock_status'),
            'classes': ('collapse',)
        }),
        ('其他信息', {
            'fields': ('image', 'is_active'),
            'classes': ('collapse',)
        }),
        ('系统信息', {
            'fields': ('created_at', 'updated_at', 'created_by'),
            'classes': ('collapse',)
        }),
    )

    def current_stock_display(self, obj):
        """当前库存显示"""
        stock = obj.current_stock
        if stock <= 0:
            color = '#dc3545'
        elif obj.is_low_stock:
            color = '#ffc107'
        else:
            color = '#28a745'

        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            stock
        )
    current_stock_display.short_description = '当前库存'

    def stock_status_badge(self, obj):
        """库存状态徽章"""
        status_colors = {
            'out_of_stock': '#dc3545',
            'low_stock': '#ffc107',
            'normal': '#28a745',
            'overstock': '#17a2b8',
        }
        status_names = {
            'out_of_stock': '缺货',
            'low_stock': '低库存',
            'normal': '正常',
            'overstock': '超库存',
        }

        status = obj.stock_status
        color = status_colors.get(status, '#6c757d')
        name = status_names.get(status, '未知')

        return format_html(
            '<span style="background-color: {}; color: white; padding: 2px 8px; '
            'border-radius: 12px; font-size: 12px;">{}</span>',
            color,
            name
        )
    stock_status_badge.short_description = '库存状态'

    def save_model(self, request, obj, form, change):
        """保存模型时设置创建者"""
        if not change:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(StockMovement)
class StockMovementAdmin(admin.ModelAdmin):
    """库存变动管理"""
    list_display = [
        'item', 'movement_type_badge', 'quantity_display',
        'unit_price', 'total_amount', 'supplier',
        'created_by', 'created_at'
    ]
    list_filter = ['movement_type', 'created_at', 'supplier']
    search_fields = ['item__name', 'item__code', 'reference_number']
    ordering = ['-created_at']
    readonly_fields = ['total_amount', 'created_at']

    def movement_type_badge(self, obj):
        """变动类型徽章"""
        colors = {
            'in': '#28a745',
            'out': '#dc3545',
            'transfer': '#17a2b8',
            'adjustment': '#ffc107',
        }
        color = colors.get(obj.movement_type, '#6c757d')
        return format_html(
            '<span style="background-color: {}; color: white; padding: 2px 8px; '
            'border-radius: 12px; font-size: 12px;">{}</span>',
            color,
            obj.get_movement_type_display()
        )
    movement_type_badge.short_description = '变动类型'

    def quantity_display(self, obj):
        """数量显示"""
        if obj.movement_type == 'out':
            return format_html('<span style="color: #dc3545;">-{}</span>', obj.quantity)
        else:
            return format_html('<span style="color: #28a745;">+{}</span>', obj.quantity)
    quantity_display.short_description = '数量'

    def save_model(self, request, obj, form, change):
        """保存模型时设置创建者"""
        if not change:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)
