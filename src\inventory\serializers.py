"""
库存模块序列化器
"""
from rest_framework import serializers
from .models import Supplier, ItemCategory, Item, StockMovement, PurchaseOrder, PurchaseOrderItem


class SupplierSerializer(serializers.ModelSerializer):
    """供应商序列化器"""
    item_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Supplier
        fields = [
            'id', 'name', 'code', 'description', 'contact_person', 'phone',
            'email', 'address', 'website', 'rating', 'is_active',
            'item_count', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_item_count(self, obj):
        """获取物品数量"""
        return Item.objects.filter(
            stockmovement__supplier=obj
        ).distinct().count()


class ItemCategorySerializer(serializers.ModelSerializer):
    """物品分类序列化器"""
    full_path = serializers.SerializerMethodField()
    children_count = serializers.SerializerMethodField()
    item_count = serializers.SerializerMethodField()
    
    class Meta:
        model = ItemCategory
        fields = [
            'id', 'name', 'code', 'description', 'parent', 'icon', 'color',
            'sort_order', 'is_active', 'full_path', 'children_count', 'item_count',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_full_path(self, obj):
        """获取完整路径"""
        return obj.get_full_path()
    
    def get_children_count(self, obj):
        """获取子分类数量"""
        return obj.children.filter(is_active=True).count()
    
    def get_item_count(self, obj):
        """获取物品数量"""
        return obj.item_set.filter(is_active=True).count()


class ItemSerializer(serializers.ModelSerializer):
    """物品序列化器"""
    category_name = serializers.CharField(source='category.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.username', read_only=True)
    
    # 计算字段
    current_stock = serializers.ReadOnlyField()
    is_low_stock = serializers.ReadOnlyField()
    stock_status = serializers.ReadOnlyField()
    
    class Meta:
        model = Item
        fields = [
            'id', 'name', 'code', 'description', 'category', 'category_name',
            'specifications', 'unit', 'brand', 'model', 'standard_price',
            'min_stock', 'max_stock', 'reorder_point', 'item_type', 'image',
            'is_active', 'current_stock', 'is_low_stock', 'stock_status',
            'created_at', 'updated_at', 'created_by', 'created_by_name'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'created_by']
    
    def create(self, validated_data):
        """创建物品"""
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)


class ItemListSerializer(serializers.ModelSerializer):
    """物品列表序列化器"""
    category_name = serializers.CharField(source='category.name', read_only=True)
    current_stock = serializers.ReadOnlyField()
    stock_status = serializers.ReadOnlyField()
    
    class Meta:
        model = Item
        fields = [
            'id', 'name', 'code', 'category_name', 'unit', 'brand',
            'standard_price', 'current_stock', 'reorder_point',
            'stock_status', 'is_active'
        ]


class StockMovementSerializer(serializers.ModelSerializer):
    """库存变动序列化器"""
    item_name = serializers.CharField(source='item.name', read_only=True)
    item_code = serializers.CharField(source='item.code', read_only=True)
    supplier_name = serializers.CharField(source='supplier.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.username', read_only=True)
    
    class Meta:
        model = StockMovement
        fields = [
            'id', 'item', 'item_name', 'item_code', 'movement_type',
            'quantity', 'unit_price', 'total_amount', 'reference_number',
            'supplier', 'supplier_name', 'notes', 'created_by',
            'created_by_name', 'created_at'
        ]
        read_only_fields = ['id', 'total_amount', 'created_by', 'created_at']
    
    def create(self, validated_data):
        """创建库存变动"""
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)


class PurchaseOrderItemSerializer(serializers.ModelSerializer):
    """采购订单明细序列化器"""
    item_name = serializers.CharField(source='item.name', read_only=True)
    item_code = serializers.CharField(source='item.code', read_only=True)
    remaining_quantity = serializers.ReadOnlyField()
    is_fully_received = serializers.ReadOnlyField()
    
    class Meta:
        model = PurchaseOrderItem
        fields = [
            'id', 'item', 'item_name', 'item_code', 'quantity',
            'unit_price', 'total_price', 'received_quantity',
            'remaining_quantity', 'is_fully_received', 'notes'
        ]
        read_only_fields = ['id', 'total_price']


class PurchaseOrderSerializer(serializers.ModelSerializer):
    """采购订单序列化器"""
    supplier_name = serializers.CharField(source='supplier.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.username', read_only=True)
    approved_by_name = serializers.CharField(source='approved_by.username', read_only=True)
    items = PurchaseOrderItemSerializer(many=True, read_only=True)
    
    class Meta:
        model = PurchaseOrder
        fields = [
            'id', 'order_number', 'supplier', 'supplier_name', 'order_date',
            'expected_delivery_date', 'actual_delivery_date', 'status',
            'total_amount', 'notes', 'created_by', 'created_by_name',
            'approved_by', 'approved_by_name', 'items',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_by', 'created_at', 'updated_at']
    
    def create(self, validated_data):
        """创建采购订单"""
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)


class PurchaseOrderListSerializer(serializers.ModelSerializer):
    """采购订单列表序列化器"""
    supplier_name = serializers.CharField(source='supplier.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.username', read_only=True)
    item_count = serializers.SerializerMethodField()
    
    class Meta:
        model = PurchaseOrder
        fields = [
            'id', 'order_number', 'supplier_name', 'order_date',
            'expected_delivery_date', 'status', 'total_amount',
            'item_count', 'created_by_name'
        ]
    
    def get_item_count(self, obj):
        """获取明细数量"""
        return obj.items.count()


class InventoryStatsSerializer(serializers.Serializer):
    """库存统计序列化器"""
    total_items = serializers.IntegerField()
    low_stock_items = serializers.IntegerField()
    out_of_stock_items = serializers.IntegerField()
    total_suppliers = serializers.IntegerField()
    pending_orders = serializers.IntegerField()
    total_stock_value = serializers.DecimalField(max_digits=12, decimal_places=2)
    category_distribution = serializers.DictField()
    stock_status_distribution = serializers.DictField()
    recent_movements = StockMovementSerializer(many=True)
    low_stock_items_list = ItemListSerializer(many=True)
