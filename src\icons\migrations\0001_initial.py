# Generated by Django 5.2.4 on 2025-08-05 04:16

import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='IconSet',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=50, unique=True, verbose_name='图标集名称')),
                ('display_name', models.CharField(max_length=100, verbose_name='显示名称')),
                ('description', models.TextField(blank=True, null=True, verbose_name='描述')),
                ('version', models.CharField(max_length=20, verbose_name='版本')),
                ('base_url', models.URLField(blank=True, null=True, verbose_name='基础URL')),
                ('css_url', models.URLField(blank=True, null=True, verbose_name='CSS文件URL')),
                ('js_url', models.URLField(blank=True, null=True, verbose_name='JS文件URL')),
                ('icon_type', models.CharField(choices=[('font', '字体图标'), ('svg', 'SVG图标'), ('image', '图片图标')], default='font', max_length=20, verbose_name='图标类型')),
                ('prefix', models.CharField(default='icon-', max_length=20, verbose_name='图标前缀')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否激活')),
                ('is_default', models.BooleanField(default=False, verbose_name='是否默认图标集')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '图标集',
                'verbose_name_plural': '图标集',
                'db_table': 'icon_set',
            },
        ),
        migrations.CreateModel(
            name='Icon',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100, verbose_name='图标名称')),
                ('display_name', models.CharField(max_length=100, verbose_name='显示名称')),
                ('description', models.TextField(blank=True, null=True, verbose_name='描述')),
                ('icon_class', models.CharField(blank=True, max_length=100, null=True, verbose_name='图标类名')),
                ('svg_content', models.TextField(blank=True, null=True, verbose_name='SVG内容')),
                ('image_url', models.URLField(blank=True, null=True, verbose_name='图片URL')),
                ('category', models.CharField(blank=True, max_length=50, null=True, verbose_name='分类')),
                ('tags', models.JSONField(blank=True, default=list, verbose_name='标签')),
                ('default_size', models.CharField(default='16px', max_length=20, verbose_name='默认大小')),
                ('default_color', models.CharField(default='#000000', max_length=7, verbose_name='默认颜色')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否激活')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('icon_set', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='icons.iconset', verbose_name='图标集')),
            ],
            options={
                'verbose_name': '图标',
                'verbose_name_plural': '图标',
                'db_table': 'icon',
                'unique_together': {('icon_set', 'name')},
            },
        ),
        migrations.CreateModel(
            name='IconUsage',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('usage_count', models.PositiveIntegerField(default=0, verbose_name='使用次数')),
                ('last_used_at', models.DateTimeField(auto_now=True, verbose_name='最后使用时间')),
                ('context', models.CharField(blank=True, max_length=100, null=True, verbose_name='使用上下文')),
                ('icon', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='icons.icon', verbose_name='图标')),
            ],
            options={
                'verbose_name': '图标使用记录',
                'verbose_name_plural': '图标使用记录',
                'db_table': 'icon_usage',
                'unique_together': {('icon', 'context')},
            },
        ),
    ]
