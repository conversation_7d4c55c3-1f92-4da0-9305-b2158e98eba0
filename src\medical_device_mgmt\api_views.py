"""
API根视图和统一接口
"""
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import permissions
from django.urls import reverse
from django.conf import settings


class APIRootView(APIView):
    """API根视图"""
    permission_classes = [permissions.AllowAny]
    
    def get(self, request, format=None):
        """返回API概览"""
        return Response({
            'message': '欢迎使用医疗设备管理系统API',
            'version': '1.0.0',
            'description': '提供完整的医疗设备管理功能API接口',
            'endpoints': {
                'authentication': {
                    'login': '/api/auth/login/',
                    'logout': '/api/auth/logout/',
                    'refresh': '/api/auth/refresh/',
                    'profile': '/api/auth/profile/',
                    'change_password': '/api/auth/change-password/',
                },
                'themes': {
                    'themes': '/api/themes/themes/',
                    'current': '/api/themes/themes/current/',
                    'css': '/api/themes/css/',
                    'default': '/api/themes/default/',
                    'preference': '/api/themes/preference/',
                },
                'icons': {
                    'icon_sets': '/api/icons/icon-sets/',
                    'icons': '/api/icons/icons/',
                    'search': '/api/icons/icons/search/',
                    'stats': '/api/icons/stats/',
                },
                'devices': {
                    'categories': '/api/devices/categories/',
                    'manufacturers': '/api/devices/manufacturers/',
                    'models': '/api/devices/models/',
                    'locations': '/api/devices/locations/',
                    'devices': '/api/devices/devices/',
                    'dashboard': '/api/devices/devices/dashboard/',
                },
                'maintenance': {
                    'types': '/api/maintenance/types/',
                    'plans': '/api/maintenance/plans/',
                    'records': '/api/maintenance/records/',
                    'faults': '/api/maintenance/faults/',
                },
                'inventory': {
                    'suppliers': '/api/inventory/suppliers/',
                    'categories': '/api/inventory/categories/',
                    'items': '/api/inventory/items/',
                    'movements': '/api/inventory/movements/',
                    'purchase_orders': '/api/inventory/purchase-orders/',
                },
                'reports': {
                    'templates': '/api/reports/templates/',
                    'instances': '/api/reports/instances/',
                    'dashboards': '/api/reports/dashboards/',
                    'dashboard_stats': '/api/reports/dashboard-stats/',
                }
            },
            'authentication': {
                'type': 'JWT Token',
                'header': 'Authorization: Bearer <token>',
                'login_endpoint': '/api/auth/login/',
                'refresh_endpoint': '/api/auth/refresh/',
            },
            'features': [
                '三层令牌认证系统',
                '动态主题管理',
                '图标库封装',
                '设备全生命周期管理',
                '智能维护计划',
                '库存自动预警',
                '报表分析系统',
                '仪表板统计',
            ],
            'documentation': {
                'api_docs': '/api-docs/',
                'admin_panel': '/admin/',
                'swagger': '/api/swagger/',  # 如果集成了swagger
                'redoc': '/api/redoc/',      # 如果集成了redoc
            }
        })


class APIHealthView(APIView):
    """API健康检查视图"""
    permission_classes = [permissions.AllowAny]
    
    def get(self, request, format=None):
        """返回API健康状态"""
        from django.db import connection
        from django.utils import timezone
        
        # 检查数据库连接
        try:
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
            db_status = 'healthy'
        except Exception as e:
            db_status = f'error: {str(e)}'
        
        # 检查各模块状态
        modules_status = {}
        
        try:
            from devices.models import Device
            device_count = Device.objects.count()
            modules_status['devices'] = {'status': 'healthy', 'count': device_count}
        except Exception as e:
            modules_status['devices'] = {'status': 'error', 'error': str(e)}
        
        try:
            from maintenance.models import MaintenanceRecord
            maintenance_count = MaintenanceRecord.objects.count()
            modules_status['maintenance'] = {'status': 'healthy', 'count': maintenance_count}
        except Exception as e:
            modules_status['maintenance'] = {'status': 'error', 'error': str(e)}
        
        try:
            from inventory.models import Item
            item_count = Item.objects.count()
            modules_status['inventory'] = {'status': 'healthy', 'count': item_count}
        except Exception as e:
            modules_status['inventory'] = {'status': 'error', 'error': str(e)}
        
        try:
            from reports.models import ReportTemplate
            template_count = ReportTemplate.objects.count()
            modules_status['reports'] = {'status': 'healthy', 'count': template_count}
        except Exception as e:
            modules_status['reports'] = {'status': 'error', 'error': str(e)}
        
        return Response({
            'status': 'healthy',
            'timestamp': timezone.now(),
            'version': '1.0.0',
            'database': db_status,
            'modules': modules_status,
            'system_info': {
                'django_version': settings.DJANGO_VERSION if hasattr(settings, 'DJANGO_VERSION') else 'Unknown',
                'debug_mode': settings.DEBUG,
                'allowed_hosts': settings.ALLOWED_HOSTS,
            }
        })


class APIStatsView(APIView):
    """API统计视图"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request, format=None):
        """返回API使用统计"""
        from django.contrib.auth import get_user_model
        from django.utils import timezone
        from datetime import timedelta
        
        User = get_user_model()
        
        # 基础统计
        stats = {
            'users': {
                'total': User.objects.count(),
                'active': User.objects.filter(is_active=True).count(),
                'staff': User.objects.filter(is_staff=True).count(),
            },
            'system': {
                'uptime': timezone.now(),
                'version': '1.0.0',
                'modules': ['authentication', 'themes', 'icons', 'devices', 'maintenance', 'inventory', 'reports'],
            }
        }
        
        # 各模块统计
        try:
            from devices.models import Device, DeviceCategory, DeviceManufacturer
            stats['devices'] = {
                'total_devices': Device.objects.filter(is_active=True).count(),
                'categories': DeviceCategory.objects.filter(is_active=True).count(),
                'manufacturers': DeviceManufacturer.objects.filter(is_active=True).count(),
            }
        except:
            stats['devices'] = {'error': 'Module not available'}
        
        try:
            from maintenance.models import MaintenancePlan, MaintenanceRecord, FaultReport
            stats['maintenance'] = {
                'active_plans': MaintenancePlan.objects.filter(is_active=True).count(),
                'total_records': MaintenanceRecord.objects.count(),
                'open_faults': FaultReport.objects.filter(status='open').count(),
            }
        except:
            stats['maintenance'] = {'error': 'Module not available'}
        
        try:
            from inventory.models import Item, Supplier, StockMovement
            stats['inventory'] = {
                'total_items': Item.objects.filter(is_active=True).count(),
                'suppliers': Supplier.objects.filter(is_active=True).count(),
                'movements_today': StockMovement.objects.filter(
                    created_at__date=timezone.now().date()
                ).count(),
            }
        except:
            stats['inventory'] = {'error': 'Module not available'}
        
        try:
            from reports.models import ReportTemplate, ReportInstance, Dashboard
            stats['reports'] = {
                'templates': ReportTemplate.objects.filter(is_active=True).count(),
                'instances': ReportInstance.objects.count(),
                'dashboards': Dashboard.objects.filter(is_active=True).count(),
            }
        except:
            stats['reports'] = {'error': 'Module not available'}
        
        return Response(stats)
