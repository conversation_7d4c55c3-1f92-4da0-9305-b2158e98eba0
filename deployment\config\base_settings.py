"""
基础配置文件 - 所有环境共享的配置
"""
import os
from pathlib import Path
from decouple import config

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent.parent

# 安全配置
SECRET_KEY = config('SECRET_KEY', default='django-insecure-change-me-in-production')
DEBUG = config('DEBUG', default=False, cast=bool)
ALLOWED_HOSTS = config('ALLOWED_HOSTS', default='localhost,127.0.0.1', cast=lambda v: [s.strip() for s in v.split(',')])

# 应用定义
DJANGO_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
]

THIRD_PARTY_APPS = [
    'rest_framework',
    'corsheaders',
    'drf_spectacular',
]

LOCAL_APPS = [
    'core',
    'medical_device_mgmt',
    'authentication',
    'themes',
    'icons',
    'devices',
    'maintenance',
    'inventory',
    'reports',
    'monitoring',
]

INSTALLED_APPS = DJANGO_APPS + THIRD_PARTY_APPS + LOCAL_APPS

# 中间件
MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'monitoring.middleware.HealthCheckMiddleware',
    'monitoring.middleware.RequestLoggingMiddleware',
    'monitoring.middleware.PerformanceMonitoringMiddleware',
    'monitoring.middleware.SecurityLoggingMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'medical_device_mgmt.urls'

# 模板配置
TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'src' / 'templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'medical_device_mgmt.wsgi.application'

# 数据库配置（基础配置，具体环境会覆盖）
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'data' / 'db.sqlite3',
    }
}

# 密码验证
AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# 国际化
LANGUAGE_CODE = 'zh-hans'
TIME_ZONE = 'Asia/Shanghai'
USE_I18N = True
USE_TZ = True

# 静态文件配置
STATIC_URL = '/static/'
STATICFILES_DIRS = [
    BASE_DIR / 'src' / 'static',
]
STATIC_ROOT = BASE_DIR / 'data' / 'staticfiles'

# 媒体文件配置
MEDIA_URL = 'media/'
MEDIA_ROOT = BASE_DIR / 'media'

# 默认主键字段类型
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# 自定义用户模型
AUTH_USER_MODEL = 'authentication.User'

# REST Framework配置
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework.authentication.SessionAuthentication',
        'authentication.authentication.JWTAuthentication',
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ],
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 20,
    'DEFAULT_FILTER_BACKENDS': [
        'rest_framework.filters.SearchFilter',
        'rest_framework.filters.OrderingFilter',
    ],
    'DEFAULT_RENDERER_CLASSES': [
        'rest_framework.renderers.JSONRenderer',
        'rest_framework.renderers.BrowsableAPIRenderer',
    ],
    'EXCEPTION_HANDLER': 'core.exceptions.custom_exception_handler',
    'DEFAULT_SCHEMA_CLASS': 'drf_spectacular.openapi.AutoSchema',
}

# CORS配置
CORS_ALLOWED_ORIGINS = config(
    'CORS_ALLOWED_ORIGINS',
    default='http://localhost:3000,http://127.0.0.1:3000',
    cast=lambda v: [s.strip() for s in v.split(',')]
)

CORS_ALLOW_CREDENTIALS = True

# JWT配置
JWT_SETTINGS = {
    'ACCESS_TOKEN_LIFETIME': 60 * 15,  # 15分钟
    'REFRESH_TOKEN_LIFETIME': 60 * 60 * 24 * 7,  # 7天
    'DEVICE_TOKEN_LIFETIME': 60 * 60 * 24 * 30,  # 30天
    'ALGORITHM': 'HS256',
    'SECRET_KEY': SECRET_KEY,
}

# 缓存配置（基础配置，具体环境会覆盖）
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'unique-snowflake',
    }
}

# 监控配置
SLOW_REQUEST_THRESHOLD = config('SLOW_REQUEST_THRESHOLD', default=1.0, cast=float)
SLOW_FUNCTION_THRESHOLD = config('SLOW_FUNCTION_THRESHOLD', default=0.5, cast=float)
DETAILED_PERFORMANCE_MONITORING = config('DETAILED_PERFORMANCE_MONITORING', default=True, cast=bool)

# 文件上传配置
FILE_UPLOAD_MAX_MEMORY_SIZE = 5 * 1024 * 1024  # 5MB
DATA_UPLOAD_MAX_MEMORY_SIZE = 5 * 1024 * 1024  # 5MB
FILE_UPLOAD_PERMISSIONS = 0o644

# 邮件配置（基础配置，具体环境会覆盖）
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'
DEFAULT_FROM_EMAIL = config('DEFAULT_FROM_EMAIL', default='<EMAIL>')

# 安全配置
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
X_FRAME_OPTIONS = 'DENY'

# 会话配置
SESSION_COOKIE_AGE = 60 * 60 * 24 * 7  # 7天
SESSION_COOKIE_HTTPONLY = True
SESSION_COOKIE_SECURE = config('SESSION_COOKIE_SECURE', default=False, cast=bool)

# CSRF配置
CSRF_COOKIE_HTTPONLY = True
CSRF_COOKIE_SECURE = config('CSRF_COOKIE_SECURE', default=False, cast=bool)

# API文档配置 (drf-spectacular)
SPECTACULAR_SETTINGS = {
    'TITLE': '医疗设备管理系统 API',
    'DESCRIPTION': '提供完整的医疗设备管理功能API接口，包括设备管理、维护计划、库存管理、报表分析等功能。',
    'VERSION': '1.0.0',
    'SERVE_INCLUDE_SCHEMA': False,
    'SCHEMA_PATH_PREFIX': '/api/',
    'COMPONENT_SPLIT_REQUEST': True,
    'SORT_OPERATIONS': False,
    'ENUM_NAME_OVERRIDES': {
        'ValidationErrorEnum': 'core.exceptions.ValidationErrorEnum',
    },
    'POSTPROCESSING_HOOKS': [],
    'TAGS': [
        {'name': 'authentication', 'description': '认证相关接口'},
        {'name': 'themes', 'description': '主题管理接口'},
        {'name': 'icons', 'description': '图标库接口'},
        {'name': 'devices', 'description': '设备管理接口'},
        {'name': 'maintenance', 'description': '维护管理接口'},
        {'name': 'inventory', 'description': '库存管理接口'},
        {'name': 'reports', 'description': '报表分析接口'},
        {'name': 'monitoring', 'description': '系统监控接口'},
    ],
    'CONTACT': {
        'name': '医疗设备管理系统',
        'email': '<EMAIL>',
    },
    'LICENSE': {
        'name': 'MIT License',
    },
}
