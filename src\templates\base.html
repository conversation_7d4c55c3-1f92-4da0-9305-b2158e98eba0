<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}医疗设备管理系统{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Feather Icons -->
    <link href="https://cdn.jsdelivr.net/npm/feather-icons@4.29.0/dist/feather.min.css" rel="stylesheet">
    
    <!-- 自定义样式 -->
    <style>
        :root {
            --primary-color: #007bff;
            --secondary-color: #6c757d;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
            --background-color: #ffffff;
            --text-color: #212529;
            --border-color: #dee2e6;
            --navbar-bg-color: #f8f9fa;
            --navbar-text-color: #495057;
            --sidebar-bg-color: #f8f9fa;
            --sidebar-text-color: #495057;
        }
        
        body {
            background-color: var(--background-color);
            color: var(--text-color);
        }
        
        .navbar {
            background-color: var(--navbar-bg-color) !important;
            border-bottom: 1px solid var(--border-color);
        }
        
        .navbar .navbar-brand,
        .navbar .nav-link {
            color: var(--navbar-text-color) !important;
        }
        
        .sidebar {
            background-color: var(--sidebar-bg-color);
            color: var(--sidebar-text-color);
            min-height: calc(100vh - 56px);
            border-right: 1px solid var(--border-color);
        }
        
        .main-content {
            padding: 20px;
        }
        
        .card {
            border-color: var(--border-color);
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .api-endpoint {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        .method-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-weight: bold;
            margin-right: 0.5rem;
        }
        
        .method-get { background-color: #28a745; color: white; }
        .method-post { background-color: #007bff; color: white; }
        .method-put { background-color: #ffc107; color: black; }
        .method-delete { background-color: #dc3545; color: white; }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-light">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i data-feather="activity" class="me-2"></i>
                医疗设备管理系统
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/">
                            <i data-feather="settings" class="me-1"></i>
                            管理后台
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/api/">
                            <i data-feather="code" class="me-1"></i>
                            API文档
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/">
                                <i data-feather="home" class="me-2"></i>
                                首页
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/api/devices/">
                                <i data-feather="cpu" class="me-2"></i>
                                设备管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/api/maintenance/">
                                <i data-feather="tool" class="me-2"></i>
                                维护管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/api/inventory/">
                                <i data-feather="package" class="me-2"></i>
                                库存管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/api/reports/">
                                <i data-feather="bar-chart-2" class="me-2"></i>
                                报表分析
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
            
            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                {% block content %}{% endblock %}
            </main>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Feather Icons -->
    <script src="https://cdn.jsdelivr.net/npm/feather-icons@4.29.0/dist/feather.min.js"></script>
    
    <script>
        // 初始化 Feather Icons
        feather.replace();
        
        // 主题切换功能
        function switchTheme(themeName) {
            // 这里可以调用API切换主题
            console.log('切换主题:', themeName);
        }
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
