"""
主应用视图
"""
from django.shortcuts import render
from django.http import JsonResponse
from django.contrib.auth.decorators import login_required


def index(request):
    """首页视图"""
    return render(request, 'index.html')

def modern_index(request):
    """现代化界面首页"""
    return render(request, 'modern_index.html')


def api_docs(request):
    """API文档视图"""
    return render(request, 'api_docs.html')


@login_required
def dashboard_data(request):
    """仪表板数据API（简化版）"""
    # 这里返回模拟数据，实际应该调用各模块的统计接口
    data = {
        'device_stats': {
            'total': 0,
            'normal': 0,
            'maintenance': 0,
            'fault': 0,
        },
        'maintenance_stats': {
            'total_this_month': 0,
            'completed_this_month': 0,
        },
        'inventory_stats': {
            'total_items': 0,
            'low_stock_items': 0,
        },
        'fault_stats': {
            'open_faults': 0,
            'critical_faults': 0,
        },
        'recent_activities': [],
        'alerts': []
    }
    
    return JsonResponse(data)
