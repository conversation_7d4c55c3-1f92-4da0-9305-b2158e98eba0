from django.shortcuts import render
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import permissions
from .performance import SystemMonitor, performance_collector


class SystemStatsView(APIView):
    """系统统计视图"""
    permission_classes = [permissions.IsAdminUser]
    
    def get(self, request):
        """获取系统统计信息"""
        stats = SystemMonitor.get_system_stats()
        recent_metrics = performance_collector.get_recent_metrics(10)
        
        return Response({
            'current_stats': stats,
            'recent_metrics': recent_metrics,
        })
