# Generated by Django 5.2.4 on 2025-08-05 04:48

import django.db.models.deletion
import uuid
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Supplier',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.Char<PERSON>ield(max_length=200, unique=True, verbose_name='供应商名称')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='供应商代码')),
                ('description', models.TextField(blank=True, null=True, verbose_name='描述')),
                ('contact_person', models.CharField(blank=True, max_length=100, null=True, verbose_name='联系人')),
                ('phone', models.Char<PERSON>ield(blank=True, max_length=20, null=True, verbose_name='电话')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='邮箱')),
                ('address', models.TextField(blank=True, null=True, verbose_name='地址')),
                ('website', models.URLField(blank=True, null=True, verbose_name='网站')),
                ('tax_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='税号')),
                ('bank_account', models.CharField(blank=True, max_length=50, null=True, verbose_name='银行账户')),
                ('rating', models.PositiveSmallIntegerField(blank=True, choices=[(1, '很差'), (2, '差'), (3, '一般'), (4, '好'), (5, '很好')], null=True, verbose_name='供应商评级')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否激活')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '供应商',
                'verbose_name_plural': '供应商',
                'db_table': 'supplier',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='ItemCategory',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='分类名称')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='分类代码')),
                ('description', models.TextField(blank=True, null=True, verbose_name='分类描述')),
                ('icon', models.CharField(blank=True, max_length=50, null=True, verbose_name='图标')),
                ('color', models.CharField(default='#007bff', max_length=7, verbose_name='颜色')),
                ('sort_order', models.PositiveIntegerField(default=0, verbose_name='排序')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否激活')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='inventory.itemcategory', verbose_name='父分类')),
            ],
            options={
                'verbose_name': '物品分类',
                'verbose_name_plural': '物品分类',
                'db_table': 'item_category',
                'ordering': ['sort_order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='Item',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200, verbose_name='物品名称')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='物品编码')),
                ('description', models.TextField(blank=True, null=True, verbose_name='描述')),
                ('specifications', models.JSONField(blank=True, default=dict, verbose_name='规格参数')),
                ('unit', models.CharField(default='个', max_length=20, verbose_name='单位')),
                ('brand', models.CharField(blank=True, max_length=100, null=True, verbose_name='品牌')),
                ('model', models.CharField(blank=True, max_length=100, null=True, verbose_name='型号')),
                ('standard_price', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True, verbose_name='标准价格')),
                ('min_stock', models.PositiveIntegerField(default=0, verbose_name='最小库存')),
                ('max_stock', models.PositiveIntegerField(default=1000, verbose_name='最大库存')),
                ('reorder_point', models.PositiveIntegerField(default=10, verbose_name='补货点')),
                ('item_type', models.CharField(choices=[('consumable', '消耗品'), ('spare_part', '备件'), ('tool', '工具'), ('material', '材料'), ('other', '其他')], default='consumable', max_length=20, verbose_name='物品类型')),
                ('image', models.ImageField(blank=True, null=True, upload_to='items/', verbose_name='物品图片')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否激活')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='创建者')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventory.itemcategory', verbose_name='分类')),
            ],
            options={
                'verbose_name': '物品',
                'verbose_name_plural': '物品',
                'db_table': 'item',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='PurchaseOrder',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('order_number', models.CharField(max_length=50, unique=True, verbose_name='订单号')),
                ('order_date', models.DateField(verbose_name='订单日期')),
                ('expected_delivery_date', models.DateField(blank=True, null=True, verbose_name='预计交货日期')),
                ('actual_delivery_date', models.DateField(blank=True, null=True, verbose_name='实际交货日期')),
                ('status', models.CharField(choices=[('draft', '草稿'), ('submitted', '已提交'), ('confirmed', '已确认'), ('partial_received', '部分收货'), ('received', '已收货'), ('cancelled', '已取消')], default='draft', max_length=20, verbose_name='状态')),
                ('total_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12, verbose_name='总金额')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='备注')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_purchase_orders', to=settings.AUTH_USER_MODEL, verbose_name='审批者')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='创建者')),
                ('supplier', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventory.supplier', verbose_name='供应商')),
            ],
            options={
                'verbose_name': '采购订单',
                'verbose_name_plural': '采购订单',
                'db_table': 'purchase_order',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='StockMovement',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('movement_type', models.CharField(choices=[('in', '入库'), ('out', '出库'), ('transfer', '调拨'), ('adjustment', '调整')], max_length=20, verbose_name='变动类型')),
                ('quantity', models.IntegerField(verbose_name='数量')),
                ('unit_price', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True, verbose_name='单价')),
                ('total_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True, verbose_name='总金额')),
                ('reference_number', models.CharField(blank=True, max_length=100, null=True, verbose_name='参考单号')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='备注')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='操作人')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventory.item', verbose_name='物品')),
                ('supplier', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='inventory.supplier', verbose_name='供应商')),
            ],
            options={
                'verbose_name': '库存变动记录',
                'verbose_name_plural': '库存变动记录',
                'db_table': 'stock_movement',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PurchaseOrderItem',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('quantity', models.PositiveIntegerField(verbose_name='数量')),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=12, verbose_name='单价')),
                ('total_price', models.DecimalField(decimal_places=2, max_digits=12, verbose_name='总价')),
                ('received_quantity', models.PositiveIntegerField(default=0, verbose_name='已收货数量')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='备注')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventory.item', verbose_name='物品')),
                ('purchase_order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='inventory.purchaseorder', verbose_name='采购订单')),
            ],
            options={
                'verbose_name': '采购订单明细',
                'verbose_name_plural': '采购订单明细',
                'db_table': 'purchase_order_item',
                'unique_together': {('purchase_order', 'item')},
            },
        ),
    ]
