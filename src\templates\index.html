{% extends 'base.html' %}

{% block title %}首页 - 医疗设备管理系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">系统概览</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary">
                <i data-feather="refresh-cw" class="me-1"></i>
                刷新数据
            </button>
        </div>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            设备总数
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-devices">
                            加载中...
                        </div>
                    </div>
                    <div class="col-auto">
                        <i data-feather="cpu" class="text-primary" style="width: 2rem; height: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            正常设备
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="normal-devices">
                            加载中...
                        </div>
                    </div>
                    <div class="col-auto">
                        <i data-feather="check-circle" class="text-success" style="width: 2rem; height: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            维护中设备
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="maintenance-devices">
                            加载中...
                        </div>
                    </div>
                    <div class="col-auto">
                        <i data-feather="tool" class="text-warning" style="width: 2rem; height: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-danger shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                            故障设备
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="fault-devices">
                            加载中...
                        </div>
                    </div>
                    <div class="col-auto">
                        <i data-feather="alert-triangle" class="text-danger" style="width: 2rem; height: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 告警信息 -->
<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i data-feather="bell" class="me-2"></i>
                    系统告警
                </h5>
            </div>
            <div class="card-body">
                <div id="alerts-container">
                    <div class="text-center text-muted">
                        <i data-feather="loader" class="me-2"></i>
                        加载中...
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 快速操作 -->
<div class="row">
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i data-feather="zap" class="me-2"></i>
                    快速操作
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="/admin/devices/device/add/" class="btn btn-primary">
                        <i data-feather="plus" class="me-2"></i>
                        添加新设备
                    </a>
                    <a href="/admin/maintenance/faultreport/add/" class="btn btn-warning">
                        <i data-feather="alert-circle" class="me-2"></i>
                        报告故障
                    </a>
                    <a href="/admin/inventory/stockmovement/add/" class="btn btn-info">
                        <i data-feather="package" class="me-2"></i>
                        库存变动
                    </a>
                    <a href="/admin/maintenance/maintenancerecord/add/" class="btn btn-success">
                        <i data-feather="tool" class="me-2"></i>
                        记录维护
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i data-feather="activity" class="me-2"></i>
                    最近活动
                </h5>
            </div>
            <div class="card-body">
                <div id="recent-activities">
                    <div class="text-center text-muted">
                        <i data-feather="loader" class="me-2"></i>
                        加载中...
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 系统信息 -->
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i data-feather="info" class="me-2"></i>
                    系统信息
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>系统特性</h6>
                        <ul class="list-unstyled">
                            <li><i data-feather="check" class="me-2 text-success"></i>三层令牌认证系统</li>
                            <li><i data-feather="check" class="me-2 text-success"></i>动态主题管理</li>
                            <li><i data-feather="check" class="me-2 text-success"></i>图标库封装</li>
                            <li><i data-feather="check" class="me-2 text-success"></i>设备全生命周期管理</li>
                            <li><i data-feather="check" class="me-2 text-success"></i>智能维护计划</li>
                            <li><i data-feather="check" class="me-2 text-success"></i>库存自动预警</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>技术架构</h6>
                        <ul class="list-unstyled">
                            <li><strong>后端框架:</strong> Django 5.2.4</li>
                            <li><strong>API框架:</strong> Django REST Framework</li>
                            <li><strong>数据库:</strong> SQLite</li>
                            <li><strong>认证:</strong> JWT Token</li>
                            <li><strong>前端:</strong> Bootstrap 5 + Feather Icons</li>
                            <li><strong>架构模式:</strong> 模块化设计</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 加载仪表板数据
async function loadDashboardStats() {
    try {
        const response = await fetch('/api/reports/dashboard-stats/');
        if (response.ok) {
            const data = await response.json();
            
            // 更新统计数字
            document.getElementById('total-devices').textContent = data.device_stats.total || 0;
            document.getElementById('normal-devices').textContent = data.device_stats.normal || 0;
            document.getElementById('maintenance-devices').textContent = data.device_stats.maintenance || 0;
            document.getElementById('fault-devices').textContent = data.device_stats.fault || 0;
            
            // 更新告警信息
            const alertsContainer = document.getElementById('alerts-container');
            if (data.alerts && data.alerts.length > 0) {
                alertsContainer.innerHTML = '';
                data.alerts.forEach(alert => {
                    const alertDiv = document.createElement('div');
                    alertDiv.className = `alert alert-${alert.type} alert-dismissible fade show`;
                    alertDiv.innerHTML = `
                        <i data-feather="alert-triangle" class="me-2"></i>
                        ${alert.message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    `;
                    alertsContainer.appendChild(alertDiv);
                });
                feather.replace();
            } else {
                alertsContainer.innerHTML = '<div class="text-success"><i data-feather="check-circle" class="me-2"></i>暂无告警信息</div>';
                feather.replace();
            }
            
            // 更新最近活动
            const activitiesContainer = document.getElementById('recent-activities');
            if (data.recent_activities && data.recent_activities.length > 0) {
                activitiesContainer.innerHTML = '';
                data.recent_activities.forEach(activity => {
                    const activityDiv = document.createElement('div');
                    activityDiv.className = 'mb-2 pb-2 border-bottom';
                    activityDiv.innerHTML = `
                        <small class="text-muted">${activity.time}</small>
                        <div>${activity.description}</div>
                    `;
                    activitiesContainer.appendChild(activityDiv);
                });
            } else {
                activitiesContainer.innerHTML = '<div class="text-muted">暂无最近活动</div>';
            }
        } else {
            console.error('Failed to load dashboard stats');
        }
    } catch (error) {
        console.error('Error loading dashboard stats:', error);
        // 显示默认数据
        document.getElementById('total-devices').textContent = '0';
        document.getElementById('normal-devices').textContent = '0';
        document.getElementById('maintenance-devices').textContent = '0';
        document.getElementById('fault-devices').textContent = '0';
        
        document.getElementById('alerts-container').innerHTML = 
            '<div class="text-muted">无法加载告警信息</div>';
        document.getElementById('recent-activities').innerHTML = 
            '<div class="text-muted">无法加载最近活动</div>';
    }
}

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    loadDashboardStats();
    
    // 每5分钟刷新一次数据
    setInterval(loadDashboardStats, 5 * 60 * 1000);
});
</script>
{% endblock %}
