from django.contrib import admin
from django.utils.html import format_html
from .models import (
    <PERSON>ceCategory, DeviceManufacturer, DeviceModel, Device,
    DeviceStatusHistory, DeviceAttachment, DeviceLocation
)


@admin.register(DeviceCategory)
class DeviceCategoryAdmin(admin.ModelAdmin):
    """设备分类管理"""
    list_display = [
        'name', 'code', 'parent', 'icon_preview', 'sort_order',
        'device_count', 'is_active', 'created_at'
    ]
    list_filter = ['is_active', 'parent', 'created_at']
    search_fields = ['name', 'code', 'description']
    ordering = ['sort_order', 'name']
    readonly_fields = ['created_at', 'updated_at']

    def icon_preview(self, obj):
        """图标预览"""
        if obj.icon:
            return format_html(
                '<i class="{}" style="color: {}; font-size: 16px;"></i>',
                obj.icon,
                obj.color
            )
        return '-'
    icon_preview.short_description = '图标'

    def device_count(self, obj):
        """设备数量"""
        return Device.objects.filter(device_model__category=obj, is_active=True).count()
    device_count.short_description = '设备数量'

    def save_model(self, request, obj, form, change):
        """保存模型时设置创建者"""
        if not change:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(DeviceManufacturer)
class DeviceManufacturerAdmin(admin.ModelAdmin):
    """设备制造商管理"""
    list_display = [
        'name', 'code', 'contact_person', 'phone', 'email',
        'model_count', 'is_active', 'created_at'
    ]
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'code', 'contact_person']
    ordering = ['name']
    readonly_fields = ['created_at', 'updated_at']

    def model_count(self, obj):
        """型号数量"""
        return obj.devicemodel_set.filter(is_active=True).count()
    model_count.short_description = '型号数量'


@admin.register(DeviceModel)
class DeviceModelAdmin(admin.ModelAdmin):
    """设备型号管理"""
    list_display = [
        'name', 'model_number', 'manufacturer', 'category',
        'purchase_price', 'device_count', 'is_active', 'created_at'
    ]
    list_filter = ['manufacturer', 'category', 'is_active', 'created_at']
    search_fields = ['name', 'model_number', 'description']
    ordering = ['manufacturer__name', 'name']
    readonly_fields = ['created_at', 'updated_at']

    def device_count(self, obj):
        """设备数量"""
        return obj.device_set.filter(is_active=True).count()
    device_count.short_description = '设备数量'


@admin.register(DeviceLocation)
class DeviceLocationAdmin(admin.ModelAdmin):
    """设备位置管理"""
    list_display = [
        'name', 'code', 'location_type', 'parent', 'manager',
        'device_count', 'is_active', 'created_at'
    ]
    list_filter = ['location_type', 'is_active', 'created_at']
    search_fields = ['name', 'code', 'description']
    ordering = ['name']
    readonly_fields = ['created_at', 'updated_at']

    def device_count(self, obj):
        """设备数量"""
        return Device.objects.filter(location=obj.name, is_active=True).count()
    device_count.short_description = '设备数量'


@admin.register(Device)
class DeviceAdmin(admin.ModelAdmin):
    """设备管理"""
    list_display = [
        'asset_number', 'name', 'device_model', 'department',
        'status_badge', 'responsible_person', 'warranty_status',
        'last_check_date', 'created_at'
    ]
    list_filter = [
        'status', 'usage_status', 'importance', 'device_model__category',
        'device_model__manufacturer', 'department', 'is_active', 'created_at'
    ]
    search_fields = [
        'asset_number', 'name', 'serial_number', 'device_model__name',
        'department', 'location'
    ]
    ordering = ['-created_at']
    readonly_fields = ['created_at', 'updated_at', 'is_under_warranty', 'warranty_days_left']

    fieldsets = (
        ('基本信息', {
            'fields': (
                'asset_number', 'serial_number', 'name', 'device_model'
            )
        }),
        ('位置信息', {
            'fields': ('department', 'location', 'room')
        }),
        ('责任人', {
            'fields': ('responsible_person', 'operator')
        }),
        ('采购信息', {
            'fields': (
                'purchase_date', 'purchase_price', 'supplier',
                'warranty_start_date', 'warranty_end_date'
            )
        }),
        ('状态信息', {
            'fields': ('status', 'usage_status', 'importance')
        }),
        ('检查信息', {
            'fields': (
                'last_check_date', 'last_check_result', 'next_check_date'
            )
        }),
        ('其他信息', {
            'fields': ('parameters', 'notes', 'is_active'),
            'classes': ('collapse',)
        }),
        ('系统信息', {
            'fields': (
                'is_under_warranty', 'warranty_days_left',
                'created_at', 'updated_at', 'created_by'
            ),
            'classes': ('collapse',)
        }),
    )

    def status_badge(self, obj):
        """状态徽章"""
        color = obj.get_status_display_color()
        return format_html(
            '<span style="background-color: {}; color: white; padding: 2px 8px; '
            'border-radius: 12px; font-size: 12px;">{}</span>',
            color,
            obj.get_status_display()
        )
    status_badge.short_description = '状态'

    def warranty_status(self, obj):
        """保修状态"""
        if obj.is_under_warranty:
            days_left = obj.warranty_days_left
            if days_left <= 30:
                color = '#ffc107'  # 警告色
                text = f'剩余{days_left}天'
            else:
                color = '#28a745'  # 成功色
                text = f'剩余{days_left}天'
        else:
            color = '#dc3545'  # 危险色
            text = '已过期'

        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            text
        )
    warranty_status.short_description = '保修状态'

    def save_model(self, request, obj, form, change):
        """保存模型时设置创建者"""
        if not change:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(DeviceStatusHistory)
class DeviceStatusHistoryAdmin(admin.ModelAdmin):
    """设备状态历史管理"""
    list_display = [
        'device', 'old_status', 'new_status', 'reason',
        'changed_by', 'changed_at'
    ]
    list_filter = ['old_status', 'new_status', 'changed_at']
    search_fields = ['device__asset_number', 'device__name', 'reason']
    ordering = ['-changed_at']
    readonly_fields = ['changed_at']


@admin.register(DeviceAttachment)
class DeviceAttachmentAdmin(admin.ModelAdmin):
    """设备附件管理"""
    list_display = [
        'device', 'name', 'attachment_type', 'file_size_display',
        'uploaded_by', 'uploaded_at'
    ]
    list_filter = ['attachment_type', 'uploaded_at']
    search_fields = ['device__asset_number', 'device__name', 'name']
    ordering = ['-uploaded_at']
    readonly_fields = ['file_size', 'uploaded_at']

    def file_size_display(self, obj):
        """文件大小显示"""
        if obj.file_size:
            if obj.file_size < 1024:
                return f'{obj.file_size} B'
            elif obj.file_size < 1024 * 1024:
                return f'{obj.file_size / 1024:.1f} KB'
            else:
                return f'{obj.file_size / (1024 * 1024):.1f} MB'
        return '-'
    file_size_display.short_description = '文件大小'

    def save_model(self, request, obj, form, change):
        """保存模型时设置上传者"""
        if not change:
            obj.uploaded_by = request.user
        super().save_model(request, obj, form, change)
