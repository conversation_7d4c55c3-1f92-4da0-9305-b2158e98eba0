import { apiRequest } from './request'

export const authApi = {
  // 登录
  login: (credentials) => {
    return apiRequest.post('/auth/login/', credentials)
  },
  
  // 登出
  logout: () => {
    return apiRequest.post('/auth/logout/')
  },
  
  // 刷新token
  refreshToken: (refreshToken) => {
    return apiRequest.post('/auth/refresh/', { refresh_token: refreshToken })
  },
  
  // 获取用户信息
  getProfile: () => {
    return apiRequest.get('/auth/profile/')
  },
  
  // 更新用户信息
  updateProfile: (data) => {
    return apiRequest.patch('/auth/profile/', data)
  },
  
  // 修改密码
  changePassword: (data) => {
    return apiRequest.post('/auth/change-password/', data)
  }
}
