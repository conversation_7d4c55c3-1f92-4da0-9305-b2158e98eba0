"""
认证模块序列化器
"""
from rest_framework import serializers
from django.contrib.auth import authenticate
from django.contrib.auth.password_validation import validate_password
from .models import User, Role, UserRole, LoginLog


class UserSerializer(serializers.ModelSerializer):
    """用户序列化器"""
    password = serializers.CharField(write_only=True, validators=[validate_password])
    confirm_password = serializers.CharField(write_only=True)
    roles = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = [
            'id', 'username', 'email', 'first_name', 'last_name',
            'phone', 'avatar', 'department', 'position', 'is_active',
            'password', 'confirm_password', 'roles', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_roles(self, obj):
        """获取用户角色"""
        user_roles = UserRole.objects.filter(user=obj).select_related('role')
        return [{'id': ur.role.id, 'name': ur.role.name} for ur in user_roles]
    
    def validate(self, attrs):
        """验证数据"""
        if attrs.get('password') != attrs.get('confirm_password'):
            raise serializers.ValidationError("密码不匹配")
        return attrs
    
    def create(self, validated_data):
        """创建用户"""
        validated_data.pop('confirm_password', None)
        password = validated_data.pop('password')
        user = User.objects.create_user(**validated_data)
        user.set_password(password)
        user.save()
        return user
    
    def update(self, instance, validated_data):
        """更新用户"""
        validated_data.pop('confirm_password', None)
        password = validated_data.pop('password', None)
        
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        
        if password:
            instance.set_password(password)
        
        instance.save()
        return instance


class RoleSerializer(serializers.ModelSerializer):
    """角色序列化器"""
    user_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Role
        fields = [
            'id', 'name', 'description', 'permissions', 'is_active',
            'user_count', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_user_count(self, obj):
        """获取角色用户数量"""
        return UserRole.objects.filter(role=obj).count()


class UserRoleSerializer(serializers.ModelSerializer):
    """用户角色序列化器"""
    user_name = serializers.CharField(source='user.username', read_only=True)
    role_name = serializers.CharField(source='role.name', read_only=True)
    assigned_by_name = serializers.CharField(source='assigned_by.username', read_only=True)
    
    class Meta:
        model = UserRole
        fields = [
            'id', 'user', 'role', 'user_name', 'role_name',
            'assigned_at', 'assigned_by', 'assigned_by_name'
        ]
        read_only_fields = ['id', 'assigned_at']


class LoginSerializer(serializers.Serializer):
    """登录序列化器"""
    username = serializers.CharField()
    password = serializers.CharField()
    device_id = serializers.CharField(required=False)
    device_name = serializers.CharField(required=False)
    device_type = serializers.ChoiceField(
        choices=['web', 'mobile', 'tablet'],
        default='web'
    )
    remember_me = serializers.BooleanField(default=False)
    
    def validate(self, attrs):
        """验证登录信息"""
        username = attrs.get('username')
        password = attrs.get('password')
        
        if username and password:
            user = authenticate(username=username, password=password)
            if not user:
                raise serializers.ValidationError('用户名或密码错误')
            if not user.is_active:
                raise serializers.ValidationError('用户账户已被禁用')
            attrs['user'] = user
        else:
            raise serializers.ValidationError('必须提供用户名和密码')
        
        return attrs


class TokenRefreshSerializer(serializers.Serializer):
    """令牌刷新序列化器"""
    refresh_token = serializers.CharField()
    device_id = serializers.CharField(required=False)


class ChangePasswordSerializer(serializers.Serializer):
    """修改密码序列化器"""
    old_password = serializers.CharField()
    new_password = serializers.CharField(validators=[validate_password])
    confirm_password = serializers.CharField()
    
    def validate(self, attrs):
        """验证密码"""
        if attrs.get('new_password') != attrs.get('confirm_password'):
            raise serializers.ValidationError("新密码不匹配")
        return attrs
    
    def validate_old_password(self, value):
        """验证旧密码"""
        user = self.context['request'].user
        if not user.check_password(value):
            raise serializers.ValidationError("旧密码错误")
        return value


class LoginLogSerializer(serializers.ModelSerializer):
    """登录日志序列化器"""
    user_name = serializers.CharField(source='user.username', read_only=True)
    duration = serializers.SerializerMethodField()
    
    class Meta:
        model = LoginLog
        fields = [
            'id', 'user', 'user_name', 'login_time', 'logout_time',
            'ip_address', 'user_agent', 'device_type', 'login_status', 'duration'
        ]
        read_only_fields = ['id', 'login_time']
    
    def get_duration(self, obj):
        """获取登录时长"""
        if obj.logout_time and obj.login_time:
            duration = obj.logout_time - obj.login_time
            return str(duration)
        return None


class UserProfileSerializer(serializers.ModelSerializer):
    """用户资料序列化器"""
    roles = serializers.SerializerMethodField()
    permissions = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = [
            'id', 'username', 'email', 'first_name', 'last_name',
            'phone', 'avatar', 'department', 'position',
            'roles', 'permissions', 'last_login', 'date_joined'
        ]
        read_only_fields = ['id', 'username', 'last_login', 'date_joined']
    
    def get_roles(self, obj):
        """获取用户角色"""
        user_roles = UserRole.objects.filter(user=obj).select_related('role')
        return [
            {
                'id': ur.role.id,
                'name': ur.role.name,
                'description': ur.role.description
            }
            for ur in user_roles
        ]
    
    def get_permissions(self, obj):
        """获取用户权限"""
        permissions = set()
        user_roles = UserRole.objects.filter(user=obj).select_related('role')
        for ur in user_roles:
            if ur.role.permissions:
                permissions.update(ur.role.permissions)
        return list(permissions)
