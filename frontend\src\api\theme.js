import { apiRequest } from './request'

export const themeApi = {
  // 获取主题列表
  getThemes: () => {
    return apiRequest.get('/themes/themes/')
  },
  
  // 获取当前主题
  getCurrentTheme: () => {
    return apiRequest.get('/themes/themes/current/')
  },
  
  // 设置主题
  setTheme: (themeId) => {
    return apiRequest.post('/themes/preference/', { theme_id: themeId })
  },
  
  // 获取主题CSS
  getThemeCSS: (themeId) => {
    return apiRequest.get(`/themes/css/?theme=${themeId}`)
  },
  
  // 获取默认主题
  getDefaultTheme: () => {
    return apiRequest.get('/themes/default/')
  }
}
