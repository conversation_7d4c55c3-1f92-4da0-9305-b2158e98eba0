# Generated by Django 5.2.4 on 2025-08-05 04:41

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('devices', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='MaintenanceType',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='维护类型名称')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='类型代码')),
                ('description', models.TextField(blank=True, null=True, verbose_name='描述')),
                ('default_duration', models.PositiveIntegerField(default=60, verbose_name='默认时长(分钟)')),
                ('default_interval_days', models.PositiveIntegerField(default=30, verbose_name='默认间隔(天)')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否激活')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '维护类型',
                'verbose_name_plural': '维护类型',
                'db_table': 'maintenance_type',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='MaintenancePlan',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=200, verbose_name='计划标题')),
                ('description', models.TextField(blank=True, null=True, verbose_name='计划描述')),
                ('start_date', models.DateField(verbose_name='开始日期')),
                ('interval_days', models.PositiveIntegerField(verbose_name='间隔天数')),
                ('estimated_duration', models.PositiveIntegerField(verbose_name='预计时长(分钟)')),
                ('next_maintenance_date', models.DateField(verbose_name='下次维护日期')),
                ('priority', models.CharField(choices=[('low', '低'), ('normal', '普通'), ('high', '高'), ('urgent', '紧急')], default='normal', max_length=20, verbose_name='优先级')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否激活')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('assigned_to', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_maintenance_plans', to=settings.AUTH_USER_MODEL, verbose_name='负责人')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_maintenance_plans', to=settings.AUTH_USER_MODEL, verbose_name='创建者')),
                ('device', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='maintenance_plans', to='devices.device', verbose_name='设备')),
                ('maintenance_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='maintenance.maintenancetype', verbose_name='维护类型')),
            ],
            options={
                'verbose_name': '维护计划',
                'verbose_name_plural': '维护计划',
                'db_table': 'maintenance_plan',
                'ordering': ['next_maintenance_date'],
            },
        ),
        migrations.CreateModel(
            name='MaintenanceRecord',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=200, verbose_name='维护标题')),
                ('description', models.TextField(verbose_name='维护描述')),
                ('scheduled_date', models.DateTimeField(verbose_name='计划时间')),
                ('start_time', models.DateTimeField(blank=True, null=True, verbose_name='开始时间')),
                ('end_time', models.DateTimeField(blank=True, null=True, verbose_name='结束时间')),
                ('actual_duration', models.PositiveIntegerField(blank=True, null=True, verbose_name='实际时长(分钟)')),
                ('status', models.CharField(choices=[('scheduled', '已计划'), ('in_progress', '进行中'), ('completed', '已完成'), ('cancelled', '已取消'), ('failed', '失败')], default='scheduled', max_length=20, verbose_name='状态')),
                ('result', models.TextField(blank=True, null=True, verbose_name='维护结果')),
                ('issues_found', models.TextField(blank=True, null=True, verbose_name='发现问题')),
                ('parts_replaced', models.JSONField(blank=True, default=list, verbose_name='更换部件')),
                ('cost', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='维护成本')),
                ('rating', models.PositiveSmallIntegerField(blank=True, choices=[(1, '很差'), (2, '差'), (3, '一般'), (4, '好'), (5, '很好')], null=True, verbose_name='维护评级')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_maintenance_records', to=settings.AUTH_USER_MODEL, verbose_name='创建者')),
                ('device', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='maintenance_records', to='devices.device', verbose_name='设备')),
                ('maintenance_plan', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='maintenance_records', to='maintenance.maintenanceplan', verbose_name='维护计划')),
                ('performed_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='performed_maintenances', to=settings.AUTH_USER_MODEL, verbose_name='执行人')),
                ('maintenance_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='maintenance.maintenancetype', verbose_name='维护类型')),
            ],
            options={
                'verbose_name': '维护记录',
                'verbose_name_plural': '维护记录',
                'db_table': 'maintenance_record',
                'ordering': ['-scheduled_date'],
            },
        ),
        migrations.CreateModel(
            name='FaultReport',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=200, verbose_name='故障标题')),
                ('description', models.TextField(verbose_name='故障描述')),
                ('fault_code', models.CharField(blank=True, max_length=50, null=True, verbose_name='故障代码')),
                ('severity', models.CharField(choices=[('low', '轻微'), ('medium', '中等'), ('high', '严重'), ('critical', '致命')], default='medium', max_length=20, verbose_name='严重程度')),
                ('fault_type', models.CharField(choices=[('mechanical', '机械故障'), ('electrical', '电气故障'), ('software', '软件故障'), ('calibration', '校准问题'), ('wear', '磨损'), ('other', '其他')], default='other', max_length=20, verbose_name='故障类型')),
                ('occurred_at', models.DateTimeField(verbose_name='发生时间')),
                ('reported_at', models.DateTimeField(auto_now_add=True, verbose_name='报告时间')),
                ('status', models.CharField(choices=[('open', '待处理'), ('assigned', '已分配'), ('in_progress', '处理中'), ('resolved', '已解决'), ('closed', '已关闭')], default='open', max_length=20, verbose_name='处理状态')),
                ('resolution', models.TextField(blank=True, null=True, verbose_name='解决方案')),
                ('resolved_at', models.DateTimeField(blank=True, null=True, verbose_name='解决时间')),
                ('assigned_to', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_faults', to=settings.AUTH_USER_MODEL, verbose_name='分配给')),
                ('device', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='fault_reports', to='devices.device', verbose_name='设备')),
                ('reported_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reported_faults', to=settings.AUTH_USER_MODEL, verbose_name='报告人')),
                ('resolved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='resolved_faults', to=settings.AUTH_USER_MODEL, verbose_name='解决人')),
                ('maintenance_record', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='related_faults', to='maintenance.maintenancerecord', verbose_name='关联维护记录')),
            ],
            options={
                'verbose_name': '故障报告',
                'verbose_name_plural': '故障报告',
                'db_table': 'fault_report',
                'ordering': ['-occurred_at'],
            },
        ),
    ]
