"""
主题模块视图
"""
from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from django.http import HttpResponse
from django.template.loader import render_to_string
from django.conf import settings

from .models import Theme, UserThemePreference
from .serializers import (
    ThemeSerializer, ThemeListSerializer, UserThemePreferenceSerializer,
    ThemeConfigSerializer, ThemePreviewSerializer
)


class ThemeViewSet(viewsets.ModelViewSet):
    """主题视图集"""
    queryset = Theme.objects.filter(is_active=True)
    serializer_class = ThemeSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'list':
            return ThemeListSerializer
        return ThemeSerializer

    def get_permissions(self):
        """根据动作设置权限"""
        if self.action in ['list', 'retrieve', 'current', 'apply']:
            permission_classes = [permissions.IsAuthenticated]
        else:
            permission_classes = [permissions.IsAdminUser]
        return [permission() for permission in permission_classes]

    @action(detail=False, methods=['get'])
    def current(self, request):
        """获取当前用户的主题"""
        try:
            preference = UserThemePreference.objects.get(user=request.user)
            serializer = UserThemePreferenceSerializer(preference)
            return Response(serializer.data)
        except UserThemePreference.DoesNotExist:
            # 返回默认主题
            default_theme = Theme.objects.filter(is_default=True, is_active=True).first()
            if default_theme:
                serializer = ThemeSerializer(default_theme)
                return Response({
                    'theme_details': serializer.data,
                    'is_default': True
                })
            return Response({'error': '未找到默认主题'}, status=status.HTTP_404_NOT_FOUND)

    @action(detail=True, methods=['post'])
    def apply(self, request, pk=None):
        """应用主题"""
        theme = self.get_object()
        custom_settings = request.data.get('custom_settings', {})

        preference, created = UserThemePreference.objects.update_or_create(
            user=request.user,
            defaults={
                'theme': theme,
                'custom_settings': custom_settings
            }
        )

        serializer = UserThemePreferenceSerializer(preference)
        return Response({
            'message': '主题应用成功',
            'preference': serializer.data
        })

    @action(detail=False, methods=['post'])
    def preview(self, request):
        """预览主题"""
        serializer = ThemePreviewSerializer(data=request.data)
        if serializer.is_valid():
            # 生成预览CSS
            css_variables = {
                '--primary-color': serializer.validated_data['primary_color'],
                '--secondary-color': serializer.validated_data['secondary_color'],
                '--success-color': serializer.validated_data['success_color'],
                '--warning-color': serializer.validated_data['warning_color'],
                '--danger-color': serializer.validated_data['danger_color'],
                '--info-color': serializer.validated_data['info_color'],
                '--background-color': serializer.validated_data['background_color'],
                '--text-color': serializer.validated_data['text_color'],
                '--border-color': serializer.validated_data['border_color'],
                '--navbar-bg-color': serializer.validated_data['navbar_bg_color'],
                '--navbar-text-color': serializer.validated_data['navbar_text_color'],
                '--sidebar-bg-color': serializer.validated_data['sidebar_bg_color'],
                '--sidebar-text-color': serializer.validated_data['sidebar_text_color'],
            }

            return Response({
                'css_variables': css_variables,
                'preview_url': '/api/themes/preview-css/',
                'message': '预览生成成功'
            })

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ThemePreferenceView(APIView):
    """用户主题偏好视图"""
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        """获取用户主题偏好"""
        try:
            preference = UserThemePreference.objects.get(user=request.user)
            serializer = UserThemePreferenceSerializer(preference)
            return Response(serializer.data)
        except UserThemePreference.DoesNotExist:
            return Response({'error': '用户未设置主题偏好'}, status=status.HTTP_404_NOT_FOUND)

    def post(self, request):
        """设置用户主题偏好"""
        serializer = ThemeConfigSerializer(data=request.data)
        if serializer.is_valid():
            theme_id = serializer.validated_data['theme_id']
            custom_settings = serializer.validated_data.get('custom_settings', {})

            try:
                theme = Theme.objects.get(id=theme_id, is_active=True)
                preference, created = UserThemePreference.objects.update_or_create(
                    user=request.user,
                    defaults={
                        'theme': theme,
                        'custom_settings': custom_settings
                    }
                )

                response_serializer = UserThemePreferenceSerializer(preference)
                return Response({
                    'message': '主题偏好设置成功',
                    'preference': response_serializer.data
                })

            except Theme.DoesNotExist:
                return Response({'error': '主题不存在'}, status=status.HTTP_404_NOT_FOUND)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ThemeCSSView(APIView):
    """主题CSS生成视图"""
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        """生成用户当前主题的CSS"""
        try:
            preference = UserThemePreference.objects.get(user=request.user)
            theme = preference.theme
        except UserThemePreference.DoesNotExist:
            # 使用默认主题
            theme = Theme.objects.filter(is_default=True, is_active=True).first()
            if not theme:
                return Response({'error': '未找到可用主题'}, status=status.HTTP_404_NOT_FOUND)

        # 生成CSS变量
        css_variables = theme.to_css_variables()

        # 生成CSS内容
        css_content = ":root {\n"
        for key, value in css_variables.items():
            css_content += f"  {key}: {value};\n"
        css_content += "}\n"

        # 添加自定义CSS
        if theme.custom_css:
            css_content += "\n" + theme.custom_css

        # 添加基础样式
        css_content += """
/* 基础样式 */
body {
    background-color: var(--background-color);
    color: var(--text-color);
}

.navbar {
    background-color: var(--navbar-bg-color) !important;
    color: var(--navbar-text-color) !important;
}

.navbar .navbar-brand,
.navbar .nav-link {
    color: var(--navbar-text-color) !important;
}

.sidebar {
    background-color: var(--sidebar-bg-color);
    color: var(--sidebar-text-color);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-secondary {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.btn-warning {
    background-color: var(--warning-color);
    border-color: var(--warning-color);
}

.btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

.btn-info {
    background-color: var(--info-color);
    border-color: var(--info-color);
}

.border {
    border-color: var(--border-color) !important;
}

.card {
    border-color: var(--border-color);
}

.table {
    color: var(--text-color);
}

.table th,
.table td {
    border-color: var(--border-color);
}
"""

        return Response({
            'css': css_content,
            'theme_name': theme.name,
            'theme_display_name': theme.display_name
        })


class DefaultThemeView(APIView):
    """默认主题视图"""
    permission_classes = [permissions.AllowAny]

    def get(self, request):
        """获取默认主题"""
        default_theme = Theme.objects.filter(is_default=True, is_active=True).first()
        if default_theme:
            serializer = ThemeSerializer(default_theme)
            return Response(serializer.data)
        return Response({'error': '未找到默认主题'}, status=status.HTTP_404_NOT_FOUND)
