{% extends 'base.html' %}

{% block title %}API文档 - 医疗设备管理系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">API文档</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary">
                <i data-feather="download" class="me-1"></i>
                导出文档
            </button>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i data-feather="info" class="me-2"></i>
                    API概述
                </h5>
            </div>
            <div class="card-body">
                <p>医疗设备管理系统提供完整的RESTful API接口，支持设备管理、维护管理、库存管理和报表分析等功能。</p>
                <ul>
                    <li><strong>基础URL:</strong> <code>http://127.0.0.1:8000/api/</code></li>
                    <li><strong>认证方式:</strong> JWT Token</li>
                    <li><strong>数据格式:</strong> JSON</li>
                    <li><strong>字符编码:</strong> UTF-8</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i data-feather="lock" class="me-2"></i>
                    认证接口
                </h5>
            </div>
            <div class="card-body">
                <div class="api-endpoint">
                    <span class="method-badge method-post">POST</span>
                    <strong>/api/auth/login/</strong>
                    <p class="mt-2 mb-1">用户登录</p>
                    <small class="text-muted">参数: username, password, device_type</small>
                </div>
                
                <div class="api-endpoint">
                    <span class="method-badge method-post">POST</span>
                    <strong>/api/auth/logout/</strong>
                    <p class="mt-2 mb-1">用户登出</p>
                    <small class="text-muted">需要认证</small>
                </div>
                
                <div class="api-endpoint">
                    <span class="method-badge method-post">POST</span>
                    <strong>/api/auth/refresh/</strong>
                    <p class="mt-2 mb-1">刷新访问令牌</p>
                    <small class="text-muted">参数: refresh_token</small>
                </div>
                
                <div class="api-endpoint">
                    <span class="method-badge method-get">GET</span>
                    <strong>/api/auth/profile/</strong>
                    <p class="mt-2 mb-1">获取用户资料</p>
                    <small class="text-muted">需要认证</small>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i data-feather="cpu" class="me-2"></i>
                    设备管理接口
                </h5>
            </div>
            <div class="card-body">
                <div class="api-endpoint">
                    <span class="method-badge method-get">GET</span>
                    <strong>/api/devices/devices/</strong>
                    <p class="mt-2 mb-1">获取设备列表</p>
                    <small class="text-muted">支持分页、搜索、过滤</small>
                </div>
                
                <div class="api-endpoint">
                    <span class="method-badge method-post">POST</span>
                    <strong>/api/devices/devices/</strong>
                    <p class="mt-2 mb-1">创建新设备</p>
                    <small class="text-muted">需要管理员权限</small>
                </div>
                
                <div class="api-endpoint">
                    <span class="method-badge method-get">GET</span>
                    <strong>/api/devices/devices/{id}/</strong>
                    <p class="mt-2 mb-1">获取设备详情</p>
                    <small class="text-muted">包含状态历史和附件</small>
                </div>
                
                <div class="api-endpoint">
                    <span class="method-badge method-post">POST</span>
                    <strong>/api/devices/devices/{id}/change_status/</strong>
                    <p class="mt-2 mb-1">更改设备状态</p>
                    <small class="text-muted">参数: status, usage_status, reason</small>
                </div>
                
                <div class="api-endpoint">
                    <span class="method-badge method-get">GET</span>
                    <strong>/api/devices/devices/dashboard/</strong>
                    <p class="mt-2 mb-1">设备仪表板数据</p>
                    <small class="text-muted">统计信息和图表数据</small>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i data-feather="tool" class="me-2"></i>
                    维护管理接口
                </h5>
            </div>
            <div class="card-body">
                <div class="api-endpoint">
                    <span class="method-badge method-get">GET</span>
                    <strong>/api/maintenance/plans/</strong>
                    <p class="mt-2 mb-1">获取维护计划列表</p>
                    <small class="text-muted">支持逾期和即将到期过滤</small>
                </div>
                
                <div class="api-endpoint">
                    <span class="method-badge method-get">GET</span>
                    <strong>/api/maintenance/records/</strong>
                    <p class="mt-2 mb-1">获取维护记录列表</p>
                    <small class="text-muted">支持状态和日期过滤</small>
                </div>
                
                <div class="api-endpoint">
                    <span class="method-badge method-get">GET</span>
                    <strong>/api/maintenance/faults/</strong>
                    <p class="mt-2 mb-1">获取故障报告列表</p>
                    <small class="text-muted">支持严重程度和状态过滤</small>
                </div>
                
                <div class="api-endpoint">
                    <span class="method-badge method-post">POST</span>
                    <strong>/api/maintenance/faults/{id}/assign/</strong>
                    <p class="mt-2 mb-1">分配故障处理人员</p>
                    <small class="text-muted">参数: assigned_to</small>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i data-feather="package" class="me-2"></i>
                    库存管理接口
                </h5>
            </div>
            <div class="card-body">
                <div class="api-endpoint">
                    <span class="method-badge method-get">GET</span>
                    <strong>/api/inventory/items/</strong>
                    <p class="mt-2 mb-1">获取物品列表</p>
                    <small class="text-muted">包含库存状态信息</small>
                </div>
                
                <div class="api-endpoint">
                    <span class="method-badge method-get">GET</span>
                    <strong>/api/inventory/items/low_stock/</strong>
                    <p class="mt-2 mb-1">获取低库存物品</p>
                    <small class="text-muted">库存不足提醒</small>
                </div>
                
                <div class="api-endpoint">
                    <span class="method-badge method-post">POST</span>
                    <strong>/api/inventory/items/{id}/adjust_stock/</strong>
                    <p class="mt-2 mb-1">调整库存</p>
                    <small class="text-muted">参数: quantity, notes</small>
                </div>
                
                <div class="api-endpoint">
                    <span class="method-badge method-get">GET</span>
                    <strong>/api/inventory/movements/</strong>
                    <p class="mt-2 mb-1">获取库存变动记录</p>
                    <small class="text-muted">支持类型和日期过滤</small>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i data-feather="bar-chart-2" class="me-2"></i>
                    报表分析接口
                </h5>
            </div>
            <div class="card-body">
                <div class="api-endpoint">
                    <span class="method-badge method-get">GET</span>
                    <strong>/api/reports/dashboard-stats/</strong>
                    <p class="mt-2 mb-1">获取仪表板统计数据</p>
                    <small class="text-muted">包含各模块统计和告警信息</small>
                </div>
                
                <div class="api-endpoint">
                    <span class="method-badge method-get">GET</span>
                    <strong>/api/reports/templates/</strong>
                    <p class="mt-2 mb-1">获取报表模板列表</p>
                    <small class="text-muted">支持类型过滤</small>
                </div>
                
                <div class="api-endpoint">
                    <span class="method-badge method-post">POST</span>
                    <strong>/api/reports/templates/{id}/generate/</strong>
                    <p class="mt-2 mb-1">生成报表</p>
                    <small class="text-muted">参数: title, parameters, format</small>
                </div>
                
                <div class="api-endpoint">
                    <span class="method-badge method-get">GET</span>
                    <strong>/api/reports/instances/</strong>
                    <p class="mt-2 mb-1">获取报表实例列表</p>
                    <small class="text-muted">已生成的报表记录</small>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i data-feather="palette" class="me-2"></i>
                    主题和图标接口
                </h5>
            </div>
            <div class="card-body">
                <div class="api-endpoint">
                    <span class="method-badge method-get">GET</span>
                    <strong>/api/themes/themes/</strong>
                    <p class="mt-2 mb-1">获取主题列表</p>
                    <small class="text-muted">可用的系统主题</small>
                </div>
                
                <div class="api-endpoint">
                    <span class="method-badge method-get">GET</span>
                    <strong>/api/themes/css/</strong>
                    <p class="mt-2 mb-1">获取当前用户主题CSS</p>
                    <small class="text-muted">动态生成的主题样式</small>
                </div>
                
                <div class="api-endpoint">
                    <span class="method-badge method-get">GET</span>
                    <strong>/api/icons/icon-sets/</strong>
                    <p class="mt-2 mb-1">获取图标集列表</p>
                    <small class="text-muted">可用的图标库</small>
                </div>
                
                <div class="api-endpoint">
                    <span class="method-badge method-post">POST</span>
                    <strong>/api/icons/icons/search/</strong>
                    <p class="mt-2 mb-1">搜索图标</p>
                    <small class="text-muted">参数: query, category, tags</small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
