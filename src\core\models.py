"""
核心基础模型
"""
import uuid
from django.db import models
from django.contrib.auth import get_user_model

User = get_user_model()


class BaseModel(models.Model):
    """基础模型类 - 所有模型的父类"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    is_active = models.BooleanField(default=True, verbose_name='是否激活')
    
    class Meta:
        abstract = True
        ordering = ['-created_at']
    
    def soft_delete(self):
        """软删除"""
        self.is_active = False
        self.save(update_fields=['is_active', 'updated_at'])


class AuditableModel(BaseModel):
    """可审计模型类 - 需要记录创建者和修改者的模型"""
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='%(class)s_created',
        verbose_name='创建者'
    )
    updated_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='%(class)s_updated',
        verbose_name='修改者'
    )
    
    class Meta:
        abstract = True
    
    def save(self, *args, **kwargs):
        """重写保存方法，自动设置修改者"""
        user = kwargs.pop('user', None)
        if user:
            if not self.pk:
                self.created_by = user
            self.updated_by = user
        super().save(*args, **kwargs)


class CodeNameModel(BaseModel):
    """编码名称模型类 - 有编码和名称的模型"""
    code = models.CharField(max_length=50, unique=True, verbose_name='编码')
    name = models.CharField(max_length=200, verbose_name='名称')
    description = models.TextField(blank=True, null=True, verbose_name='描述')
    sort_order = models.PositiveIntegerField(default=0, verbose_name='排序')
    
    class Meta:
        abstract = True
        ordering = ['sort_order', 'code']
    
    def __str__(self):
        return f"{self.code} - {self.name}"


class TreeModel(CodeNameModel):
    """树形模型类 - 支持层级结构的模型"""
    parent = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='children',
        verbose_name='父级'
    )
    level = models.PositiveIntegerField(default=0, verbose_name='层级')
    path = models.CharField(max_length=500, blank=True, verbose_name='路径')
    
    class Meta:
        abstract = True
    
    def save(self, *args, **kwargs):
        """自动计算层级和路径"""
        if self.parent:
            self.level = self.parent.level + 1
            self.path = f"{self.parent.path}/{self.code}" if self.parent.path else self.code
        else:
            self.level = 0
            self.path = self.code
        super().save(*args, **kwargs)
    
    def get_descendants(self):
        """获取所有子节点"""
        return self.__class__.objects.filter(path__startswith=f"{self.path}/")
    
    def get_ancestors(self):
        """获取所有父节点"""
        if not self.parent:
            return self.__class__.objects.none()
        
        path_parts = self.path.split('/')
        ancestor_paths = []
        for i in range(len(path_parts) - 1):
            ancestor_paths.append('/'.join(path_parts[:i+1]))
        
        return self.__class__.objects.filter(path__in=ancestor_paths)
