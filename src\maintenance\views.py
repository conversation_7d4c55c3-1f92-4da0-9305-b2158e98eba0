"""
维护模块视图
"""
from rest_framework import viewsets, permissions, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db.models import Count, Q
from django.utils import timezone
from datetime import timedelta

from .models import MaintenanceType, MaintenancePlan, MaintenanceRecord, FaultReport
from .serializers import (
    MaintenanceTypeSerializer, MaintenancePlanSerializer, MaintenanceRecordSerializer,
    FaultReportSerializer, MaintenanceListSerializer, FaultListSerializer,
    MaintenanceStatsSerializer
)


class MaintenanceTypeViewSet(viewsets.ModelViewSet):
    """维护类型视图集"""
    queryset = MaintenanceType.objects.filter(is_active=True)
    serializer_class = MaintenanceTypeSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'code', 'description']
    ordering_fields = ['name', 'created_at']
    ordering = ['name']

    def get_permissions(self):
        """根据动作设置权限"""
        if self.action in ['list', 'retrieve']:
            permission_classes = [permissions.IsAuthenticated]
        else:
            permission_classes = [permissions.IsAdminUser]
        return [permission() for permission in permission_classes]


class MaintenancePlanViewSet(viewsets.ModelViewSet):
    """维护计划视图集"""
    queryset = MaintenancePlan.objects.filter(is_active=True)
    serializer_class = MaintenancePlanSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['title', 'device__name', 'device__asset_number']
    ordering_fields = ['next_maintenance_date', 'priority', 'created_at']
    ordering = ['next_maintenance_date']

    def get_queryset(self):
        """获取查询集"""
        queryset = super().get_queryset()

        # 设备过滤
        device = self.request.query_params.get('device')
        if device:
            queryset = queryset.filter(device__id=device)

        # 维护类型过滤
        maintenance_type = self.request.query_params.get('maintenance_type')
        if maintenance_type:
            queryset = queryset.filter(maintenance_type__id=maintenance_type)

        # 优先级过滤
        priority = self.request.query_params.get('priority')
        if priority:
            queryset = queryset.filter(priority=priority)

        # 负责人过滤
        assigned_to = self.request.query_params.get('assigned_to')
        if assigned_to:
            queryset = queryset.filter(assigned_to__id=assigned_to)

        # 逾期过滤
        overdue = self.request.query_params.get('overdue')
        if overdue == 'true':
            queryset = queryset.filter(next_maintenance_date__lt=timezone.now().date())

        return queryset.select_related(
            'device', 'maintenance_type', 'assigned_to', 'created_by'
        )

    def perform_create(self, serializer):
        """创建时设置创建者"""
        serializer.save(created_by=self.request.user)

    @action(detail=False, methods=['get'])
    def overdue(self, request):
        """获取逾期的维护计划"""
        overdue_plans = self.get_queryset().filter(
            next_maintenance_date__lt=timezone.now().date()
        )
        serializer = self.get_serializer(overdue_plans, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def upcoming(self, request):
        """获取即将到期的维护计划"""
        days = int(request.query_params.get('days', 7))
        upcoming_date = timezone.now().date() + timedelta(days=days)

        upcoming_plans = self.get_queryset().filter(
            next_maintenance_date__lte=upcoming_date,
            next_maintenance_date__gte=timezone.now().date()
        )
        serializer = self.get_serializer(upcoming_plans, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def create_maintenance(self, request, pk=None):
        """从计划创建维护记录"""
        plan = self.get_object()

        # 创建维护记录
        maintenance_data = {
            'device': plan.device.id,
            'maintenance_plan': plan.id,
            'maintenance_type': plan.maintenance_type.id,
            'title': plan.title,
            'description': plan.description,
            'scheduled_date': request.data.get('scheduled_date', timezone.now()),
            'status': 'scheduled'
        }

        serializer = MaintenanceRecordSerializer(
            data=maintenance_data,
            context={'request': request}
        )

        if serializer.is_valid():
            maintenance_record = serializer.save()

            # 更新计划的下次维护日期
            plan.update_next_maintenance_date()

            return Response({
                'message': '维护记录创建成功',
                'maintenance_record': serializer.data
            })

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class MaintenanceRecordViewSet(viewsets.ModelViewSet):
    """维护记录视图集"""
    queryset = MaintenanceRecord.objects.all()
    serializer_class = MaintenanceRecordSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['title', 'device__name', 'device__asset_number']
    ordering_fields = ['scheduled_date', 'status', 'created_at']
    ordering = ['-scheduled_date']

    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'list':
            return MaintenanceListSerializer
        return MaintenanceRecordSerializer

    def get_queryset(self):
        """获取查询集"""
        queryset = super().get_queryset()

        # 设备过滤
        device = self.request.query_params.get('device')
        if device:
            queryset = queryset.filter(device__id=device)

        # 维护类型过滤
        maintenance_type = self.request.query_params.get('maintenance_type')
        if maintenance_type:
            queryset = queryset.filter(maintenance_type__id=maintenance_type)

        # 状态过滤
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # 执行人过滤
        performed_by = self.request.query_params.get('performed_by')
        if performed_by:
            queryset = queryset.filter(performed_by__id=performed_by)

        # 日期范围过滤
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        if start_date:
            queryset = queryset.filter(scheduled_date__gte=start_date)
        if end_date:
            queryset = queryset.filter(scheduled_date__lte=end_date)

        return queryset.select_related(
            'device', 'maintenance_type', 'maintenance_plan',
            'performed_by', 'created_by'
        )

    def perform_create(self, serializer):
        """创建时设置创建者"""
        serializer.save(created_by=self.request.user)

    @action(detail=True, methods=['post'])
    def start_maintenance(self, request, pk=None):
        """开始维护"""
        record = self.get_object()

        if record.status != 'scheduled':
            return Response(
                {'error': '只能开始已计划的维护'},
                status=status.HTTP_400_BAD_REQUEST
            )

        record.status = 'in_progress'
        record.start_time = timezone.now()
        record.performed_by = request.user
        record.save()

        serializer = self.get_serializer(record)
        return Response({
            'message': '维护已开始',
            'record': serializer.data
        })

    @action(detail=True, methods=['post'])
    def complete_maintenance(self, request, pk=None):
        """完成维护"""
        record = self.get_object()

        if record.status != 'in_progress':
            return Response(
                {'error': '只能完成进行中的维护'},
                status=status.HTTP_400_BAD_REQUEST
            )

        record.status = 'completed'
        record.end_time = timezone.now()
        record.result = request.data.get('result', '')
        record.issues_found = request.data.get('issues_found', '')
        record.parts_replaced = request.data.get('parts_replaced', [])
        record.cost = request.data.get('cost')
        record.rating = request.data.get('rating')
        record.save()

        serializer = self.get_serializer(record)
        return Response({
            'message': '维护已完成',
            'record': serializer.data
        })


class FaultReportViewSet(viewsets.ModelViewSet):
    """故障报告视图集"""
    queryset = FaultReport.objects.all()
    serializer_class = FaultReportSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['title', 'description', 'device__name', 'device__asset_number']
    ordering_fields = ['occurred_at', 'severity', 'status', 'reported_at']
    ordering = ['-occurred_at']

    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'list':
            return FaultListSerializer
        return FaultReportSerializer

    def get_queryset(self):
        """获取查询集"""
        queryset = super().get_queryset()

        # 设备过滤
        device = self.request.query_params.get('device')
        if device:
            queryset = queryset.filter(device__id=device)

        # 严重程度过滤
        severity = self.request.query_params.get('severity')
        if severity:
            queryset = queryset.filter(severity=severity)

        # 故障类型过滤
        fault_type = self.request.query_params.get('fault_type')
        if fault_type:
            queryset = queryset.filter(fault_type=fault_type)

        # 状态过滤
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # 分配给过滤
        assigned_to = self.request.query_params.get('assigned_to')
        if assigned_to:
            queryset = queryset.filter(assigned_to__id=assigned_to)

        return queryset.select_related(
            'device', 'reported_by', 'assigned_to', 'resolved_by', 'maintenance_record'
        )

    def perform_create(self, serializer):
        """创建时设置报告人"""
        serializer.save(reported_by=self.request.user)

    @action(detail=True, methods=['post'])
    def assign(self, request, pk=None):
        """分配故障"""
        fault = self.get_object()
        assigned_to_id = request.data.get('assigned_to')

        if not assigned_to_id:
            return Response(
                {'error': '必须指定分配给的用户'},
                status=status.HTTP_400_BAD_REQUEST
            )

        fault.assigned_to_id = assigned_to_id
        fault.status = 'assigned'
        fault.save()

        serializer = self.get_serializer(fault)
        return Response({
            'message': '故障已分配',
            'fault': serializer.data
        })

    @action(detail=True, methods=['post'])
    def resolve(self, request, pk=None):
        """解决故障"""
        fault = self.get_object()

        fault.status = 'resolved'
        fault.resolution = request.data.get('resolution', '')
        fault.resolved_at = timezone.now()
        fault.resolved_by = request.user
        fault.maintenance_record_id = request.data.get('maintenance_record')
        fault.save()

        serializer = self.get_serializer(fault)
        return Response({
            'message': '故障已解决',
            'fault': serializer.data
        })
