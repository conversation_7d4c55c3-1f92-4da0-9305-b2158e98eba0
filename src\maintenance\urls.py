"""
维护模块URL配置
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

app_name = 'maintenance'

router = DefaultRouter()
router.register(r'types', views.MaintenanceTypeViewSet)
router.register(r'plans', views.MaintenancePlanViewSet)
router.register(r'records', views.MaintenanceRecordViewSet)
router.register(r'faults', views.FaultReportViewSet)

urlpatterns = [
    # REST API 路由
    path('', include(router.urls)),
]
