"""
图标模块视图
"""
from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from django.http import HttpResponse
from django.core.cache import cache
from django.db.models import Q, Count
from django.utils import timezone

from .models import IconSet, Icon, IconUsage
from .serializers import (
    IconSetSerializer, IconSetListSerializer, IconSerializer,
    IconListSerializer, IconUsageSerializer, IconSearchSerializer,
    IconRenderSerializer, IconBatchSerializer, IconStatsSerializer
)


class IconSetViewSet(viewsets.ModelViewSet):
    """图标集视图集"""
    queryset = IconSet.objects.filter(is_active=True)
    serializer_class = IconSetSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'list':
            return IconSetListSerializer
        return IconSetSerializer

    def get_permissions(self):
        """根据动作设置权限"""
        if self.action in ['list', 'retrieve', 'icons']:
            permission_classes = [permissions.IsAuthenticated]
        else:
            permission_classes = [permissions.IsAdminUser]
        return [permission() for permission in permission_classes]

    @action(detail=True, methods=['get'])
    def icons(self, request, pk=None):
        """获取图标集中的所有图标"""
        icon_set = self.get_object()
        icons = Icon.objects.filter(icon_set=icon_set, is_active=True)

        # 分类过滤
        category = request.query_params.get('category')
        if category:
            icons = icons.filter(category=category)

        # 搜索
        search = request.query_params.get('search')
        if search:
            icons = icons.filter(
                Q(name__icontains=search) |
                Q(display_name__icontains=search) |
                Q(description__icontains=search)
            )

        serializer = IconListSerializer(icons, many=True)
        return Response({
            'icon_set': IconSetListSerializer(icon_set).data,
            'icons': serializer.data,
            'total': icons.count()
        })

    @action(detail=False, methods=['get'])
    def default(self, request):
        """获取默认图标集"""
        default_icon_set = IconSet.objects.filter(is_default=True, is_active=True).first()
        if default_icon_set:
            serializer = IconSetSerializer(default_icon_set)
            return Response(serializer.data)
        return Response({'error': '未找到默认图标集'}, status=status.HTTP_404_NOT_FOUND)


class IconViewSet(viewsets.ModelViewSet):
    """图标视图集"""
    queryset = Icon.objects.filter(is_active=True)
    serializer_class = IconSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'list':
            return IconListSerializer
        return IconSerializer

    def get_permissions(self):
        """根据动作设置权限"""
        if self.action in ['list', 'retrieve', 'search', 'render', 'use']:
            permission_classes = [permissions.IsAuthenticated]
        else:
            permission_classes = [permissions.IsAdminUser]
        return [permission() for permission in permission_classes]

    def get_queryset(self):
        """获取查询集"""
        queryset = super().get_queryset()

        # 图标集过滤
        icon_set = self.request.query_params.get('icon_set')
        if icon_set:
            queryset = queryset.filter(icon_set__name=icon_set)

        # 分类过滤
        category = self.request.query_params.get('category')
        if category:
            queryset = queryset.filter(category=category)

        return queryset

    @action(detail=False, methods=['post'])
    def search(self, request):
        """搜索图标"""
        serializer = IconSearchSerializer(data=request.data)
        if serializer.is_valid():
            queryset = Icon.objects.filter(is_active=True)

            # 关键词搜索
            query = serializer.validated_data.get('query')
            if query:
                queryset = queryset.filter(
                    Q(name__icontains=query) |
                    Q(display_name__icontains=query) |
                    Q(description__icontains=query) |
                    Q(tags__contains=[query])
                )

            # 图标集过滤
            icon_set = serializer.validated_data.get('icon_set')
            if icon_set:
                queryset = queryset.filter(icon_set__name=icon_set)

            # 分类过滤
            category = serializer.validated_data.get('category')
            if category:
                queryset = queryset.filter(category=category)

            # 标签过滤
            tags = serializer.validated_data.get('tags')
            if tags:
                for tag in tags:
                    queryset = queryset.filter(tags__contains=[tag])

            # 图标类型过滤
            icon_type = serializer.validated_data.get('icon_type')
            if icon_type:
                queryset = queryset.filter(icon_set__icon_type=icon_type)

            # 分页
            page_size = int(request.query_params.get('page_size', 20))
            page = int(request.query_params.get('page', 1))
            start = (page - 1) * page_size
            end = start + page_size

            total = queryset.count()
            icons = queryset[start:end]

            result_serializer = IconListSerializer(icons, many=True)
            return Response({
                'icons': result_serializer.data,
                'total': total,
                'page': page,
                'page_size': page_size,
                'total_pages': (total + page_size - 1) // page_size
            })

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'])
    def use(self, request, pk=None):
        """记录图标使用"""
        icon = self.get_object()
        context = request.data.get('context', 'default')

        # 更新使用记录
        usage, created = IconUsage.objects.get_or_create(
            icon=icon,
            context=context,
            defaults={'usage_count': 0}
        )
        usage.usage_count += 1
        usage.save()

        return Response({
            'message': '使用记录已更新',
            'usage_count': usage.usage_count
        })

    @action(detail=True, methods=['post'])
    def render(self, request, pk=None):
        """渲染图标"""
        icon = self.get_object()
        serializer = IconRenderSerializer(data=request.data)

        if serializer.is_valid():
            size = serializer.validated_data.get('size', icon.default_size)
            color = serializer.validated_data.get('color', icon.default_color)
            css_class = serializer.validated_data.get('css_class', '')
            style = serializer.validated_data.get('style', '')

            # 构建样式
            icon_style = f'font-size: {size}; color: {color};'
            if style:
                icon_style += f' {style}'

            # 构建CSS类
            full_class = icon.get_full_class()
            if css_class:
                full_class += f' {css_class}'

            # 根据图标类型生成HTML
            if icon.icon_set.icon_type == 'font':
                html = f'<i class="{full_class}" style="{icon_style}"></i>'
            elif icon.icon_set.icon_type == 'svg' and icon.svg_content:
                # 处理SVG内容
                svg_content = icon.svg_content
                if 'style=' in svg_content:
                    # 如果SVG已有style，则合并
                    svg_content = svg_content.replace('style="', f'style="{icon_style} ')
                else:
                    # 添加style属性
                    svg_content = svg_content.replace('<svg', f'<svg style="{icon_style}"')
                html = svg_content
            elif icon.icon_set.icon_type == 'image' and icon.image_url:
                html = f'<img src="{icon.image_url}" style="{icon_style}" class="{css_class}" alt="{icon.display_name}">'
            else:
                html = f'<span class="{full_class}" style="{icon_style}"></span>'

            # 记录使用
            context = request.data.get('context', 'render')
            usage, created = IconUsage.objects.get_or_create(
                icon=icon,
                context=context,
                defaults={'usage_count': 0}
            )
            usage.usage_count += 1
            usage.save()

            return Response({
                'html': html,
                'css_class': full_class,
                'style': icon_style,
                'icon_data': IconListSerializer(icon).data
            })

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'])
    def batch(self, request):
        """批量操作图标"""
        serializer = IconBatchSerializer(data=request.data)
        if serializer.is_valid():
            icon_ids = serializer.validated_data['icon_ids']
            action_type = serializer.validated_data['action']

            icons = Icon.objects.filter(id__in=icon_ids)

            if action_type == 'activate':
                icons.update(is_active=True)
                message = f'已激活 {icons.count()} 个图标'
            elif action_type == 'deactivate':
                icons.update(is_active=False)
                message = f'已禁用 {icons.count()} 个图标'
            elif action_type == 'delete':
                count = icons.count()
                icons.delete()
                message = f'已删除 {count} 个图标'
            elif action_type == 'update_category':
                category = serializer.validated_data['category']
                icons.update(category=category)
                message = f'已更新 {icons.count()} 个图标的分类为 {category}'

            return Response({'message': message})

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class IconStatsView(APIView):
    """图标统计视图"""
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        """获取图标统计信息"""
        # 基础统计
        total_icons = Icon.objects.filter(is_active=True).count()
        total_icon_sets = IconSet.objects.filter(is_active=True).count()

        # 最常用图标
        most_used_icons = Icon.objects.filter(
            is_active=True,
            iconusage__isnull=False
        ).annotate(
            total_usage=Count('iconusage__usage_count')
        ).order_by('-total_usage')[:10]

        # 分类统计
        categories = Icon.objects.filter(
            is_active=True,
            category__isnull=False
        ).values('category').annotate(
            count=Count('id')
        ).order_by('-count')

        categories_dict = {item['category']: item['count'] for item in categories}

        # 图标类型统计
        icon_types = IconSet.objects.filter(
            is_active=True
        ).values('icon_type').annotate(
            count=Count('icon')
        ).order_by('-count')

        icon_types_dict = {item['icon_type']: item['count'] for item in icon_types}

        serializer = IconStatsSerializer({
            'total_icons': total_icons,
            'total_icon_sets': total_icon_sets,
            'most_used_icons': most_used_icons,
            'categories': categories_dict,
            'icon_types': icon_types_dict,
        })

        return Response(serializer.data)
