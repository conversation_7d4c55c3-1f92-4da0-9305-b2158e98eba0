# Generated by Django 5.2.4 on 2025-08-05 04:11

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Theme',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=50, unique=True, verbose_name='主题名称')),
                ('display_name', models.CharField(max_length=100, verbose_name='显示名称')),
                ('description', models.TextField(blank=True, null=True, verbose_name='主题描述')),
                ('primary_color', models.Char<PERSON>ield(default='#007bff', max_length=7, verbose_name='主色调')),
                ('secondary_color', models.Char<PERSON>ield(default='#6c757d', max_length=7, verbose_name='辅助色')),
                ('success_color', models.CharField(default='#28a745', max_length=7, verbose_name='成功色')),
                ('warning_color', models.CharField(default='#ffc107', max_length=7, verbose_name='警告色')),
                ('danger_color', models.CharField(default='#dc3545', max_length=7, verbose_name='危险色')),
                ('info_color', models.CharField(default='#17a2b8', max_length=7, verbose_name='信息色')),
                ('background_color', models.CharField(default='#ffffff', max_length=7, verbose_name='背景色')),
                ('text_color', models.CharField(default='#212529', max_length=7, verbose_name='文字色')),
                ('border_color', models.CharField(default='#dee2e6', max_length=7, verbose_name='边框色')),
                ('navbar_bg_color', models.CharField(default='#343a40', max_length=7, verbose_name='导航栏背景色')),
                ('navbar_text_color', models.CharField(default='#ffffff', max_length=7, verbose_name='导航栏文字色')),
                ('sidebar_bg_color', models.CharField(default='#f8f9fa', max_length=7, verbose_name='侧边栏背景色')),
                ('sidebar_text_color', models.CharField(default='#495057', max_length=7, verbose_name='侧边栏文字色')),
                ('custom_css', models.TextField(blank=True, null=True, verbose_name='自定义CSS')),
                ('theme_type', models.CharField(choices=[('light', '浅色主题'), ('dark', '深色主题'), ('medical', '医疗主题'), ('custom', '自定义主题')], default='light', max_length=20, verbose_name='主题类型')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否激活')),
                ('is_default', models.BooleanField(default=False, verbose_name='是否默认主题')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='创建者')),
            ],
            options={
                'verbose_name': '主题',
                'verbose_name_plural': '主题',
                'db_table': 'theme',
            },
        ),
        migrations.CreateModel(
            name='UserThemePreference',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('custom_settings', models.JSONField(blank=True, default=dict, verbose_name='个人定制设置')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('theme', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='themes.theme', verbose_name='主题')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '用户主题偏好',
                'verbose_name_plural': '用户主题偏好',
                'db_table': 'user_theme_preference',
            },
        ),
    ]
