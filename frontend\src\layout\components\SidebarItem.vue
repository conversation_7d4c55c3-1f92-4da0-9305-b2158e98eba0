<template>
  <!-- 有子菜单的情况 -->
  <el-sub-menu v-if="hasChildren" :index="route.path">
    <template #title>
      <el-icon v-if="route.meta?.icon">
        <component :is="route.meta.icon" />
      </el-icon>
      <span>{{ route.meta?.title }}</span>
    </template>
    
    <template v-for="child in route.children" :key="child.path">
      <SidebarItem :route="child" />
    </template>
  </el-sub-menu>
  
  <!-- 没有子菜单的情况 -->
  <el-menu-item v-else :index="route.path">
    <el-icon v-if="route.meta?.icon">
      <component :is="route.meta.icon" />
    </el-icon>
    <template #title>
      <span>{{ route.meta?.title }}</span>
    </template>
  </el-menu-item>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  route: {
    type: Object,
    required: true
  }
})

// 是否有子菜单
const hasChildren = computed(() => {
  return props.route.children && props.route.children.length > 0
})
</script>
