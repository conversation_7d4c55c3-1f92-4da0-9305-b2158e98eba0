import { defineStore } from 'pinia'
import { ref } from 'vue'
import { themeApi } from '@/api/theme'

export const useThemeStore = defineStore('theme', () => {
  // 状态
  const currentTheme = ref('light')
  const themes = ref([])
  const isDark = ref(false)
  const loading = ref(false)

  // 初始化主题
  const initTheme = async () => {
    // 从本地存储获取主题设置
    const savedTheme = localStorage.getItem('theme') || 'light'
    const savedDarkMode = localStorage.getItem('darkMode') === 'true'
    
    currentTheme.value = savedTheme
    isDark.value = savedDarkMode
    
    // 应用主题
    applyTheme()
    
    // 加载可用主题列表
    await loadThemes()
  }

  // 加载主题列表
  const loadThemes = async () => {
    loading.value = true
    try {
      const response = await themeApi.getThemes()
      themes.value = response.data.results || []
    } catch (error) {
      console.error('Failed to load themes:', error)
      // 使用默认主题
      themes.value = [
        { id: 'light', name: '浅色主题', description: '经典浅色主题' },
        { id: 'dark', name: '深色主题', description: '护眼深色主题' },
        { id: 'medical', name: '医疗主题', description: '专业医疗主题' }
      ]
    } finally {
      loading.value = false
    }
  }

  // 切换主题
  const switchTheme = async (themeName) => {
    if (themeName === currentTheme.value) return
    
    loading.value = true
    try {
      // 调用API切换主题
      await themeApi.setTheme(themeName)
      
      currentTheme.value = themeName
      localStorage.setItem('theme', themeName)
      
      // 应用主题
      applyTheme()
      
      return true
    } catch (error) {
      console.error('Failed to switch theme:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  // 切换深色模式
  const toggleDarkMode = () => {
    isDark.value = !isDark.value
    localStorage.setItem('darkMode', isDark.value.toString())
    applyTheme()
  }

  // 应用主题
  const applyTheme = () => {
    const html = document.documentElement
    
    // 移除所有主题类
    html.classList.remove('light', 'dark', 'medical')
    
    // 应用当前主题
    html.classList.add(currentTheme.value)
    
    // 应用深色模式
    if (isDark.value) {
      html.classList.add('dark')
    } else {
      html.classList.remove('dark')
    }
    
    // 设置CSS变量
    setCSSVariables()
  }

  // 设置CSS变量
  const setCSSVariables = () => {
    const root = document.documentElement
    
    // 根据主题设置颜色变量
    const themeColors = getThemeColors(currentTheme.value)
    
    Object.entries(themeColors).forEach(([key, value]) => {
      root.style.setProperty(`--theme-${key}`, value)
    })
  }

  // 获取主题颜色
  const getThemeColors = (theme) => {
    const themes = {
      light: {
        primary: '#409EFF',
        success: '#67C23A',
        warning: '#E6A23C',
        danger: '#F56C6C',
        info: '#909399',
        background: '#ffffff',
        surface: '#f5f7fa',
        text: '#303133',
        'text-secondary': '#606266'
      },
      dark: {
        primary: '#409EFF',
        success: '#67C23A',
        warning: '#E6A23C',
        danger: '#F56C6C',
        info: '#909399',
        background: '#1d1e1f',
        surface: '#2b2b2b',
        text: '#E5EAF3',
        'text-secondary': '#CFD3DC'
      },
      medical: {
        primary: '#00A86B',
        success: '#52C41A',
        warning: '#FAAD14',
        danger: '#FF4D4F',
        info: '#1890FF',
        background: '#f0f8f0',
        surface: '#ffffff',
        text: '#2c3e50',
        'text-secondary': '#5a6c7d'
      }
    }
    
    return themes[theme] || themes.light
  }

  // 获取当前主题配置
  const getCurrentThemeConfig = () => {
    return themes.value.find(theme => theme.id === currentTheme.value) || {}
  }

  return {
    // 状态
    currentTheme,
    themes,
    isDark,
    loading,
    
    // 方法
    initTheme,
    loadThemes,
    switchTheme,
    toggleDarkMode,
    applyTheme,
    getCurrentThemeConfig
  }
})
