"""
创建测试数据的管理命令
"""
from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta, date
import random

User = get_user_model()


class Command(BaseCommand):
    help = '创建测试数据'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='清除现有测试数据',
        )
    
    def handle(self, *args, **options):
        """执行命令"""
        if options['clear']:
            self.clear_test_data()
        
        self.stdout.write('开始创建测试数据...')
        
        # 创建用户
        self.create_users()
        
        # 创建设备相关数据
        self.create_device_data()
        
        # 创建维护数据
        self.create_maintenance_data()
        
        # 创建库存数据
        self.create_inventory_data()
        
        self.stdout.write(self.style.SUCCESS('测试数据创建完成！'))
    
    def clear_test_data(self):
        """清除测试数据"""
        self.stdout.write('清除现有测试数据...')
        
        # 清除非超级用户
        User.objects.filter(is_superuser=False).delete()
        
        # 清除设备数据
        try:
            from devices.models import Device, DeviceCategory, DeviceManufacturer, DeviceModel
            Device.objects.all().delete()
            DeviceModel.objects.all().delete()
            DeviceManufacturer.objects.all().delete()
            DeviceCategory.objects.all().delete()
        except ImportError:
            pass
        
        # 清除维护数据
        try:
            from maintenance.models import MaintenanceRecord, MaintenancePlan, FaultReport, MaintenanceType
            MaintenanceRecord.objects.all().delete()
            MaintenancePlan.objects.all().delete()
            FaultReport.objects.all().delete()
            MaintenanceType.objects.all().delete()
        except ImportError:
            pass
        
        # 清除库存数据
        try:
            from inventory.models import Item, ItemCategory, Supplier, StockMovement
            StockMovement.objects.all().delete()
            Item.objects.all().delete()
            ItemCategory.objects.all().delete()
            Supplier.objects.all().delete()
        except ImportError:
            pass
        
        self.stdout.write(self.style.SUCCESS('测试数据清除完成'))
    
    def create_users(self):
        """创建测试用户"""
        self.stdout.write('创建测试用户...')
        
        users_data = [
            {'username': 'doctor1', 'email': '<EMAIL>', 'first_name': '张', 'last_name': '医生'},
            {'username': 'nurse1', 'email': '<EMAIL>', 'first_name': '李', 'last_name': '护士'},
            {'username': 'technician1', 'email': '<EMAIL>', 'first_name': '王', 'last_name': '技师'},
            {'username': 'manager1', 'email': '<EMAIL>', 'first_name': '赵', 'last_name': '主任'},
        ]
        
        for user_data in users_data:
            user, created = User.objects.get_or_create(
                username=user_data['username'],
                defaults={
                    'email': user_data['email'],
                    'first_name': user_data['first_name'],
                    'last_name': user_data['last_name'],
                    'is_active': True,
                }
            )
            if created:
                user.set_password('password123')
                user.save()
                self.stdout.write(f'✓ 创建用户: {user.username}')
    
    def create_device_data(self):
        """创建设备测试数据"""
        self.stdout.write('创建设备测试数据...')
        
        try:
            from devices.models import DeviceCategory, DeviceManufacturer, DeviceModel, Device
            
            # 创建设备分类
            categories_data = [
                {'name': '监护设备', 'code': 'MONITOR'},
                {'name': '治疗设备', 'code': 'THERAPY'},
                {'name': '检验设备', 'code': 'LAB'},
                {'name': '影像设备', 'code': 'IMAGING'},
            ]
            
            categories = {}
            for cat_data in categories_data:
                category, created = DeviceCategory.objects.get_or_create(
                    code=cat_data['code'],
                    defaults=cat_data
                )
                categories[cat_data['code']] = category
                if created:
                    self.stdout.write(f'✓ 创建设备分类: {category.name}')
            
            # 创建制造商
            manufacturers_data = [
                {'name': '飞利浦医疗', 'code': 'PHILIPS'},
                {'name': 'GE医疗', 'code': 'GE'},
                {'name': '西门子医疗', 'code': 'SIEMENS'},
                {'name': '迈瑞医疗', 'code': 'MINDRAY'},
            ]
            
            manufacturers = {}
            for manu_data in manufacturers_data:
                manufacturer, created = DeviceManufacturer.objects.get_or_create(
                    code=manu_data['code'],
                    defaults=manu_data
                )
                manufacturers[manu_data['code']] = manufacturer
                if created:
                    self.stdout.write(f'✓ 创建制造商: {manufacturer.name}')
            
            # 创建设备型号
            models_data = [
                {'name': 'IntelliVue MX40', 'model_number': 'MX40', 'manufacturer': 'PHILIPS', 'category': 'MONITOR'},
                {'name': 'CARESCAPE B650', 'model_number': 'B650', 'manufacturer': 'GE', 'category': 'MONITOR'},
                {'name': 'SC 6002XL', 'model_number': 'SC6002XL', 'manufacturer': 'SIEMENS', 'category': 'MONITOR'},
                {'name': 'BeneView T1', 'model_number': 'T1', 'manufacturer': 'MINDRAY', 'category': 'MONITOR'},
            ]
            
            device_models = {}
            for model_data in models_data:
                device_model, created = DeviceModel.objects.get_or_create(
                    model_number=model_data['model_number'],
                    manufacturer=manufacturers[model_data['manufacturer']],
                    defaults={
                        'name': model_data['name'],
                        'category': categories[model_data['category']],
                        'purchase_price': random.uniform(50000, 500000),
                    }
                )
                device_models[model_data['model_number']] = device_model
                if created:
                    self.stdout.write(f'✓ 创建设备型号: {device_model.name}')
            
            # 创建设备
            users = list(User.objects.all())
            departments = ['ICU', '急诊科', '内科', '外科', '儿科']
            statuses = ['normal', 'maintenance', 'repair', 'fault']
            
            for i in range(20):
                asset_number = f'DEV{i+1:04d}'
                device, created = Device.objects.get_or_create(
                    asset_number=asset_number,
                    defaults={
                        'name': f'设备{i+1}',
                        'device_model': random.choice(list(device_models.values())),
                        'department': random.choice(departments),
                        'location': f'{random.choice(departments)}-{random.randint(101, 999)}',
                        'responsible_person': random.choice(users),
                        'status': random.choice(statuses),
                        'purchase_date': date.today() - timedelta(days=random.randint(30, 1000)),
                        'purchase_price': random.uniform(50000, 500000),
                        'created_by': random.choice(users),
                    }
                )
                if created:
                    self.stdout.write(f'✓ 创建设备: {device.asset_number}')
        
        except ImportError:
            self.stdout.write(self.style.WARNING('设备模块未安装，跳过设备数据创建'))
    
    def create_maintenance_data(self):
        """创建维护测试数据"""
        self.stdout.write('创建维护测试数据...')
        
        try:
            from maintenance.models import MaintenanceType, MaintenancePlan, FaultReport
            from devices.models import Device
            
            # 创建维护类型
            maintenance_types_data = [
                {'name': '日常保养', 'code': 'DAILY', 'default_duration': 60, 'default_interval_days': 30},
                {'name': '月度检查', 'code': 'MONTHLY', 'default_duration': 120, 'default_interval_days': 30},
                {'name': '季度维护', 'code': 'QUARTERLY', 'default_duration': 240, 'default_interval_days': 90},
                {'name': '年度大修', 'code': 'YEARLY', 'default_duration': 480, 'default_interval_days': 365},
            ]
            
            maintenance_types = {}
            for mt_data in maintenance_types_data:
                maintenance_type, created = MaintenanceType.objects.get_or_create(
                    code=mt_data['code'],
                    defaults=mt_data
                )
                maintenance_types[mt_data['code']] = maintenance_type
                if created:
                    self.stdout.write(f'✓ 创建维护类型: {maintenance_type.name}')
            
            # 创建维护计划
            devices = list(Device.objects.all())
            users = list(User.objects.all())
            
            for device in devices[:10]:  # 为前10个设备创建维护计划
                for mt_code in ['DAILY', 'MONTHLY']:
                    maintenance_type = maintenance_types[mt_code]
                    plan, created = MaintenancePlan.objects.get_or_create(
                        device=device,
                        maintenance_type=maintenance_type,
                        defaults={
                            'title': f'{device.name} - {maintenance_type.name}',
                            'description': f'{device.name}的{maintenance_type.name}计划',
                            'start_date': date.today(),
                            'interval_days': maintenance_type.default_interval_days,
                            'estimated_duration': maintenance_type.default_duration,
                            'next_maintenance_date': date.today() + timedelta(days=random.randint(1, 30)),
                            'assigned_to': random.choice(users),
                            'priority': random.choice(['low', 'normal', 'high']),
                            'created_by': random.choice(users),
                        }
                    )
                    if created:
                        self.stdout.write(f'✓ 创建维护计划: {plan.title}')
            
            # 创建故障报告
            fault_types = ['mechanical', 'electrical', 'software', 'calibration']
            severities = ['low', 'medium', 'high', 'critical']
            statuses = ['open', 'assigned', 'in_progress', 'resolved']
            
            for i in range(15):
                device = random.choice(devices)
                fault, created = FaultReport.objects.get_or_create(
                    device=device,
                    title=f'{device.name}故障报告{i+1}',
                    defaults={
                        'description': f'{device.name}出现{random.choice(fault_types)}故障',
                        'fault_type': random.choice(fault_types),
                        'severity': random.choice(severities),
                        'status': random.choice(statuses),
                        'occurred_at': timezone.now() - timedelta(days=random.randint(1, 30)),
                        'reported_by': random.choice(users),
                        'assigned_to': random.choice(users) if random.choice([True, False]) else None,
                    }
                )
                if created:
                    self.stdout.write(f'✓ 创建故障报告: {fault.title}')
        
        except ImportError:
            self.stdout.write(self.style.WARNING('维护模块未安装，跳过维护数据创建'))
    
    def create_inventory_data(self):
        """创建库存测试数据"""
        self.stdout.write('创建库存测试数据...')
        
        try:
            from inventory.models import Supplier, ItemCategory, Item, StockMovement
            
            # 创建供应商
            suppliers_data = [
                {'name': '医疗器械供应商A', 'code': 'SUP001'},
                {'name': '医疗耗材供应商B', 'code': 'SUP002'},
                {'name': '设备配件供应商C', 'code': 'SUP003'},
            ]
            
            suppliers = {}
            for sup_data in suppliers_data:
                supplier, created = Supplier.objects.get_or_create(
                    code=sup_data['code'],
                    defaults=sup_data
                )
                suppliers[sup_data['code']] = supplier
                if created:
                    self.stdout.write(f'✓ 创建供应商: {supplier.name}')
            
            # 创建物品分类
            categories_data = [
                {'name': '医疗耗材', 'code': 'CONSUMABLE'},
                {'name': '设备配件', 'code': 'SPARE_PART'},
                {'name': '药品试剂', 'code': 'REAGENT'},
            ]
            
            categories = {}
            for cat_data in categories_data:
                category, created = ItemCategory.objects.get_or_create(
                    code=cat_data['code'],
                    defaults=cat_data
                )
                categories[cat_data['code']] = category
                if created:
                    self.stdout.write(f'✓ 创建物品分类: {category.name}')
            
            # 创建物品
            items_data = [
                {'name': '一次性注射器', 'code': 'ITEM001', 'category': 'CONSUMABLE', 'unit': '支'},
                {'name': '医用口罩', 'code': 'ITEM002', 'category': 'CONSUMABLE', 'unit': '个'},
                {'name': '心电电极片', 'code': 'ITEM003', 'category': 'CONSUMABLE', 'unit': '片'},
                {'name': '监护仪电源线', 'code': 'ITEM004', 'category': 'SPARE_PART', 'unit': '根'},
                {'name': '血氧传感器', 'code': 'ITEM005', 'category': 'SPARE_PART', 'unit': '个'},
            ]
            
            items = {}
            users = list(User.objects.all())
            
            for item_data in items_data:
                item, created = Item.objects.get_or_create(
                    code=item_data['code'],
                    defaults={
                        'name': item_data['name'],
                        'category': categories[item_data['category']],
                        'unit': item_data['unit'],
                        'standard_price': random.uniform(10, 1000),
                        'min_stock': random.randint(10, 50),
                        'max_stock': random.randint(100, 500),
                        'reorder_point': random.randint(20, 80),
                        'created_by': random.choice(users),
                    }
                )
                items[item_data['code']] = item
                if created:
                    self.stdout.write(f'✓ 创建物品: {item.name}')
            
            # 创建库存变动记录
            movement_types = ['in', 'out', 'adjustment']
            
            for item in items.values():
                for i in range(random.randint(3, 8)):
                    movement_type = random.choice(movement_types)
                    quantity = random.randint(10, 100)
                    if movement_type == 'out':
                        quantity = -quantity
                    
                    StockMovement.objects.create(
                        item=item,
                        movement_type=movement_type,
                        quantity=quantity,
                        unit_price=item.standard_price,
                        supplier=random.choice(list(suppliers.values())) if movement_type == 'in' else None,
                        notes=f'{item.name}的{movement_type}操作',
                        created_by=random.choice(users),
                    )
                    self.stdout.write(f'✓ 创建库存变动: {item.name} {movement_type} {quantity}')
        
        except ImportError:
            self.stdout.write(self.style.WARNING('库存模块未安装，跳过库存数据创建'))
