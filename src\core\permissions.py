"""
核心权限类
"""
from rest_framework import permissions
from django.contrib.auth import get_user_model

User = get_user_model()


class IsOwnerOrReadOnly(permissions.BasePermission):
    """
    只有对象的所有者才能编辑，其他人只能读取
    """
    
    def has_object_permission(self, request, view, obj):
        # 读取权限对所有人开放
        if request.method in permissions.SAFE_METHODS:
            return True
        
        # 写入权限只给对象的所有者
        return obj.created_by == request.user


class IsOwnerOrStaff(permissions.BasePermission):
    """
    只有对象的所有者或管理员才能访问
    """
    
    def has_object_permission(self, request, view, obj):
        # 管理员有所有权限
        if request.user.is_staff:
            return True
        
        # 所有者有所有权限
        return obj.created_by == request.user


class IsSuperUserOrReadOnly(permissions.BasePermission):
    """
    只有超级用户才能修改，其他人只能读取
    """
    
    def has_permission(self, request, view):
        if request.method in permissions.SAFE_METHODS:
            return request.user.is_authenticated
        
        return request.user.is_superuser


class IsStaffOrReadOnly(permissions.BasePermission):
    """
    只有管理员才能修改，其他人只能读取
    """
    
    def has_permission(self, request, view):
        if request.method in permissions.SAFE_METHODS:
            return request.user.is_authenticated
        
        return request.user.is_staff


class HasRolePermission(permissions.BasePermission):
    """
    基于角色的权限控制
    """
    required_roles = []
    
    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False
        
        # 超级用户有所有权限
        if request.user.is_superuser:
            return True
        
        # 检查用户角色
        user_roles = getattr(request.user, 'roles', [])
        if hasattr(user_roles, 'values_list'):
            user_roles = user_roles.values_list('code', flat=True)
        
        return any(role in user_roles for role in self.required_roles)


class DoctorPermission(HasRolePermission):
    """医生权限"""
    required_roles = ['doctor', 'chief_doctor']


class NursePermission(HasRolePermission):
    """护士权限"""
    required_roles = ['nurse', 'head_nurse']


class TechnicianPermission(HasRolePermission):
    """技师权限"""
    required_roles = ['technician', 'senior_technician']


class AdminPermission(HasRolePermission):
    """管理员权限"""
    required_roles = ['admin', 'super_admin']


class DepartmentPermission(permissions.BasePermission):
    """
    部门权限 - 只能访问本部门的数据
    """
    
    def has_object_permission(self, request, view, obj):
        # 超级用户和管理员有所有权限
        if request.user.is_superuser or request.user.is_staff:
            return True
        
        # 检查是否属于同一部门
        user_department = getattr(request.user, 'department', None)
        obj_department = getattr(obj, 'department', None)
        
        if hasattr(obj, 'created_by'):
            obj_department = getattr(obj.created_by, 'department', None)
        
        return user_department and user_department == obj_department


class TimeBasedPermission(permissions.BasePermission):
    """
    基于时间的权限控制
    """
    
    def has_permission(self, request, view):
        from datetime import time
        from django.utils import timezone
        
        # 工作时间：8:00-18:00
        current_time = timezone.now().time()
        work_start = time(8, 0)
        work_end = time(18, 0)
        
        # 管理员不受时间限制
        if request.user.is_staff:
            return True
        
        # 只读操作不受时间限制
        if request.method in permissions.SAFE_METHODS:
            return True
        
        # 写操作需要在工作时间内
        return work_start <= current_time <= work_end


class IPWhitelistPermission(permissions.BasePermission):
    """
    IP白名单权限
    """
    allowed_ips = [
        '127.0.0.1',
        '***********/24',  # 内网IP段
    ]
    
    def has_permission(self, request, view):
        from ipaddress import ip_address, ip_network
        
        # 获取客户端IP
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            client_ip = x_forwarded_for.split(',')[0].strip()
        else:
            client_ip = request.META.get('REMOTE_ADDR')
        
        # 检查IP是否在白名单中
        try:
            client_ip_obj = ip_address(client_ip)
            for allowed_ip in self.allowed_ips:
                if '/' in allowed_ip:
                    # IP段
                    if client_ip_obj in ip_network(allowed_ip, strict=False):
                        return True
                else:
                    # 单个IP
                    if str(client_ip_obj) == allowed_ip:
                        return True
        except ValueError:
            pass
        
        return False


class RateLimitPermission(permissions.BasePermission):
    """
    频率限制权限
    """
    
    def has_permission(self, request, view):
        from django.core.cache import cache
        from django.conf import settings
        
        # 获取用户标识
        if request.user.is_authenticated:
            user_id = str(request.user.id)
        else:
            # 使用IP作为标识
            user_id = request.META.get('REMOTE_ADDR', 'unknown')
        
        # 生成缓存键
        cache_key = f"rate_limit:{user_id}:{view.__class__.__name__}"
        
        # 获取当前请求次数
        current_requests = cache.get(cache_key, 0)
        
        # 设置限制（每分钟最多60次请求）
        max_requests = getattr(settings, 'API_RATE_LIMIT', 60)
        
        if current_requests >= max_requests:
            return False
        
        # 增加请求次数
        cache.set(cache_key, current_requests + 1, 60)  # 60秒过期
        
        return True
