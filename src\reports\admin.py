from django.contrib import admin
from django.utils.html import format_html
from .models import ReportTemplate, ReportInstance, Dashboard


@admin.register(ReportTemplate)
class ReportTemplateAdmin(admin.ModelAdmin):
    """报表模板管理"""
    list_display = [
        'name', 'report_type', 'is_public', 'is_active',
        'instance_count', 'created_by', 'created_at'
    ]
    list_filter = ['report_type', 'is_public', 'is_active', 'created_at']
    search_fields = ['name', 'description']
    ordering = ['-created_at']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'description', 'report_type')
        }),
        ('配置信息', {
            'fields': ('config', 'filters', 'fields', 'ordering')
        }),
        ('权限设置', {
            'fields': ('is_active', 'is_public')
        }),
        ('系统信息', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def instance_count(self, obj):
        """实例数量"""
        return obj.instances.count()
    instance_count.short_description = '实例数量'

    def save_model(self, request, obj, form, change):
        """保存模型时设置创建者"""
        if not change:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(ReportInstance)
class ReportInstanceAdmin(admin.ModelAdmin):
    """报表实例管理"""
    list_display = [
        'title', 'template', 'status_badge', 'total_records',
        'generated_by', 'generated_at', 'completed_at'
    ]
    list_filter = ['status', 'template__report_type', 'generated_at']
    search_fields = ['title', 'template__name']
    ordering = ['-generated_at']
    readonly_fields = [
        'data', 'total_records', 'status', 'error_message',
        'file_path', 'file_size', 'generated_at', 'completed_at'
    ]

    fieldsets = (
        ('基本信息', {
            'fields': ('template', 'title', 'description')
        }),
        ('生成参数', {
            'fields': ('parameters',)
        }),
        ('生成结果', {
            'fields': (
                'status', 'total_records', 'data', 'error_message',
                'file_path', 'file_size'
            )
        }),
        ('时间信息', {
            'fields': ('generated_by', 'generated_at', 'completed_at')
        }),
    )

    def status_badge(self, obj):
        """状态徽章"""
        colors = {
            'generating': '#17a2b8',
            'completed': '#28a745',
            'failed': '#dc3545',
        }
        color = colors.get(obj.status, '#6c757d')
        return format_html(
            '<span style="background-color: {}; color: white; padding: 2px 8px; '
            'border-radius: 12px; font-size: 12px;">{}</span>',
            color,
            obj.get_status_display()
        )
    status_badge.short_description = '状态'

    def save_model(self, request, obj, form, change):
        """保存模型时设置生成者"""
        if not change:
            obj.generated_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(Dashboard)
class DashboardAdmin(admin.ModelAdmin):
    """仪表板管理"""
    list_display = [
        'name', 'is_public', 'auto_refresh', 'widget_count',
        'created_by', 'created_at'
    ]
    list_filter = ['is_public', 'auto_refresh', 'is_active', 'created_at']
    search_fields = ['name', 'description']
    ordering = ['-created_at']
    readonly_fields = ['created_at', 'updated_at']
    filter_horizontal = ['allowed_users']

    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'description')
        }),
        ('布局配置', {
            'fields': ('layout', 'widgets')
        }),
        ('刷新配置', {
            'fields': ('auto_refresh', 'refresh_interval')
        }),
        ('权限设置', {
            'fields': ('is_public', 'allowed_users', 'is_active')
        }),
        ('系统信息', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def widget_count(self, obj):
        """组件数量"""
        return len(obj.widgets) if obj.widgets else 0
    widget_count.short_description = '组件数量'

    def save_model(self, request, obj, form, change):
        """保存模型时设置创建者"""
        if not change:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)
