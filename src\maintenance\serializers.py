"""
维护模块序列化器
"""
from rest_framework import serializers
from django.utils import timezone
from .models import MaintenanceType, MaintenancePlan, MaintenanceRecord, FaultReport


class MaintenanceTypeSerializer(serializers.ModelSerializer):
    """维护类型序列化器"""
    plan_count = serializers.SerializerMethodField()
    
    class Meta:
        model = MaintenanceType
        fields = [
            'id', 'name', 'code', 'description', 'default_duration',
            'default_interval_days', 'is_active', 'plan_count',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_plan_count(self, obj):
        """获取计划数量"""
        return obj.maintenanceplan_set.filter(is_active=True).count()


class MaintenancePlanSerializer(serializers.ModelSerializer):
    """维护计划序列化器"""
    device_name = serializers.CharField(source='device.name', read_only=True)
    device_asset_number = serializers.CharField(source='device.asset_number', read_only=True)
    maintenance_type_name = serializers.CharField(source='maintenance_type.name', read_only=True)
    assigned_to_name = serializers.CharField(source='assigned_to.username', read_only=True)
    created_by_name = serializers.CharField(source='created_by.username', read_only=True)
    
    # 计算字段
    is_overdue = serializers.ReadOnlyField()
    days_until_maintenance = serializers.ReadOnlyField()
    
    class Meta:
        model = MaintenancePlan
        fields = [
            'id', 'device', 'device_name', 'device_asset_number',
            'maintenance_type', 'maintenance_type_name', 'title', 'description',
            'start_date', 'interval_days', 'estimated_duration',
            'next_maintenance_date', 'assigned_to', 'assigned_to_name',
            'priority', 'is_active', 'is_overdue', 'days_until_maintenance',
            'created_at', 'updated_at', 'created_by', 'created_by_name'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'created_by']
    
    def create(self, validated_data):
        """创建维护计划"""
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)


class MaintenanceRecordSerializer(serializers.ModelSerializer):
    """维护记录序列化器"""
    device_name = serializers.CharField(source='device.name', read_only=True)
    device_asset_number = serializers.CharField(source='device.asset_number', read_only=True)
    maintenance_type_name = serializers.CharField(source='maintenance_type.name', read_only=True)
    maintenance_plan_title = serializers.CharField(source='maintenance_plan.title', read_only=True)
    performed_by_name = serializers.CharField(source='performed_by.username', read_only=True)
    created_by_name = serializers.CharField(source='created_by.username', read_only=True)
    
    # 计算字段
    duration_display = serializers.ReadOnlyField()
    
    class Meta:
        model = MaintenanceRecord
        fields = [
            'id', 'device', 'device_name', 'device_asset_number',
            'maintenance_plan', 'maintenance_plan_title',
            'maintenance_type', 'maintenance_type_name', 'title', 'description',
            'scheduled_date', 'start_time', 'end_time', 'actual_duration',
            'duration_display', 'performed_by', 'performed_by_name',
            'status', 'result', 'issues_found', 'parts_replaced',
            'cost', 'rating', 'created_at', 'updated_at',
            'created_by', 'created_by_name'
        ]
        read_only_fields = ['id', 'actual_duration', 'created_at', 'updated_at', 'created_by']
    
    def create(self, validated_data):
        """创建维护记录"""
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)


class FaultReportSerializer(serializers.ModelSerializer):
    """故障报告序列化器"""
    device_name = serializers.CharField(source='device.name', read_only=True)
    device_asset_number = serializers.CharField(source='device.asset_number', read_only=True)
    reported_by_name = serializers.CharField(source='reported_by.username', read_only=True)
    assigned_to_name = serializers.CharField(source='assigned_to.username', read_only=True)
    resolved_by_name = serializers.CharField(source='resolved_by.username', read_only=True)
    maintenance_record_title = serializers.CharField(source='maintenance_record.title', read_only=True)
    
    # 计算字段
    resolution_time_display = serializers.SerializerMethodField()
    
    class Meta:
        model = FaultReport
        fields = [
            'id', 'device', 'device_name', 'device_asset_number',
            'title', 'description', 'fault_code', 'severity', 'fault_type',
            'occurred_at', 'reported_at', 'reported_by', 'reported_by_name',
            'status', 'assigned_to', 'assigned_to_name', 'resolution',
            'resolved_at', 'resolved_by', 'resolved_by_name',
            'maintenance_record', 'maintenance_record_title',
            'resolution_time_display'
        ]
        read_only_fields = ['id', 'reported_at', 'reported_by']
    
    def get_resolution_time_display(self, obj):
        """获取解决时长显示"""
        resolution_time = obj.resolution_time
        if resolution_time:
            days = resolution_time.days
            hours = resolution_time.seconds // 3600
            if days > 0:
                return f"{days}天{hours}小时"
            return f"{hours}小时"
        return "未解决"
    
    def create(self, validated_data):
        """创建故障报告"""
        validated_data['reported_by'] = self.context['request'].user
        return super().create(validated_data)


class MaintenanceListSerializer(serializers.ModelSerializer):
    """维护记录列表序列化器"""
    device_name = serializers.CharField(source='device.name', read_only=True)
    device_asset_number = serializers.CharField(source='device.asset_number', read_only=True)
    maintenance_type_name = serializers.CharField(source='maintenance_type.name', read_only=True)
    performed_by_name = serializers.CharField(source='performed_by.username', read_only=True)
    duration_display = serializers.ReadOnlyField()
    
    class Meta:
        model = MaintenanceRecord
        fields = [
            'id', 'device_asset_number', 'device_name', 'maintenance_type_name',
            'title', 'scheduled_date', 'status', 'performed_by_name',
            'duration_display', 'rating'
        ]


class FaultListSerializer(serializers.ModelSerializer):
    """故障报告列表序列化器"""
    device_name = serializers.CharField(source='device.name', read_only=True)
    device_asset_number = serializers.CharField(source='device.asset_number', read_only=True)
    reported_by_name = serializers.CharField(source='reported_by.username', read_only=True)
    assigned_to_name = serializers.CharField(source='assigned_to.username', read_only=True)
    
    class Meta:
        model = FaultReport
        fields = [
            'id', 'device_asset_number', 'device_name', 'title',
            'severity', 'fault_type', 'status', 'occurred_at',
            'reported_by_name', 'assigned_to_name'
        ]


class MaintenanceStatsSerializer(serializers.Serializer):
    """维护统计序列化器"""
    total_plans = serializers.IntegerField()
    overdue_plans = serializers.IntegerField()
    upcoming_plans = serializers.IntegerField()
    total_records = serializers.IntegerField()
    completed_records = serializers.IntegerField()
    total_faults = serializers.IntegerField()
    open_faults = serializers.IntegerField()
    resolved_faults = serializers.IntegerField()
    maintenance_by_type = serializers.DictField()
    fault_by_severity = serializers.DictField()
    recent_maintenances = MaintenanceListSerializer(many=True)
    recent_faults = FaultListSerializer(many=True)
