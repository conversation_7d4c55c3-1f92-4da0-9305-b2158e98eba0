"""
报表模块序列化器
"""
from rest_framework import serializers
from .models import ReportTemplate, ReportInstance, Dashboard


class ReportTemplateSerializer(serializers.ModelSerializer):
    """报表模板序列化器"""
    created_by_name = serializers.CharField(source='created_by.username', read_only=True)
    instance_count = serializers.SerializerMethodField()
    
    class Meta:
        model = ReportTemplate
        fields = [
            'id', 'name', 'description', 'report_type', 'config',
            'filters', 'fields', 'ordering', 'is_active', 'is_public',
            'created_by', 'created_by_name', 'instance_count',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_by', 'created_at', 'updated_at']
    
    def get_instance_count(self, obj):
        """获取实例数量"""
        return obj.instances.count()
    
    def create(self, validated_data):
        """创建报表模板"""
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)


class ReportTemplateListSerializer(serializers.ModelSerializer):
    """报表模板列表序列化器"""
    created_by_name = serializers.CharField(source='created_by.username', read_only=True)
    instance_count = serializers.SerializerMethodField()
    
    class Meta:
        model = ReportTemplate
        fields = [
            'id', 'name', 'description', 'report_type', 'is_active',
            'is_public', 'created_by_name', 'instance_count', 'created_at'
        ]
    
    def get_instance_count(self, obj):
        """获取实例数量"""
        return obj.instances.count()


class ReportInstanceSerializer(serializers.ModelSerializer):
    """报表实例序列化器"""
    template_name = serializers.CharField(source='template.name', read_only=True)
    template_type = serializers.CharField(source='template.report_type', read_only=True)
    generated_by_name = serializers.CharField(source='generated_by.username', read_only=True)
    duration = serializers.SerializerMethodField()
    
    class Meta:
        model = ReportInstance
        fields = [
            'id', 'template', 'template_name', 'template_type', 'title',
            'description', 'parameters', 'data', 'total_records',
            'status', 'error_message', 'file_path', 'file_size',
            'generated_by', 'generated_by_name', 'generated_at',
            'completed_at', 'duration'
        ]
        read_only_fields = [
            'id', 'data', 'total_records', 'status', 'error_message',
            'file_path', 'file_size', 'generated_by', 'generated_at', 'completed_at'
        ]
    
    def get_duration(self, obj):
        """获取生成时长"""
        if obj.completed_at and obj.generated_at:
            duration = obj.completed_at - obj.generated_at
            return str(duration)
        return None
    
    def create(self, validated_data):
        """创建报表实例"""
        validated_data['generated_by'] = self.context['request'].user
        return super().create(validated_data)


class ReportInstanceListSerializer(serializers.ModelSerializer):
    """报表实例列表序列化器"""
    template_name = serializers.CharField(source='template.name', read_only=True)
    template_type = serializers.CharField(source='template.report_type', read_only=True)
    generated_by_name = serializers.CharField(source='generated_by.username', read_only=True)
    
    class Meta:
        model = ReportInstance
        fields = [
            'id', 'template_name', 'template_type', 'title',
            'status', 'total_records', 'generated_by_name',
            'generated_at', 'completed_at'
        ]


class DashboardSerializer(serializers.ModelSerializer):
    """仪表板序列化器"""
    created_by_name = serializers.CharField(source='created_by.username', read_only=True)
    allowed_users_names = serializers.SerializerMethodField()
    
    class Meta:
        model = Dashboard
        fields = [
            'id', 'name', 'description', 'layout', 'widgets',
            'auto_refresh', 'refresh_interval', 'is_public',
            'allowed_users', 'allowed_users_names', 'is_active',
            'created_by', 'created_by_name', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_by', 'created_at', 'updated_at']
    
    def get_allowed_users_names(self, obj):
        """获取允许访问的用户名"""
        return [user.username for user in obj.allowed_users.all()]
    
    def create(self, validated_data):
        """创建仪表板"""
        allowed_users = validated_data.pop('allowed_users', [])
        validated_data['created_by'] = self.context['request'].user
        dashboard = super().create(validated_data)
        dashboard.allowed_users.set(allowed_users)
        return dashboard
    
    def update(self, instance, validated_data):
        """更新仪表板"""
        allowed_users = validated_data.pop('allowed_users', None)
        dashboard = super().update(instance, validated_data)
        if allowed_users is not None:
            dashboard.allowed_users.set(allowed_users)
        return dashboard


class DashboardListSerializer(serializers.ModelSerializer):
    """仪表板列表序列化器"""
    created_by_name = serializers.CharField(source='created_by.username', read_only=True)
    widget_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Dashboard
        fields = [
            'id', 'name', 'description', 'is_public', 'auto_refresh',
            'widget_count', 'created_by_name', 'created_at'
        ]
    
    def get_widget_count(self, obj):
        """获取组件数量"""
        return len(obj.widgets) if obj.widgets else 0


class ReportGenerateSerializer(serializers.Serializer):
    """报表生成序列化器"""
    template_id = serializers.UUIDField()
    title = serializers.CharField(max_length=200, required=False)
    description = serializers.CharField(required=False)
    parameters = serializers.JSONField(default=dict)
    format = serializers.ChoiceField(
        choices=['json', 'excel', 'pdf', 'csv'],
        default='json'
    )
    
    def validate_template_id(self, value):
        """验证模板ID"""
        try:
            template = ReportTemplate.objects.get(id=value, is_active=True)
            return value
        except ReportTemplate.DoesNotExist:
            raise serializers.ValidationError("报表模板不存在或已被禁用")


class DashboardStatsSerializer(serializers.Serializer):
    """仪表板统计序列化器"""
    device_stats = serializers.DictField()
    maintenance_stats = serializers.DictField()
    inventory_stats = serializers.DictField()
    fault_stats = serializers.DictField()
    recent_activities = serializers.ListField()
    alerts = serializers.ListField()


class ChartDataSerializer(serializers.Serializer):
    """图表数据序列化器"""
    chart_type = serializers.ChoiceField(
        choices=['line', 'bar', 'pie', 'doughnut', 'area']
    )
    title = serializers.CharField()
    labels = serializers.ListField()
    datasets = serializers.ListField()
    options = serializers.DictField(default=dict)
