"""
创建默认主题的管理命令
"""
from django.core.management.base import BaseCommand
from themes.models import Theme


class Command(BaseCommand):
    help = '创建默认主题'
    
    def handle(self, *args, **options):
        """执行命令"""
        self.stdout.write('开始创建默认主题...')
        
        # 浅色主题（默认）
        light_theme, created = Theme.objects.get_or_create(
            name='light',
            defaults={
                'display_name': '浅色主题',
                'description': '清新明亮的浅色主题，适合日常使用',
                'theme_type': 'light',
                'primary_color': '#007bff',
                'secondary_color': '#6c757d',
                'success_color': '#28a745',
                'warning_color': '#ffc107',
                'danger_color': '#dc3545',
                'info_color': '#17a2b8',
                'background_color': '#ffffff',
                'text_color': '#212529',
                'border_color': '#dee2e6',
                'navbar_bg_color': '#f8f9fa',
                'navbar_text_color': '#495057',
                'sidebar_bg_color': '#f8f9fa',
                'sidebar_text_color': '#495057',
                'is_active': True,
                'is_default': True,
            }
        )
        if created:
            self.stdout.write(self.style.SUCCESS('✓ 浅色主题创建成功'))
        else:
            self.stdout.write('浅色主题已存在')
        
        # 深色主题
        dark_theme, created = Theme.objects.get_or_create(
            name='dark',
            defaults={
                'display_name': '深色主题',
                'description': '护眼的深色主题，适合夜间使用',
                'theme_type': 'dark',
                'primary_color': '#0d6efd',
                'secondary_color': '#6c757d',
                'success_color': '#198754',
                'warning_color': '#ffc107',
                'danger_color': '#dc3545',
                'info_color': '#0dcaf0',
                'background_color': '#212529',
                'text_color': '#ffffff',
                'border_color': '#495057',
                'navbar_bg_color': '#343a40',
                'navbar_text_color': '#ffffff',
                'sidebar_bg_color': '#343a40',
                'sidebar_text_color': '#ffffff',
                'is_active': True,
                'is_default': False,
            }
        )
        if created:
            self.stdout.write(self.style.SUCCESS('✓ 深色主题创建成功'))
        else:
            self.stdout.write('深色主题已存在')
        
        # 医疗主题
        medical_theme, created = Theme.objects.get_or_create(
            name='medical',
            defaults={
                'display_name': '医疗主题',
                'description': '专业的医疗行业主题，使用医疗相关配色',
                'theme_type': 'medical',
                'primary_color': '#0066cc',
                'secondary_color': '#6c757d',
                'success_color': '#00a651',
                'warning_color': '#ff9500',
                'danger_color': '#e74c3c',
                'info_color': '#3498db',
                'background_color': '#f8fffe',
                'text_color': '#2c3e50',
                'border_color': '#bdc3c7',
                'navbar_bg_color': '#2c3e50',
                'navbar_text_color': '#ffffff',
                'sidebar_bg_color': '#ecf0f1',
                'sidebar_text_color': '#2c3e50',
                'is_active': True,
                'is_default': False,
                'custom_css': '''
/* 医疗主题自定义样式 */
.medical-card {
    border-left: 4px solid var(--primary-color);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.medical-badge {
    background: linear-gradient(45deg, var(--primary-color), var(--info-color));
    color: white;
    border-radius: 20px;
    padding: 5px 15px;
}

.medical-button {
    background: linear-gradient(45deg, var(--primary-color), #0080ff);
    border: none;
    border-radius: 25px;
    color: white;
    padding: 10px 25px;
    transition: all 0.3s ease;
}

.medical-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,102,204,0.3);
}
                '''
            }
        )
        if created:
            self.stdout.write(self.style.SUCCESS('✓ 医疗主题创建成功'))
        else:
            self.stdout.write('医疗主题已存在')
        
        self.stdout.write(self.style.SUCCESS('默认主题创建完成！'))
