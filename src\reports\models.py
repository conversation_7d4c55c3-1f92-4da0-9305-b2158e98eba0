from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
import uuid

User = get_user_model()


class ReportTemplate(models.Model):
    """报表模板模型"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=200, verbose_name='模板名称')
    description = models.TextField(blank=True, null=True, verbose_name='模板描述')

    # 报表类型
    REPORT_TYPES = [
        ('device_usage', '设备使用报表'),
        ('maintenance_summary', '维护汇总报表'),
        ('inventory_status', '库存状态报表'),
        ('cost_analysis', '成本分析报表'),
        ('fault_analysis', '故障分析报表'),
        ('custom', '自定义报表'),
    ]
    report_type = models.CharField(
        max_length=50,
        choices=REPORT_TYPES,
        verbose_name='报表类型'
    )

    # 报表配置
    config = models.JSONField(default=dict, verbose_name='报表配置')

    # 查询条件
    filters = models.JSONField(default=dict, verbose_name='过滤条件')

    # 字段配置
    fields = models.JSONField(default=list, verbose_name='字段配置')

    # 排序配置
    ordering = models.JSONField(default=list, verbose_name='排序配置')

    # 状态
    is_active = models.BooleanField(default=True, verbose_name='是否激活')
    is_public = models.BooleanField(default=False, verbose_name='是否公开')

    # 创建信息
    created_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        verbose_name='创建者'
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '报表模板'
        verbose_name_plural = '报表模板'
        db_table = 'report_template'
        ordering = ['-created_at']

    def __str__(self):
        return self.name


class ReportInstance(models.Model):
    """报表实例模型"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    template = models.ForeignKey(
        ReportTemplate,
        on_delete=models.CASCADE,
        related_name='instances',
        verbose_name='报表模板'
    )

    # 报表信息
    title = models.CharField(max_length=200, verbose_name='报表标题')
    description = models.TextField(blank=True, null=True, verbose_name='报表描述')

    # 生成参数
    parameters = models.JSONField(default=dict, verbose_name='生成参数')

    # 报表数据
    data = models.JSONField(default=dict, verbose_name='报表数据')

    # 统计信息
    total_records = models.PositiveIntegerField(default=0, verbose_name='总记录数')

    # 状态
    STATUS_CHOICES = [
        ('generating', '生成中'),
        ('completed', '已完成'),
        ('failed', '生成失败'),
    ]
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='generating',
        verbose_name='状态'
    )

    # 错误信息
    error_message = models.TextField(blank=True, null=True, verbose_name='错误信息')

    # 文件信息
    file_path = models.CharField(max_length=500, blank=True, null=True, verbose_name='文件路径')
    file_size = models.PositiveIntegerField(null=True, blank=True, verbose_name='文件大小')

    # 生成信息
    generated_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        verbose_name='生成者'
    )
    generated_at = models.DateTimeField(auto_now_add=True, verbose_name='生成时间')
    completed_at = models.DateTimeField(null=True, blank=True, verbose_name='完成时间')

    class Meta:
        verbose_name = '报表实例'
        verbose_name_plural = '报表实例'
        db_table = 'report_instance'
        ordering = ['-generated_at']

    def __str__(self):
        return f"{self.template.name} - {self.generated_at.strftime('%Y-%m-%d %H:%M')}"

    def mark_completed(self):
        """标记为完成"""
        self.status = 'completed'
        self.completed_at = timezone.now()
        self.save(update_fields=['status', 'completed_at'])

    def mark_failed(self, error_message):
        """标记为失败"""
        self.status = 'failed'
        self.error_message = error_message
        self.completed_at = timezone.now()
        self.save(update_fields=['status', 'error_message', 'completed_at'])


class Dashboard(models.Model):
    """仪表板模型"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=200, verbose_name='仪表板名称')
    description = models.TextField(blank=True, null=True, verbose_name='描述')

    # 布局配置
    layout = models.JSONField(default=dict, verbose_name='布局配置')

    # 组件配置
    widgets = models.JSONField(default=list, verbose_name='组件配置')

    # 刷新配置
    auto_refresh = models.BooleanField(default=False, verbose_name='自动刷新')
    refresh_interval = models.PositiveIntegerField(default=300, verbose_name='刷新间隔(秒)')

    # 权限配置
    is_public = models.BooleanField(default=False, verbose_name='是否公开')
    allowed_users = models.ManyToManyField(
        User,
        blank=True,
        related_name='allowed_dashboards',
        verbose_name='允许访问的用户'
    )

    # 状态
    is_active = models.BooleanField(default=True, verbose_name='是否激活')

    # 创建信息
    created_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        verbose_name='创建者'
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '仪表板'
        verbose_name_plural = '仪表板'
        db_table = 'dashboard'
        ordering = ['-created_at']

    def __str__(self):
        return self.name
