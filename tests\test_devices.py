"""
设备模块测试
"""
import uuid
from decimal import Decimal
from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase
import factory
from factory.django import DjangoModelFactory

from devices.models import (
    DeviceCategory, DeviceManufacturer, DeviceModel, 
    Device, DeviceLocation
)
from .base import (
    BaseAPITestCase, UserFactory, TestDataMixin, 
    PerformanceTestMixin, SecurityTestMixin
)


class DeviceCategoryFactory(DjangoModelFactory):
    """设备分类工厂"""
    
    class Meta:
        model = DeviceCategory
    
    name = factory.Faker('word', locale='zh_CN')
    code = factory.Sequence(lambda n: f"CAT{n:03d}")
    description = factory.Faker('text', max_nb_chars=200, locale='zh_CN')
    is_active = True


class DeviceManufacturerFactory(DjangoModelFactory):
    """设备制造商工厂"""

    class Meta:
        model = DeviceManufacturer

    name = factory.Faker('company', locale='zh_CN')
    code = factory.Sequence(lambda n: f"MFG{n:03d}")
    description = factory.Faker('text', max_nb_chars=200, locale='zh_CN')
    contact_person = factory.Faker('name', locale='zh_CN')
    phone = factory.Faker('phone_number', locale='zh_CN')
    email = factory.Faker('email')
    is_active = True


class DeviceLocationFactory(DjangoModelFactory):
    """设备位置工厂"""
    
    class Meta:
        model = DeviceLocation
    
    name = factory.Faker('word', locale='zh_CN')
    code = factory.Sequence(lambda n: f"LOC{n:03d}")
    description = factory.Faker('text', max_nb_chars=200, locale='zh_CN')
    location_type = 'room'


class DeviceModelFactory(DjangoModelFactory):
    """设备型号工厂"""
    
    class Meta:
        model = DeviceModel
    
    name = factory.Faker('word', locale='zh_CN')
    model_number = factory.Sequence(lambda n: f"MODEL{n:03d}")
    category = factory.SubFactory(DeviceCategoryFactory)
    manufacturer = factory.SubFactory(DeviceManufacturerFactory)
    specifications = factory.LazyFunction(lambda: {
        'power': '220V',
        'weight': '10kg',
        'dimensions': '100x50x30cm'
    })
    purchase_price = factory.Faker('pydecimal', left_digits=6, right_digits=2, positive=True)
    is_active = True


class DeviceFactory(DjangoModelFactory):
    """设备工厂"""
    
    class Meta:
        model = Device
    
    asset_number = factory.Sequence(lambda n: f"ASSET{n:06d}")
    serial_number = factory.Sequence(lambda n: f"SN{n:08d}")
    name = factory.Faker('word', locale='zh_CN')
    device_model = factory.SubFactory(DeviceModelFactory)
    location = factory.SubFactory(DeviceLocationFactory)
    status = 'normal'
    purchase_date = factory.Faker('date_this_year')
    purchase_price = factory.Faker('pydecimal', left_digits=6, right_digits=2, positive=True)
    supplier = factory.Faker('company', locale='zh_CN')
    is_active = True


class DeviceCategoryModelTest(TestCase):
    """设备分类模型测试"""
    
    def test_create_category(self):
        """测试创建设备分类"""
        category = DeviceCategoryFactory()
        self.assertTrue(isinstance(category.id, uuid.UUID))
        self.assertTrue(category.is_active)
        self.assertIsNotNone(category.created_at)
        self.assertIsNotNone(category.updated_at)
    
    def test_category_str(self):
        """测试分类字符串表示"""
        category = DeviceCategoryFactory(name="测试分类")
        self.assertEqual(str(category), "测试分类")
    
    def test_category_unique_constraints(self):
        """测试分类唯一约束"""
        category1 = DeviceCategoryFactory(code="TEST001")
        
        with self.assertRaises(Exception):
            DeviceCategoryFactory(code="TEST001")


class DeviceModelTest(TestCase):
    """设备模型测试"""
    
    def test_create_device(self):
        """测试创建设备"""
        device = DeviceFactory()
        self.assertTrue(isinstance(device.id, uuid.UUID))
        self.assertEqual(device.status, 'normal')
        self.assertTrue(device.is_active)
        self.assertIsNotNone(device.created_at)
    
    def test_device_str(self):
        """测试设备字符串表示"""
        device = DeviceFactory(name="测试设备")
        self.assertEqual(str(device), "测试设备")
    
    def test_device_asset_number_unique(self):
        """测试资产编号唯一性"""
        device1 = DeviceFactory(asset_number="ASSET001")
        
        with self.assertRaises(Exception):
            DeviceFactory(asset_number="ASSET001")
    
    def test_device_warranty_status(self):
        """测试设备保修状态"""
        from datetime import date, timedelta
        
        # 测试保修中
        device = DeviceFactory(
            warranty_start_date=date.today() - timedelta(days=30),
            warranty_end_date=date.today() + timedelta(days=30)
        )
        self.assertTrue(device.is_under_warranty())
        
        # 测试保修过期
        device_expired = DeviceFactory(
            warranty_start_date=date.today() - timedelta(days=60),
            warranty_end_date=date.today() - timedelta(days=30)
        )
        self.assertFalse(device_expired.is_under_warranty())


class DeviceAPITest(BaseAPITestCase, TestDataMixin, PerformanceTestMixin, SecurityTestMixin):
    """设备API测试"""
    
    def setUp(self):
        super().setUp()
        self.category = DeviceCategoryFactory()
        self.manufacturer = DeviceManufacturerFactory()
        self.location = DeviceLocationFactory()
        self.device_model = DeviceModelFactory(
            category=self.category,
            manufacturer=self.manufacturer
        )
        self.device = DeviceFactory(
            device_model=self.device_model,
            location=self.location
        )
    
    def test_device_list_api(self):
        """测试设备列表API"""
        self.authenticate()
        url = reverse('devices:device-list')
        response = self.client.get(url)

        data = self.assert_response_success(response)
        self.assertIn('results', data)
        self.assertGreaterEqual(data['count'], 1)

    def test_device_detail_api(self):
        """测试设备详情API"""
        self.authenticate()
        url = reverse('devices:device-detail', kwargs={'pk': self.device.id})
        response = self.client.get(url)

        data = self.assert_response_success(response)
        self.assertEqual(data['id'], str(self.device.id))
        self.assertEqual(data['name'], self.device.name)

    def test_device_create_api(self):
        """测试创建设备API"""
        self.authenticate()
        url = reverse('devices:device-list')
        
        device_data = {
            'asset_number': 'TEST001',
            'serial_number': 'SN001',
            'name': '测试设备',
            'device_model': str(self.device_model.id),
            'location': str(self.location.id),
            'status': 'normal',
            'purchase_price': '10000.00',
            'supplier': '测试供应商'
        }
        
        response = self.client.post(url, device_data, format='json')
        data = self.assert_response_success(response, status.HTTP_201_CREATED)
        
        self.assertEqual(data['name'], '测试设备')
        self.assertEqual(data['asset_number'], 'TEST001')
    
    def test_device_update_api(self):
        """测试更新设备API"""
        self.authenticate()
        url = reverse('devices:device-detail', kwargs={'pk': self.device.id})

        update_data = {
            'name': '更新后的设备名称',
            'status': 'maintenance'
        }

        response = self.client.patch(url, update_data, format='json')
        data = self.assert_response_success(response)

        self.assertEqual(data['name'], '更新后的设备名称')
        self.assertEqual(data['status'], 'maintenance')

    def test_device_delete_api(self):
        """测试删除设备API"""
        self.authenticate()
        device = DeviceFactory()
        url = reverse('devices:device-detail', kwargs={'pk': device.id})

        response = self.client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        # 验证设备已被删除
        device.refresh_from_db()
        self.assertFalse(device.is_active)
    
    def test_device_search_api(self):
        """测试设备搜索API"""
        self.authenticate()
        
        # 创建特定名称的设备
        search_device = DeviceFactory(name="特殊设备名称")
        
        url = reverse('devices:device-list')
        response = self.client.get(url, {'search': '特殊设备'})

        data = self.assert_response_success(response)
        self.assertGreaterEqual(data['count'], 1)

        # 验证搜索结果包含目标设备
        device_ids = [item['id'] for item in data['results']]
        self.assertIn(str(search_device.id), device_ids)

    def test_device_filter_by_status(self):
        """测试按状态过滤设备"""
        self.authenticate()

        # 创建不同状态的设备
        DeviceFactory(status='normal')
        DeviceFactory(status='maintenance')

        url = reverse('devices:device-list')
        response = self.client.get(url, {'status': 'normal'})

        data = self.assert_response_success(response)

        # 验证只返回正常状态的设备
        for device in data['results']:
            self.assertEqual(device['status'], 'normal')

    def test_device_unauthorized_access(self):
        """测试未授权访问设备API"""
        url = reverse('devices:device-list')
        self.test_unauthorized_access(url, 'GET')
        self.test_unauthorized_access(url, 'POST')
    
    def test_device_performance(self):
        """测试设备API性能"""
        self.authenticate()
        
        # 创建多个设备
        DeviceFactory.create_batch(20)
        
        url = reverse('devices:device-list')

        def get_device_list():
            return self.client.get(url)

        # 测试查询次数不超过10次
        response = self.measure_query_count(get_device_list, max_queries=10)
        self.assert_response_success(response)


class DeviceCategoryAPITest(BaseAPITestCase):
    """设备分类API测试"""

    def setUp(self):
        super().setUp()
        self.category = DeviceCategoryFactory()

    def test_category_list_api(self):
        """测试分类列表API"""
        self.authenticate()
        url = reverse('devices:devicecategory-list')
        response = self.client.get(url)

        data = self.assert_response_success(response)
        self.assertGreaterEqual(data['count'], 1)

    def test_category_create_api(self):
        """测试创建分类API"""
        self.authenticate()
        url = reverse('devices:devicecategory-list')

        category_data = {
            'name': '新分类',
            'code': 'NEW001',
            'description': '新分类描述'
        }

        response = self.client.post(url, category_data, format='json')
        data = self.assert_response_success(response, status.HTTP_201_CREATED)

        self.assertEqual(data['name'], '新分类')
        self.assertEqual(data['code'], 'NEW001')


class DeviceLocationAPITest(BaseAPITestCase):
    """设备位置API测试"""

    def setUp(self):
        super().setUp()
        self.location = DeviceLocationFactory()

    def test_location_list_api(self):
        """测试位置列表API"""
        self.authenticate()
        url = reverse('devices:devicelocation-list')
        response = self.client.get(url)

        data = self.assert_response_success(response)
        self.assertGreaterEqual(data['count'], 1)

    def test_location_hierarchy(self):
        """测试位置层级结构"""
        # 创建父子位置关系
        parent_location = DeviceLocationFactory(name="主楼", location_type="building")
        child_location = DeviceLocationFactory(
            name="一楼",
            location_type="floor",
            parent=parent_location
        )

        self.authenticate()
        url = reverse('devices:devicelocation-detail', kwargs={'pk': child_location.id})
        response = self.client.get(url)

        data = self.assert_response_success(response)
        self.assertEqual(data['parent'], str(parent_location.id))
