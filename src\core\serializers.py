"""
核心序列化器
"""
from rest_framework import serializers
from django.contrib.auth import get_user_model

User = get_user_model()


class BaseModelSerializer(serializers.ModelSerializer):
    """基础模型序列化器"""
    created_by_name = serializers.Char<PERSON>ield(source='created_by.username', read_only=True)
    updated_by_name = serializers.CharField(source='updated_by.username', read_only=True)
    
    class Meta:
        fields = [
            'id', 'created_at', 'updated_at', 'is_active',
            'created_by', 'created_by_name', 'updated_by', 'updated_by_name'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'created_by', 'updated_by']


class CodeNameSerializer(BaseModelSerializer):
    """编码名称序列化器"""
    
    class Meta(BaseModelSerializer.Meta):
        fields = BaseModelSerializer.Meta.fields + [
            'code', 'name', 'description', 'sort_order'
        ]


class TreeModelSerializer(CodeNameSerializer):
    """树形模型序列化器"""
    parent_name = serializers.Char<PERSON>ield(source='parent.name', read_only=True)
    children_count = serializers.SerializerMethodField()
    
    class Meta(CodeNameSerializer.Meta):
        fields = CodeNameSerializer.Meta.fields + [
            'parent', 'parent_name', 'level', 'path', 'children_count'
        ]
        read_only_fields = CodeNameSerializer.Meta.read_only_fields + ['level', 'path']
    
    def get_children_count(self, obj):
        """获取子节点数量"""
        return obj.children.filter(is_active=True).count()


class UserSimpleSerializer(serializers.ModelSerializer):
    """用户简单序列化器"""
    full_name = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = ['id', 'username', 'first_name', 'last_name', 'full_name', 'email']
        read_only_fields = ['id']
    
    def get_full_name(self, obj):
        """获取全名"""
        return f"{obj.last_name}{obj.first_name}" if obj.last_name and obj.first_name else obj.username


class PaginationSerializer(serializers.Serializer):
    """分页序列化器"""
    count = serializers.IntegerField(help_text="总记录数")
    next = serializers.URLField(allow_null=True, help_text="下一页链接")
    previous = serializers.URLField(allow_null=True, help_text="上一页链接")
    results = serializers.ListField(help_text="结果列表")


class BulkOperationSerializer(serializers.Serializer):
    """批量操作序列化器"""
    ids = serializers.ListField(
        child=serializers.UUIDField(),
        help_text="对象ID列表"
    )
    action = serializers.ChoiceField(
        choices=[
            ('delete', '删除'),
            ('activate', '激活'),
            ('deactivate', '停用'),
        ],
        help_text="操作类型"
    )


class FileUploadSerializer(serializers.Serializer):
    """文件上传序列化器"""
    file = serializers.FileField(help_text="上传的文件")
    description = serializers.CharField(
        max_length=500,
        required=False,
        help_text="文件描述"
    )


class ExportSerializer(serializers.Serializer):
    """导出序列化器"""
    format = serializers.ChoiceField(
        choices=[
            ('excel', 'Excel'),
            ('csv', 'CSV'),
            ('pdf', 'PDF'),
        ],
        default='excel',
        help_text="导出格式"
    )
    fields = serializers.ListField(
        child=serializers.CharField(),
        required=False,
        help_text="导出字段列表"
    )
    filters = serializers.DictField(
        required=False,
        help_text="过滤条件"
    )


class ImportSerializer(serializers.Serializer):
    """导入序列化器"""
    file = serializers.FileField(help_text="导入文件")
    mode = serializers.ChoiceField(
        choices=[
            ('create', '仅创建'),
            ('update', '仅更新'),
            ('create_or_update', '创建或更新'),
        ],
        default='create_or_update',
        help_text="导入模式"
    )
    skip_errors = serializers.BooleanField(
        default=False,
        help_text="是否跳过错误行"
    )


class SearchSerializer(serializers.Serializer):
    """搜索序列化器"""
    query = serializers.CharField(
        max_length=200,
        help_text="搜索关键词"
    )
    fields = serializers.ListField(
        child=serializers.CharField(),
        required=False,
        help_text="搜索字段列表"
    )
    filters = serializers.DictField(
        required=False,
        help_text="过滤条件"
    )


class StatisticsSerializer(serializers.Serializer):
    """统计序列化器"""
    total = serializers.IntegerField(help_text="总数")
    active = serializers.IntegerField(help_text="激活数")
    inactive = serializers.IntegerField(help_text="停用数")
    created_today = serializers.IntegerField(help_text="今日新增")
    created_this_week = serializers.IntegerField(help_text="本周新增")
    created_this_month = serializers.IntegerField(help_text="本月新增")


class ValidationErrorSerializer(serializers.Serializer):
    """验证错误序列化器"""
    field = serializers.CharField(help_text="字段名")
    message = serializers.CharField(help_text="错误信息")
    code = serializers.CharField(help_text="错误代码")


class ResponseSerializer(serializers.Serializer):
    """通用响应序列化器"""
    success = serializers.BooleanField(help_text="是否成功")
    message = serializers.CharField(help_text="响应消息")
    data = serializers.JSONField(required=False, help_text="响应数据")
    errors = serializers.ListField(
        child=ValidationErrorSerializer(),
        required=False,
        help_text="错误列表"
    )


class DynamicFieldsModelSerializer(serializers.ModelSerializer):
    """动态字段模型序列化器"""
    
    def __init__(self, *args, **kwargs):
        # 从kwargs中提取fields参数
        fields = kwargs.pop('fields', None)
        exclude = kwargs.pop('exclude', None)
        
        super().__init__(*args, **kwargs)
        
        if fields is not None:
            # 只保留指定的字段
            allowed = set(fields)
            existing = set(self.fields)
            for field_name in existing - allowed:
                self.fields.pop(field_name)
        
        if exclude is not None:
            # 排除指定的字段
            for field_name in exclude:
                self.fields.pop(field_name, None)


class NestedCreateMixin:
    """嵌套创建混入类"""
    
    def create(self, validated_data):
        """处理嵌套对象的创建"""
        nested_fields = getattr(self.Meta, 'nested_fields', {})
        nested_data = {}
        
        # 提取嵌套字段数据
        for field_name, serializer_class in nested_fields.items():
            if field_name in validated_data:
                nested_data[field_name] = validated_data.pop(field_name)
        
        # 创建主对象
        instance = super().create(validated_data)
        
        # 创建嵌套对象
        for field_name, data in nested_data.items():
            serializer_class = nested_fields[field_name]
            if isinstance(data, list):
                # 一对多关系
                for item_data in data:
                    item_data[self.Meta.model.__name__.lower()] = instance
                    serializer = serializer_class(data=item_data)
                    serializer.is_valid(raise_exception=True)
                    serializer.save()
            else:
                # 一对一关系
                data[self.Meta.model.__name__.lower()] = instance
                serializer = serializer_class(data=data)
                serializer.is_valid(raise_exception=True)
                serializer.save()
        
        return instance
